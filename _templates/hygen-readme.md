# Commands
- `hygen controller new <service> --r <routeName>`:
 - Creates a route in: `/api/controllers/service/route-name.js`
 - Create an empty util file : `/api/utils/service/route-name.util.js`
- `hygen util new <service> --r <routeName>`:
 - Create an empty util file : `/api/utils/service/route-name.util.js`
- `hygen service database <service>`:
 - Creates a default database service template in `/api/services/<service>/<service>.private.js`
 - Creates a default database service template in`/api/services/<service>/<service>.public.js`
 - Creates a default database service template in`/api/services/<service>/<service>.servie.js`

# Links
Links to help modify the Hygen templates.
- [Hygen Docs](https://www.hygen.io/templates)
- [Hygen Changecase Support](https://github.com/blakeembrey/change-case)
