---
to: api/controllers/<%= name %>/<%= h.changeCase.paramCase(r) %>.js
---
<%
routeName = h.changeCase.camelCase(r)
fileName = h.changeCase.paramCase(r)
%>
const <%= name %>Service = require('../../services/<%= name %>/<%= name %>.service');
const globalHelper = require('../../utils/globalhelper');
const selfUtils = require('../../utils/<%= name %>/<%= fileName %>.util.js');
const utils = require('../../utils/<%= name %>/utils.js');

module.exports = {
  friendlyName: '<%= routeName %>',
  description : '',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[<%= name %> > <%= routeName %>] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[<%= name %> > <%= routeName %>] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[<%= name %> > <%= routeName %>] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      const {
        _userMeta: { _site: siteId },
      } = inputs;

      return exits.success();
    } catch(error) {
      sails.log.error('[<%= name %> > <%= routeName %>] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
