---
to: api/utils/<%= name %>/<%= h.changeCase.paramCase(r) %>.util.js
---
<%
routeName = h.changeCase.camelCase(r)
fileName = h.changeCase.paramCase(r)
%>
const globalHelper = require('../globalhelper'); 

module.exports = {
  checkInput: function(body){
    let errors = globalHelper.checkObjectKeys(body, [], '');
    if (errors.length === 0) return { "status": true }
    else return {
      "status": false,
      errors
    }
  },
};