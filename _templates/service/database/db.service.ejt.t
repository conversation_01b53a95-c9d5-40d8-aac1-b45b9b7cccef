---
to: api/services/<%= h.changeCase.camelCase(name) %>/<%= h.changeCase.camelCase(name) %>.service.js
---
<%
serviceName = h.changeCase.camelCase(name)
%>
/* eslint-disable no-undef */
const <%= serviceName %> = require('./<%= serviceName %>.private');
module.exports = {
  create: <%= serviceName %>.create,
  find: <%= serviceName %>.find,
  findOne: <%= serviceName %>.findOne,
  update: <%= serviceName %>.update,
  delete: <%= serviceName %>.delete,
};
