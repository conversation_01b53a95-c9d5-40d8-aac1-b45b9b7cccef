---
to: api/services/<%= h.changeCase.camelCase(name) %>/<%= h.changeCase.camelCase(name) %>.private.js
---
/* eslint-disable no-undef */
module.exports = {
<%
serviceName = h.changeCase.pascalCase(name)
%>
  /**
   * <%= serviceName %> module private functions
   */
  create: async (params) => {
    return <%= serviceName %>.create(params);
  },
  find: async (searchParams) => {
    // <%= serviceName %>.find
    return <%= serviceName %>.find(searchParams);
  },
  findOne: async (searchParams) => {
    // <%= serviceName %>.findone
    let <%= name %>s = await <%= serviceName %>.find(searchParams).limit(1);
    return <%= name %>s[0];
  },
  update: async (searchParams, updateValue) =>{
    return <%= serviceName %>.update(searchParams, updateValue);
  },
  delete: async (searchParams) => {
    return <%= serviceName %>.destroy(searchParams);
  },
};
