---
to: api/services/<%= h.changeCase.camelCase(name) %>/<%= h.changeCase.camelCase(name) %>.public.js
---
<%
serviceName = h.changeCase.camelCase(name)
%>
/* eslint-disable no-undef */
<%
serviceName = h.changeCase.camelCase(name)
%>
const <%= serviceName %> = require('./<%= serviceName %>.service');

module.exports = {
  findOne: <%= serviceName %>.findOne,
  find: <%= serviceName %>.find,
  create: <%= serviceName %>.create
};
