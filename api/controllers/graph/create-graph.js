
const graphService = require('../../services/graph/graph.service');
const selfUtils = require('../../utils/graph/save-graph.util.js');

module.exports = {
    friendlyName: 'createGraph',
    description: 'To save graphs from frontend',
    example: [
        `curl -X POST -H "Content-Type: application/json" --data \' {
      "global": true,
      "requestPayload": {group:'hr',devices:[{deviceId:'2200',params:['alarmhistory','outputfrequency']}],deviceType:['ahu','chiller']},
      "folderName": "chiller graphs",
      "graphType": "line",
    }\' 0:1337/m2/recipe/v2/template`,
    ],

    inputs: {
        global: {
            type: 'boolean',
            description: 'if the graph is user specific or site',
            example: true,
            required: true,
        },
        requestPayload: {
            type: 'json',
            example: "{group:'hr',devices:[{deviceId:'2200',params:['alarmhistory','outputfrequency']}],deviceType:['ahu','chiller']}",
            required: true
        },
        folderName: {
            type: 'string',
            example: 'chilller graphs',
            required: true
        },
        name: {
            type: 'string',
            example: 'chilller circuit graph',
            required: true
        },
        graphType: {
            type: 'string',
            example: 'line',
            required: true
        },
        _userMeta: {
            type: 'json',
            required: true,
            example: { id: 'userId', _role: 'role', _site: 'siteId' },
        },
    },

    exits: {
        serverError: {
            responseType: 'serverError',
            description: '[graph > save graph] Server Error!',
        },
        badRequest: {
            responseType: 'badRequest',
            description: '[graph > graph save Bad Request!',
        },
        forbidden: {
            responseType: 'forbidden',
            description: '[graph > graph save forbidden Request!',
        },
    },

    fn: async function (inputs, exits) {
        try {
            sails.log('inputs: ', inputs);
            const graphObject = selfUtils.buildInitialPacket(inputs);
            sails.log('graphObject: ', graphObject);
            await graphService.create(graphObject);
            return exits.success(graphObject);

        } catch (error) {
            sails.log.error('[graph -> save graph Error!');
            sails.log.error(error);
            exits.serverError(error);
        }
    }
};
