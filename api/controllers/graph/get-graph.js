
const graphService = require('../../services/graph/graph.service');
const saveGraphUtil = require('../../utils/graph/save-graph.util.js');
const selfUtils = require('../../utils/graph/save-graph.util.js');

module.exports = {
    friendlyName: 'findGraphs',
    description: 'Find graph with various filters like user and global',
    example: [
        `curl "0:1337/m2/graph/v2//user"`,
    ],

    inputs: {
        _userMeta: {
            type: {},
            example: { 'id': 'userName' },
            required: false // this fields auto add authentication
        },
        id: {
            type: 'string',
            description: 'finding for global or user based graphs',
            example: 'global',
            isIn: ['global', 'user'],
            required: false
        },
    },

    exits: {
        serverError: {
            responseType: 'serverError',
            description: '[graphs > findGraphs] Server Error!',
        },
        badRequest: {
            responseType: 'badRequest',
            description: '[graphs > findGraphs] Bad Request!',
        },
        forbidden: {
            responseType: 'forbidden',
            description: '[graphs > findGraphs] forbidden Request!',
        },
    },

    fn: async function (inputs, exits) {
        try {
            let { _userMeta, id } = inputs;
            const userId = _userMeta.id;
            const siteId = _userMeta._site;
            let primaryKey;
            if (id === 'user') {
                primaryKey = `${userId}_${siteId}`
            } else {
                primaryKey = `${id}_${siteId}`
            }
            const params = {
                'userId_siteId': primaryKey,
            };
            let queryResponse = await graphService.find(params);
            const graphArray = saveGraphUtil.createGraphObject(queryResponse);
            return exits.success(graphArray);

        } catch (error) {
            sails.log.error('[graph > findGraphs] Error!');
            sails.log.error(error);
            exits.serverError(error);
        }
    }
};
