const { fetchDeviceParamLineGraphs } = require('../../services/graph/graph.service.js');
const { validateDeviceParamLineGraph } = require('../../utils/graph/graph.util.js');

module.exports = {
    friendlyName: 'getDeviceParamLineGraph',
    description: 'Get device param line graph',
    example: [
    ],

    inputs: {
        siteId: {
          type: "string",
        },
        startTime: {
          type: "string",
          description: "Start time in date format",
        },
        endTime: {
          type: "string",
          description: "Start time in date format",
        },
        deviceId: {
          type: "json",
          description: "list of device Ids",
        },
        abbr: {
          type: "string",
          description: "parameter name e.g kw,pf,vpp,a_amp,kva,kvar",
        },
    },

    exits: {
        serverError: {
            responseType: 'serverError',
            description: '[graphs > getGraphs] Server Error!',
        },
        badRequest: {
            responseType: 'badRequest',
            description: '[graphs > getGraphs] Bad Request!',
        },
        forbidden: {
            responseType: 'forbidden',
            description: '[graphs > getGraphs] forbidden Request!',
        },
    },

    fn: async function (inputs, exits) {
        try {
          const {
            siteId,
            startTime,
            endTime,
            deviceId,
            abbr
          } = inputs
          validateDeviceParamLineGraph({
            siteId,
            startTime: startTime.trim(),
            endTime: endTime.trim(),
            deviceId,
            abbr
          });
          const lineGraph = await fetchDeviceParamLineGraphs({
            siteId,
            startTime: startTime.trim(),
            endTime: endTime.trim(),
            deviceId,
            abbr
          });
          return exits.success(lineGraph);
        } catch (error) {
            if(error.statusCode == 400) {
              return exits.badRequest({
                err: error.msg
              })
            } else {
              sails.log.error('[graph > getDeviceParamLineGraph] Error!');
              sails.log.error(error);
              return exits.serverError(error);
            }
        }
    }
};
