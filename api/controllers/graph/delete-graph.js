const graphService = require('../../services/graph/graph.service');

module.exports = {
    friendlyName: 'deleteGraph',
    description: '',
    example: [
        `curl -X GET "http://localhost:1337/`,
    ],

    inputs: {
        global: {
            type: 'boolean',
            example: true,
            required: true
        },
        graphId: {
            type: 'string',
            example: '3410ee75-af92-45b5-b3c1-574d726a3980',
            description: 'Unique graph ID of the graph',
            required: true
        },
        _userMeta: {
            type: {},
            example: { 'id': 'userName' },
            required: false // this fields auto add authentication
        },
    },

    exits: {
        serverError: {
            responseType: 'serverError',
            description: '[graph > deleteGraph] Server Error!',
        },
        badRequest: {
            responseType: 'badRequest',
            description: '[graph > deleteGraph] Bad Request!',
        },
        forbidden: {
            responseType: 'forbidden',
            description: '[graph > deleteGraph] forbidden Request!',
        },
    },

    fn: async function (inputs, exits) {
        try {
            let { global, graphId, _userMeta } = inputs;
            let key
            if (global) {
                key = `global_${_userMeta._site}`;
            } else {
                key = `${_userMeta.id}_${_userMeta._site}`
            }
            let graph = await graphService.findOne({ userId_siteId: key, graphId });
            if (!graph) {
                return exits.badRequest({ problems: ['graph not found'] });
            }
            await graphService.delete({ userId_siteId: key, graphId });
            return exits.success({ 'status': 'ok' });
        } catch (error) {
            sails.log.error('[graph > deleteGraph] Error!');
            sails.log.error(error);
            exits.serverError(error);
        }
    }
};
