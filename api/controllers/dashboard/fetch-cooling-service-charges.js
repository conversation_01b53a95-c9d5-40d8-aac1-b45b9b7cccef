const dashboardService = require('../../services/dashboard/dashboard.service');
module.exports = {
  friendlyName: 'fetch-cooling-service-charges',
  description: 'Fetch cooling service charges',
  example: [
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      description: 'Auto attached by isAuthorized policy.',
      required: true,
    },
    siteId: {
      type: 'string'
    }
  },
  exits: {
    serverError: {
      description: 'Failed to fetched cooling service charges',
      responseType: 'serverError',
      statusCode: 500,
    },
  },
  async fn(inputs, exits) {
    try {
      const  {siteId} = inputs;
      const dashboardResponse = await dashboardService.fetchSheratonCooling(siteId);  
      return exits.success(dashboardResponse);
    } catch (error) {
      sails.log.error('[dashboard > fetch-cooling-service-charges] Error!');
      sails.log.error(error);
      return exits.serverError(error);
    }
  },
};
