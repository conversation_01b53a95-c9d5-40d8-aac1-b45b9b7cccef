const dashboardService = require('../../services/dashboard/dashboard.service')
module.exports = {
  friendlyName: "smyras-energy-consumption-savings",
  description: "Energy consumption savings",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
      description:
        "User meta information added by default to authenticated routes",
    },
    siteId: {
      type: "string",
    }
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[dashboard > smyras-energy-consumption-savings] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[dashboard > smyras-energy-consumption-savings] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[dashboard > smyras-energy-consumption-savings] forbidden Request!",
    },
    notFound: {
      responseType: "notFound",
      description: "[dashboard > smyras-energy-consumption-savings] notFound Request!",
    },
  },
  async fn(inputs, exits) {
    try {
      const { siteId } = inputs;
      if (siteId != 'smyras-yog') return exits.notFound({
        err: 'Site is not applicable.Please enter valid siteID'
      })
      const energySavings = await dashboardService.fetchEnergyConsumptionSavings(siteId)
      return exits.success(energySavings);
    } catch (error) {
      sails.log.error("[dashboard > smyras-energy-with-tr-cost] Error!");
      sails.log.error(error);
      exits.serverError(error);
    }
  },
};
