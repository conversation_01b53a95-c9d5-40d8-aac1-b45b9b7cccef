const dashboardService = require('../../services/dashboard/dashboard.service');
module.exports = {
  friendlyName: "fetchPlantRunHourWithTRMtd",
  description: "",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
      description:
        "User meta information added by default to authenticated routes",
    },
    siteId: {
      type: 'string',
      description: 'Please provide site ID',
    }
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[dashboard > fetchPlantRunHourWithTRMtd] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[dashboard > fetchPlantRunHourWithTRMtd] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[dashboard > fetchPlantRunHourWithTRMtd] forbidden Request!",
    },
    notFound: {
      responseType: 'notFound',
      description: '[dashboard > fetchPlantRunHourWithTRMtd] Not Found'
    }
  },
  async fn(inputs, exits) {
    try {
      const { siteId } = inputs;
      if (siteId != 'smyras-yog') return exits.notFound({
        err: 'Site is not applicable.Please enter valid siteID'
      })
      const response = await dashboardService.fetchPlantTRWithMTD(siteId);
      return exits.success(response);
    } catch (error) {
      sails.log.error("[dashboard > fetchPlantRunHourWithTRMtd] Error!");
      sails.log.error(error);
      return exits.serverError(error);
    }
  },
};