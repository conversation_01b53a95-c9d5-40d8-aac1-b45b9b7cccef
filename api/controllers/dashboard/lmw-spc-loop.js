const moment = require('moment-timezone');
const LMWSPCService = require('../../services/dashboard/lmw-spc.service');
const LMWSPCUtils = require('../../utils/dashboard/lmw-spc.utils');

moment.tz.add(
  'Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6',
);
moment.tz.setDefault('Asia/Kolkata');

module.exports = {
  friendlyName: 'findSpecificPowerConsumption',
  description: 'to get specific power consumption for long time period',
  example: [
    'curl --location --request GET \'localhost:1337/m2/dashboard/getSpc/time --header \'Authorization: Bearer token\'',
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      description: 'Auto attached by isAuthorized policy.',
      required: true,
    },
  },
  exits: {
    serverError: {
      description: 'Failed to fetched drivers',
      responseType: 'serverError',
      statusCode: 500,
    },
  },
  fn: async function (inputs, exits) {
    try {
      // DateTime
      const endDateTime = moment().format();
      /**
       * Subtract 1 day from the current time
       * to take the data of 1 hour
       */
      let startDateTime = moment().subtract(1, 'days');
      //  to check from starting time to 3 mint after the starting time
      let threeMintFromStartDateTime = moment(startDateTime).add(3, 'minutes');

      const SPCIntervalData = [];
      /**
       * Executing till 1 hour by incrementing with a minute
       */
      while (startDateTime.format() < endDateTime) {
        /**
         * To set the timestamp for the dateTime
         */
        const startTimeTimestamp = startDateTime.format('YYYY-MM-DDTHH:mm:ssZ');
        const threeMintFromStartDateTimeTimestamp = threeMintFromStartDateTime.format('YYYY-MM-DDTHH:mm:ssZ');
        try {
          // eslint-disable-next-line max-len
          const influxData = await LMWSPCService.getDataFromInflux(startTimeTimestamp, threeMintFromStartDateTimeTimestamp);
          // eslint-disable-next-line max-len
          const filterObjectForTimestamp = await LMWSPCUtils.filterObjectForTimestamp(influxData, threeMintFromStartDateTimeTimestamp);
          SPCIntervalData.push(filterObjectForTimestamp);
        } catch (e) {
          sails.log.error('[dashboard > findSpecificPowerConsumption for long period of time]');
          sails.log.error(e);
        }
        /**
         * Increment by a minute.
         * */
        threeMintFromStartDateTime = threeMintFromStartDateTime.add(1, 'minute');
        startDateTime = startDateTime.add(1, 'minute');
      }
      /**
       * TODO: Generate Excel and give list of deviceIds in params
       * @description: get the buffer of data in excel
       */
      const excelBufferData = await LMWSPCUtils.generateExcel(SPCIntervalData);
      this.res.setHeader('Content-disposition', 'attachment;');
      this.res.contentType('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      return this.res.send(excelBufferData);
    } catch (error) {
      sails.log.error('[dashboard > findSpecificPowerConsumption] Error!');
      sails.log.error(error);
      return exits.serverError(error);
    }
  },
};
