const baseline = require('../../services/dashboard/dashboard.service')

module.exports = {
  friendlyName: 'fetchSavings',
  description: 'fetchSavings',
  example: [
    'curl --location --request GET \'localhost:1337/m2/site/:siteId/savings --header \'Authorization: Bearer token\'',
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      description: 'Auto attached by isAuthorized policy.',
      required: true,
    },
    siteId: {
      type: 'string',
      description: 'Please provide site ID',
    },
  },
  exits: {
    serverError: {
      description: 'Failed to fetched drivers',
      responseType: 'serverError',
      statusCode: 500,
    },
   conflict: {
    statusCode: 409,
    description: 'Conflict',
   },
   notFound: {
    statusCode: 404,
    description: 'Not Found',
   }
  },
  async fn({_userMeta, siteId}, exits) {
    try {
        if (!siteId) {
          return exits.badRequest({
            err: 'Please provide siteId',
          });
        }

       const { unitPref: unitPreference } = _userMeta;
         {
           /**
            * MOCKING DASHBOARD API FOR IBMS PRESENTATION ONLY. We are using mgch site for demo purposes
            * */
           const sunshineIBMSSites = ['be-hyd', 'suh-hyd'];
           if (sunshineIBMSSites.indexOf(siteId) !== -1) {
             siteId = "mgch"
           }
         }
    

        const fetchBaseline = await baseline.fetchSavings(siteId, unitPreference);
        return exits.success({
          data: fetchBaseline,
          err: null
        });
    } catch (error) {
      if (error.code == 'E_BASELINE_NOT_FOUND') {
        return exits.notFound({
          err: error.message,
        });
      } else if (error.code == 'E_MULTIPLE_BASELINE_EXIST')  {
        return exits.conflict({
          err: error.message,
        });
      }
      sails.log.error('[dashboard > fetchSavings] Error!');
      sails.log.error(error);
      return exits.serverError(error);
    }
  },
};
