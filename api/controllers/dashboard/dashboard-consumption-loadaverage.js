
const dashboardService = require('../../services/dashboard/dashboard.service');
const globalHelper = require('../../utils/globalhelper');
const selfUtils = require('../../utils/dashboard/dashboard-consumption-loadaverage.util.js');
const deviceService = require("../../services/device/device.public");
const energyConsumptionService = require("../../services/energyConsumption/energyConsumption.public");
const routeDataDeviceService = require("../../services/datadevice/dashboard-consumption-loadaverage.service");
// const utils = require('../../utils/dashboard/utils.js');

module.exports = {
  friendlyName: 'dashboardConsumptionLoadaverage',
  description : '',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    siteId: {
      type: 'string',
      example: "mgch",
      description: 'SiteId',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[dashboard > dashboardConsumptionLoadaverage] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[dashboard > dashboardConsumptionLoadaverage] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[dashboard > dashboardConsumptionLoadaverage] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[dashboard > dashboardConsumptionLoadaverage] Not Found!',
    }
  },

  fn: async function (inputs, exits) {
    try{
      const {
        _userMeta: { unitPref: unitPreferences }, siteId
      } = inputs;

      // Fetch Main Meter List
      let emList = await deviceService.fetchMainMetersByScanningSiteConfiguration(siteId);
      if (_.isEmpty(emList)) {
        sails.log.info(`[dashboard > dashboardConsumptionLoadaverage] siteId=${siteId} userId=${inputs._userMeta.id} message="Unable to find mainMeter list for site" mainMeterKey=${siteId}_mainMeter`);
        return exits.badRequest({ message: `Unable to find mainMeter list for site: ${siteId}` });
      }

      // Generate Time Objects
      const timeObjects = selfUtils.generateTimeObjects();

      // Fetch consumption for last hour, today, last week,
      const $lastHourConsumption = energyConsumptionService.fetchConsumptionForMultipleMeters(emList, timeObjects.lastHourTimeObject.startTime, timeObjects.lastHourTimeObject.endTime, unitPreferences.cons);
      const $todayConsumption = energyConsumptionService.fetchConsumptionForMultipleMeters(emList, timeObjects.todayTimeObject.startTime, timeObjects.todayTimeObject.endTime, unitPreferences.cons);
      const $lastWeekConsumption = energyConsumptionService.fetchConsumptionForMultipleMeters(emList, timeObjects.lastWeekTimeObject.startTime, timeObjects.lastWeekTimeObject.endTime, unitPreferences.cons);

      // Fetch last 7 days consumption grouped by day.
      const $dailyConsumption = energyConsumptionService.fetchDailyConsumptionForMultipleMeters(emList, timeObjects.lastWeekTimeObject.startTime, timeObjects.lastWeekTimeObject.endTime, unitPreferences.cons)

      // Fetch current load
      const $currentLoad = routeDataDeviceService.getCurrentLoad(emList);

      // Fetch Load Pattern Grouped by Hour
      const $loadPattern = routeDataDeviceService.getLoadPatternGroupedByHour(emList, timeObjects.loadPatternLastOneDayObject.startTime, timeObjects.loadPatternLastOneDayObject.endTime, siteId);

      // Format result for FE
      const serviceResults = {
        lastHourConsumption: await $lastHourConsumption,
        todayConsumption: await $todayConsumption,
        lastWeekConsumption: await $lastWeekConsumption,
        dailyConsumptionResult: await $dailyConsumption,
        currentLoad: await $currentLoad,
        loadPattern: await $loadPattern
      };
      const responseObject = selfUtils.formatResponseObject(serviceResults, timeObjects);

      return exits.success(responseObject);
    } catch(error) {
      sails.log.error(`[dashboard > dashboardConsumptionLoadaverage] Error!. siteId=${inputs.siteId},userId=${inputs._userMeta.id}`);
      sails.log.error(error);
      switch (error.code) {
        case 'E_SITE_ID_MISSING':
          return exits.badRequest({ message: error.message });
        default:
          return exits.serverError(error);
      }
    }
  }
};
