const dashboardService = require('../../services/dashboard/dashboard.service')
module.exports = {
  friendlyName: "smyras-energy-with-tr-cost",
  description: "Fetch First row of sheraton site dashboard",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
      description:
        "User meta information added by default to authenticated routes",
    },
    siteId: {
      type: "string",
    }
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[dashboard > smyras-energy-with-tr-cost] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[dashboard > smyras-energy-with-tr-cost] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[dashboard > smyras-energy-with-tr-cost] forbidden Request!",
    },
    notFound: {
      responseType: "notFound",
      description: "[dashboard > smyras-energy-with-tr-cost] notFound Request!",
    },
  },
  async fn(inputs, exits) {
    try {
      const { siteId } = inputs;
      if (siteId != 'smyras-yog') return exits.notFound({
        err: 'Site is not applicable.Please enter valid siteID'
      })
      const dashboard = await dashboardService.fetchEnergyConsWithTRCost(siteId)
      return exits.success(dashboard);
    } catch (error) {
      sails.log.error("[dashboard > smyras-energy-with-tr-cost] Error!");
      sails.log.error(error);
      exits.serverError(error);
    }
  },
};
