const moment = require('moment');
const LMWSPCService = require('../../services/dashboard/lmw-spc.service');
const LMWSPCUtils = require('../../utils/dashboard/lmw-spc.utils');

moment.tz.add(
  'Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6',
);
moment.tz.setDefault('Asia/Kolkata');

module.exports = {
  friendlyName: 'findSpecificPowerConsumption',
  description: 'to get specific power consumption',
  example: [
    'curl --location --request GET \'localhost:1337/m2/dashboard/getSPC/time --header \'Authorization: Bearer token\'',
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      description: 'Auto attached by isAuthorized policy.',
      required: true,
    },
  },
  exits: {
    serverError: {
      description: 'Failed to fetched drivers',
      responseType: 'serverError',
      statusCode: 500,
    },
  },
  async fn(inputs, exits) {
    try {
      const startTime = '-3m';
      const endTime = 'now()';
      const influxData = await LMWSPCService.getDataFromInflux(startTime, endTime);
      const filterObjectForTimestamp = await LMWSPCUtils.filterObjectForTimestamp(influxData);
      /**
       * we use response for return rather than filterObjectForTimestamp.
       * Because filterObjectForTimestamp is a reusable function .
       * that is using in another api called : lmw-spc-loop.
       * which is used to create an Excel sheet.
       * */
      const response = {
        specificPowerConsumption: filterObjectForTimestamp.specific_power_consumption,
        airDelivery: filterObjectForTimestamp.air_delivery,
      };
      return exits.success(response);
    } catch (error) {
      sails.log.error('[dashboard > findSpecificPowerConsumption] Error!');
      sails.log.error(error);
      return exits.serverError(error);
    }
  },
};
