const dashboardService = require('../../services/dashboard/dashboard.service')

module.exports = {
  friendlyName: 'dashboard trh pattern',
  description: 'Dashboard trh pattern',
  inputs: {
    siteId: {
      type: 'string',
      description: 'The site ID to fetch the EM list for'
    },
  },
  exits: {
    badRequest: {
      statusCode: 400,
      description: 'Missing or invalid siteId',
    },
    success: {
      statusCode: 200,
      description: 'Successfully fetched EM list',
    },
    serverError: {
      statusCode: 500,
      description: 'Server error occurred',
    },
  },
  fn: async function (inputs, exits) {
    try {
      const { siteId } = inputs;

      if (!siteId) {
        return exits.badRequest({ err: 'Site ID is required' });
      }

      const trhLoadPattern = await dashboardService.getTRHLoadPattern(siteId);
      const BASELINE_TRH_VALUE = 4800;
      const baseLine = trhLoadPattern.map(([times,]) => [times, BASELINE_TRH_VALUE])
      const response =  {
        trhLoadPattern,
        baseLine
      }
      return exits.success(response);
    } catch (e) {
      sails.log.error("[dashboard >> trh-pattern]", e);
      return exits.serverError(e);
    }
  }
}