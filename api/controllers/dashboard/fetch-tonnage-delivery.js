const componentService = require("../../services/component/component.service");
const helper = require("../../utils/globalhelper");
const {
  tonnageDeliveryCardData,
} = require("../../services/dashboard/dashboard.service");
module.exports = {
  friendlyName: "fetchTonnageDelivery",
  description: "",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
      description:
        "User meta information added by default to authenticated routes",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[dashboard > fetchTonnageDelivery] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[dashboard > fetchTonnageDelivery] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[dashboard > fetchTonnageDelivery] forbidden Request!",
    },
  },

  // eslint-disable-next-line consistent-return
  async fn(inputs, exits) {
    try {
      const {
        _userMeta: { _site: siteId },
      } = inputs;
      const tonnageData = await tonnageDeliveryCardData(siteId);
      const response = {
        tr: "NA",
        chleff: "NA",
      };
      if (!_.isEmpty(tonnageData.tptr)) {
        response.tr = helper.returnFilteredNumber(tonnageData.tptr);
      }
      if (!_.isEmpty(tonnageData.chleff)) {
        response.chleff = tonnageData.chleff;
      }
      return exits.success({ data: response });
    } catch (error) {
      if(error.code === "E_SITE_ID_MISSING"){
        exits.badRequest({err:"site id is missing"})

      }
      sails.log.error("[dashboard > fetchTonnageDelivery] Error!");
      sails.log.error(error);
      exits.serverError(error);
    }
  },
};
