const  ProductionData = require('../../services/productionData/productionData.service')
const InfluxService = require('../../services/influx/influx.service')
module.exports = {
  friendlyName: "deleteProductionData",
  description: "rest api to delete production data",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
      description:
        "User meta information added by default to authenticated routes",
    },
    production_id:{
      type:"string",
      description:"e.g 20220201#Shift-01:00-01:00#Product-Liquid Metal",
      required:true
    },
  },
  exits: {
    serverError: {
      responseType: "serverError",
      description:
        "[productionData > deleteProductionData] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description:
        "[productionData > deleteProductionData] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description:
        "[productionData > deleteProductionData] forbidden Request!",
    },
    duplicateProductName:{
      statusCode:400,
      description: ""
    }
  },
  fn: async function deleteProductionData(inputs, exits) {
    try {
      const { _userMeta, production_id } = inputs;
      await ProductionData.delete({
        siteId:_userMeta._site,
        productionId: production_id
      })
      return exits.success({ status:"done" });
    } catch (err) {
      return exits.serverError(err);
    }
  },
};
