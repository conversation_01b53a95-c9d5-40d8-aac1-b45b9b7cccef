const  ProductionData = require('../../services/productionData/productionData.service')
const InfluxService = require('../../services/influx/influx.service')
const globalhelper = require('../../utils/globalhelper');
const moment = require('moment');
module.exports = {
  friendlyName: "getAvailableDays",
  description: "this will return an array of days when data was available for any shift and product between given time range",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
      description:
        "User meta information added by default to authenticated routes",
    },
    start_date:{
      type:"string",
      description:"",
      required:true,
      custom:globalhelper.isValidDateTime

    },
    end_date: {
      type:"string",
      description:"",
      required:true,
      custom:globalhelper.isValidDateTime
    },
  },
  exits: {
    serverError: {
      responseType: "serverError",
      description:
        "[productionData > getAvailableDays] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description:
        "[productionData > getAvailableDays] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description:
        "[productionData > getAvailableDays] forbidden Request!",
    },
  },
  fn: async function getAvailableDays(inputs, exits) {
    try {
      const { _userMeta, start_date, end_date } = inputs
      let _start_date = moment(start_date).format('YYYYMMDD');
      let _end_date = moment(end_date).add(1,'d').format('YYYYMMDD');
      const productionData = await ProductionData.daysListOfAvailableProductionData(_userMeta._site, _start_date, _end_date);
      return exits.success(productionData);
    } catch (err) {
      return exits.serverError(err);
    }
  },
};
