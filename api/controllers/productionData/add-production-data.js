const  ProductionData = require('../../services/productionData/productionData.service')
const InfluxService = require('../../services/influx/influx.service')
const moment = require('moment');
module.exports = {
  friendlyName: "addProduct",
  description: "to add new product",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
      description:
        "User meta information added by default to authenticated routes",
    },
    production_value:{
      type:"number",
      description:"",
      required:true
    },
    product_id: {
      type:"string",
      description:"",
      required:true
    },
    shift_id: {
      type:"string",
      description:"",
      required:true
    },
    production_date:{
      type:"string",
      description:"",
      required:true
    },
    production_unit:{
      type:"string",
      description:"",
      required:true
    }
  },
  exits: {
    serverError: {
      responseType: "serverError",
      description:
        "[shift > addShift] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description:
        "[shift > addShift] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description:
        "[shift > addShift] forbidden Request!",
    },
    duplicateProductName:{
      statusCode:400,
      description: ""
    }
  },
  fn: async function addProductionData(inputs, exits) {
    try {
      const { _userMeta, production_value, production_date, shift_id, product_id, production_unit } = inputs
      if(!moment(production_date).isValid()) return exits.badRequest({problems:[`invalid date format of production date. it should be YYYY-MM-DD format`]})
      //add a check that user can not add the future production data
      const _productionDataRecord = await ProductionData.create({
        siteId: _userMeta._site,
        productId: product_id,
        shiftId: shift_id,
        production_date: production_date,
        production_value: production_value,
        production_unit: production_unit,
        created_by: _userMeta.id
      })
      if(!_productionDataRecord) return exits.duplicateProductName({problems:[`DUPLICATE_PRODUCTION_DATA`]})
      await InfluxService.write({
        data:{
          tags:[{ key:'product_id', value:product_id }, { key: 'shift_id',value: shift_id}],
          fields:[{
            type:'float',
            key:'production_value',
            value:production_value

          }],
          timestamp: moment(production_date).startOf('days').utcOffset(330).endOf().unix()
        },
        bucket:_userMeta._site,
        measurement:'production_data'
      })
      return exits.success(_productionDataRecord);
    } catch (err) {
      return exits.serverError(err);
    }
  },
};
