const  ProductionData = require('../../services/productionData/productionData.service')
const selfUtil = require('../../utils/productionData/add-production-batch-data.util');
module.exports = {
  friendlyName: "addProductionBatchData",
  description: "to  ",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
      description:
        "User meta information added by default to authenticated routes",
    },
    records:{
      type:"json",
      description:"",
      required:true,
    },
  },
  exits: {
    serverError: {
      responseType: "serverError",
      description:
        "[shift > addShift] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description:
        "[shift > addShift] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description:
        "[shift > addShift] forbidden Request!",
    },
    duplicateProductName:{
      statusCode:400,
      description: ""
    }
  },
  fn: async function addProductionData({ _userMeta, records}, exits) {
    try {
      const anyValidationError = selfUtil.validateInputJson(records)
      if(anyValidationError) {
        return exits.badRequest({ problems: anyValidationError });
      }
      const result = await ProductionData.addBulkProductionData(_userMeta._site,records,_userMeta.id);
      return exits.success(result);
    } catch (err) {
      if(err.code === 'E_RECORD_VALIDATION_FAIL'){
        return exits.badRequest({problems:JSON.parse(err.message)});
      }
      return exits.serverError(err);
    }
  },
};
