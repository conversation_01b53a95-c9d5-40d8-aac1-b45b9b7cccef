const ProductionData = require('../../services/productionData/productionData.service')
const ShiftService = require('../../services/shift/shift.public')
const ProductService = require('../../services/product/product.public')
const globalhelper = require('../../utils/globalhelper');
const selfUtil = require('../../utils/productionData/utils')

const moment = require('moment');
module.exports = {
  friendlyName: "getProductionData",
  description: "This will return production page data w.r.t data, shift and product wise",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
      description:
        "User meta information added by default to authenticated routes",
    },
    start_date:{
      type:"string",
      description:"",
      required:true,
      custom:globalhelper.isValidDateTime

    },
    end_date: {
      type:"string",
      description:"",
      required:true,
      custom:globalhelper.isValidDateTime
    },
  },
  exits: {
    serverError: {
      responseType: "serverError",
      description:
        "[productionData > getProductionData] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description:
        "[productionData > getProductionData] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description:
        "[productionData > getProductionData] forbidden Request!",
    },
  },
  fn: async function getAvailableDays(inputs, exits) {
    try {
      const { _userMeta, start_date, end_date } = inputs
      let _start_date = moment(start_date).format('YYYYMMDD');
      let _end_date = moment(end_date).add(1,'d').format('YYYYMMDD');
      const _shifts =  ShiftService.getShiftBySite(_userMeta._site);
      const _products =  ProductService.getProductBySite(_userMeta._site)
      const _productionData =  ProductionData.getProductionData(_userMeta._site, _start_date, _end_date);
      const _record = await Promise.all([_shifts,_products, _productionData])
      const [ shifts, products, productionData  ] = _record
      const response = selfUtil.responseObject(shifts, products, productionData, _start_date, _end_date);
      return exits.success(response);
    } catch (err) {
      return exits.serverError(err);
    }
  },
};
