const alertIncidentHistoryService = require('../../services/alerts/alertIncidentHistory/alertIncidentHistory.service');

module.exports = {
  friendlyName: 'Acknowledge Alert',
  description: 'Acknowledge an active alert incident by a subscribed user',

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    },
    alertId: {
      type: 'number',
      required: true,
      description: 'The ID of the alert in inventory'
    }
  },

  exits: {
    success: {
      description: 'Alert incident acknowledged successfully',
    },
    serverError: {
      statusCode: 500,
      description: 'Server error occurred',
    },
    notFound: {
      statusCode: 404,
      description: 'Alert incident or alert not found',
    },
    badRequest: {
      statusCode: 400,
      description: 'Invalid request or alert already acknowledged',
    }
  },

  fn: async function (inputs, exits) {
    try {
      const { alertId } = inputs;
      const { id: userId, _site: siteId } = inputs._userMeta;

      const updatedIncident = await alertIncidentHistoryService.acknowledge(userId, siteId, alertId);
      return exits.success(updatedIncident);
    } catch (error) {
      sails.log.error(`Error in acknowledgeAlert: ${error}`);

      switch (error.code) {
        case 'E_NOT_FOUND': {
          return exits.notFound({ error: error.message });
        }
        case 'E_ALREADY_ACKNOWLEDGED':
        case 'E_ALREADY_ESCALATED':
        case 'E_NOT_SUBSCRIBED': {
          return exits.badRequest({ error: error.message });
        }
        default: {
          return exits.serverError(error);
        }
      }
    }
  }
};
