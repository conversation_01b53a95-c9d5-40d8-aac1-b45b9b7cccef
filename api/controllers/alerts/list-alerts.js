const moment = require('moment-timezone');
const alertIncidentHistoryService = require('../../services/alerts/alertIncidentHistory/alertIncidentHistory.service');
const { parseDateToUTC } = require('../../utils/globalhelper');

module.exports = {
  friendlyName: 'List Alerts',
  description: 'Get list of alerts with their details and filters',

  inputs: {
    siteId: {
      type: 'string',
      required: true,
      description: 'Site ID to fetch alerts for',
    },
    startTime: {
      type: 'string',
    },
    endTime: {
      type: 'string',
    },
    alertStatus: {
      type: 'string',
      isIn: ['all', 'active', 'resolved'],
      defaultsTo: 'active',
      description: 'Filter by alert status',
    },
    subscriberId: {
      type: 'string',
      description: 'Filter alerts by subscriber',
    },
    assetType: {
      type: 'string',
      description: 'Filter by asset types: Chillers,AHUs',
      custom: function(value) {
        if (!value) return true;
        const types = value.split(',');
        return types.length > 0;
      }
    },
    severity: {
      type: 'string',
      description: 'Filter by severity levels: low,medium,high,critical',
      custom: function(value) {
        if (!value) return true;
        const validSeverities = ['low', 'medium', 'high', 'critical'];
        const severities = value.split(',');
        return severities.every(severity => validSeverities.includes(severity.toLowerCase()));
      }
    },
    durationFilter: {
      type: 'string',
      description: 'duration filters: short,medium,long',
      custom: function(value) {
        if (!value) return true;
        const filters = value.split(',');
        const validValues = ['short', 'medium', 'long'];
        return filters.every(filter => validValues.includes(filter));
      }
    },
    search: {
      type: 'string',
      description: 'Search by alert name or ID',
    },
    sortBy: {
      type: 'string',
      isIn: ['name', 'severity', 'occurredAt', 'duration', 'recentOccurrence'],
      defaultsTo: 'recentOccurrence',
      description: 'Field to sort by',
    },
    sortOrder: {
      type: 'string',
      isIn: ['asc', 'desc'],
      defaultsTo: 'desc',
      description: 'Sort order (ascending or descending)',
    },
    assetId: {
      type: 'string',
      description: 'Filter by component ID',
      custom: function(value) {
        if (!value) return true;
        const ids = value.split(',');
        return ids.length > 0;
      }
    }
  },

  exits: {
    success: {
      description: 'Successfully retrieved alerts',
    },
    badRequest: {
      statusCode: 400,
      description: 'Invalid parameters',
    },
    notFound: {
      statusCode: 404,
      description: 'Alert not found',
    },
    serverError: {
      statusCode: 500,
      description: 'Server error occurred',
    },
  },

  fn: async function (inputs, exits) {
    try {
      if ((inputs.startTime && !inputs.endTime) || (!inputs.startTime && inputs.endTime)) {
        return exits.badRequest({ error: 'Both startTime and endTime must be provided together' });
      }

      let startTime, endTime;

      if (inputs.startTime && inputs.endTime) {
        startTime = parseDateToUTC(inputs.startTime);
        endTime = parseDateToUTC(inputs.endTime);

        if (startTime >= endTime) {
          return exits.badRequest({ error: 'startTime must be before endTime' });
        }

      } else {
        endTime = moment().tz('Asia/Kolkata').utc().toISOString();
        startTime = moment().tz('Asia/Kolkata').startOf('day').utc().toISOString();
      }

      const alerts = await alertIncidentHistoryService.listAlerts({
        ...inputs,
        startTime,
        endTime,
      });

      return exits.success(alerts);
    } catch (error) {
      sails.log.error(`Error in listAlerts: ${error}`);

      switch (error.code) {
        case 'E_NOT_FOUND': {
          return exits.notFound({ error: error.message });
        }
        case 'E_BAD_REQUEST': {
          return exits.badRequest({ error: error.message });
        }
        default: {
          return exits.serverError(error);
        }
      }
    }
  },
};
