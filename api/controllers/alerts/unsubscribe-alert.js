const flaverr = require("flaverr");
const { unsubscribe } = require("../../services/alertInventory/alertInventory.service");

module.exports = {
  friendlyName: "Unsubscribe an alert",
  description: "This API will unsubscribe an alert from database. The user will stop receiving the alert notification on any channel",
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    },
    alertId: {
      type: "string",
      description: "The name of the alert that needs to be unsubscribed",
      example: "UNABLE_TO_TURN_ON_ASSET_OCCURRED",
    },
    userId: {
      type: "string",
      description: "The user id of the user who is unsubscribing the alert",
      required: true,
    }
  },

  exits: {
    serverError: {
      description: "[alerts > unsubscribe-alert] Server Error!",
    },
    badRequest: {
      statusCode: 400,
      description: "[alerts > unsubscribe-alert] Bad Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[alerts > unsubscribe-alert] Not Found",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { alertId, userId } = inputs;
      if (!userId || _.isEmpty(userId.trim())) {
        throw flaverr("E_INVALID_ARGUMENT", new Error("User ID is required"));
      }
      await unsubscribe(alertId, userId);
      return exits.success({
        message: `Alert(${alertId}) unsubscribed successfully`,
      });
    } catch (err) {
      if (err.code === "E_NOT_FOUND") {
        return exits.notFound({
          error: err.message,
        });
      } else if (err.code === "E_INVALID_ARGUMENT") {
        return exits.badRequest({
          error: err.message,
        });
      } else {
        sails.log("alerts > unsubscribe-alert");
        sails.log.error(err);
        return exits.serverError(err);
      }
    }
  },
};
