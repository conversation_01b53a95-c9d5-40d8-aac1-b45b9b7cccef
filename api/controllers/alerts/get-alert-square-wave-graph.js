const alertGraphService = require("../../services/alerts/graph/get-alert-graph");
const { validateAlertSquareWaveGraph } = require("../../utils/alerts/graph.util");

module.exports = {
  friendlyName: 'Get alert graph',
  description: 'Get alert graph',
  inputs: {
    siteId: {
      type: 'string',
      required: true,
      description: 'Site ID'
    },
    alertId: {
      type: 'number',
      required: true,
      description: 'Alert ID to fetch subscribers for'
    },
    startTime: {
      type: 'string'
    },
    endTime: {
      type:'string'
    }
  },

  exits: {
    success: {
      description: 'Successfully retrieved subscribers',
    },
    notFound: {
      statusCode: 404,
      description: 'Alert not found',
    },
    badRequest: {
      statusCode: 400,
      description: 'Invalid request parameters',
    },
    serverError: {
      statusCode: 500,
      description: 'Server error occurred',
    }
  },

  fn: async function (inputs, exits) {
    try {
      const { alertId, siteId, startTime, endTime} = inputs;
      validateAlertSquareWaveGraph({siteId, alertId, startTime, endTime})
      const response = await alertGraphService.getAlertSquareWaveGraph({siteId, alertId, startTime: startTime.trim(), endTime: endTime.trim()});
      return exits.success(response);
    } catch (error) {
      sails.log.error(`Error: getAlertGraph`, error);
      if(error.statusCode == 400) {
        return exits.badRequest({err: error.msg});
      } else {
        return exits.serverError(error);
      }
    }
  }
};
