const alertSubscriptionService = require('../../services/alerts/alertSubscription/alertSubscription.service');
const subscriptionUtil = require('../../utils/alerts/subscription.util');
const moment = require('moment-timezone');

module.exports = {
  friendlyName: 'Pause Smart Alert',
  description: 'Pause notifications for a smart alert subscription',

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    },
    siteId: {
      type: 'string',
      required: true,
      description: 'Site ID'
    },
    alertId: {
      type: 'number',
      required: true,
      description: 'Alert ID'
    },
    pausedTill: {
      type: 'string',
      description: 'ISO timestamp until which notifications should be paused',
      example: '2024-01-01T00:00:00Z',
      allowNull: true
    }
  },

  exits: {
    success: {
      description: 'Successfully updated pause status',
    },
    notFound: {
      statusCode: 404,
      description: 'Subscription not found'
    },
    badRequest: {
      statusCode: 400,
      description: 'Invalid request parameters'
    },
    serverError: {
      statusCode: 500,
      description: 'Server error occurred'
    }
  },

  fn: async function (inputs, exits) {
    try {
      const { siteId, alertId, pausedTill, _userMeta: { id: subscriberId } } = inputs;

      const validatedInputs = subscriptionUtil.validatePauseInputs({
        subscriberId,
        alertId,
        siteId,
        pausedTill
      });

      const subscription = await alertSubscriptionService.updatePause({
        ...validatedInputs,
        paused_till: pausedTill !== undefined ? validatedInputs.pausedTill : undefined
      });

      return exits.success(subscription);
    } catch (error) {
      sails.log.error(`Error in pauseSmartAlert: ${error}`);

      switch (error.code) {
        case 'E_NOT_FOUND': {
          return exits.notFound({ error: error.message });
        }
        case 'E_BAD_REQUEST': {
          return exits.badRequest({ error: error.message });
        }
        default: {
          return exits.serverError(error);
        }
      }
    }
  }
};
