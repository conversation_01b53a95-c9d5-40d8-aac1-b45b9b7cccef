const moment = require('moment-timezone');
const alertService = require('../../services/alerts/alerts.service');

module.exports = {
  friendlyName: 'Count Alerts',
  description: 'Get count of alerts within specified time range',

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: {
        id: 'userId',
        _role: 'role',
        _site: 'siteId'
      },
    },
    siteId: {
      type: 'string',
      required: true,
      description: 'Site ID to fetch alerts for'
    },
    startTime: {
      type: 'string',
      required: false,
      description: 'Start time in ISO format'
    },
    endTime: {
      type: 'string',
      required: false,
      description: 'End time in ISO format'
    },
  },

  exits: {
    success: { description: 'Successfully retrieved alerts count' },
    badRequest: {
      statusCode: 400,
      description: 'Invalid parameters'
    },
    serverError: {
      statusCode: 500,
      description: 'Server error occurred'
    }
  },

  fn: async function (inputs, exits) {
    try {
      const {
        siteId,
        startTime,
        endTime,
        _userMeta: { id: userId }
      } = inputs;

      if (!_validateTimeInputs(startTime, endTime)) {
        return exits.badRequest({ error: 'Both startTime and endTime must be provided together, and startTime must be before endTime' });
      }

      const timeRange = _getTimeRange(startTime, endTime);

      const alertCount = await alertService.getAlertCount({
        siteId,
        timeRange,
        userId
      });

      const response = {
        siteId,
        totalActiveAlerts: _parseCount(alertCount?.total_alert_count),
        myActiveAlerts: _parseCount(alertCount?.alert_count_for_subscriber)
      };

      return exits.success(response);

    } catch (error) {
      sails.log.error(`Error in count alerts for siteId: ${inputs.siteId}`, error);
      return exits.serverError({
        error: 'Failed to retrieve alert count'
      });
    }
  }
};

/**
 * Validates time input parameters
 */
function _validateTimeInputs(startTime, endTime) {
  if ((startTime && !endTime) || (!startTime && endTime)) return false;

  if (startTime && endTime) {
    const start = moment.tz(startTime, 'UTC');
    const end = moment.tz(endTime, 'UTC');

    if (!start.isValid() || !end.isValid()) return false;
    if (start.isAfter(end)) return false;
  }

  return true;
}

/**
 * Calculate appropriate time range for query
 */
function _getTimeRange(startTime, endTime) {
  if (startTime && endTime) {
    return {
      start: moment.tz(startTime, 'UTC').toISOString(),
      end: moment.tz(endTime, 'UTC').toISOString()
    };
  }

  // No time filtering - return null to indicate no time constraints
  return null;
}

/**
 * Safely parse count values
 */
function _parseCount(value) {
  const parsed = Number(value);
  return Number.isNaN(parsed) ? 0 : parsed;
}
