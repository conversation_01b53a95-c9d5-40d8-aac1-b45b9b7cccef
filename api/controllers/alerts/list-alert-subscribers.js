const alertSubscriptionService = require('../../services/alerts/alertSubscription/alertSubscription.service');

module.exports = {
  friendlyName: 'List Alert Subscribers',
  description: 'Get all subscribers for a specific alert',

  inputs: {
    siteId: {
      type: 'string',
      required: true,
      description: 'Site ID'
    },
    alertId: {
      type: 'number',
      required: true,
      description: 'Alert ID to fetch subscribers for'
    }
  },

  exits: {
    success: {
      description: 'Successfully retrieved subscribers',
    },
    notFound: {
      statusCode: 404,
      description: 'Alert not found',
    },
    badRequest: {
      statusCode: 400,
      description: 'Invalid request parameters',
    },
    serverError: {
      statusCode: 500,
      description: 'Server error occurred',
    }
  },

  fn: async function (inputs, exits) {
    try {
      const { alertId, siteId } = inputs;
      const subscribers = await alertSubscriptionService.listSubscribers({ alertId, siteId });
      return exits.success(subscribers);
    } catch (error) {
      sails.log.error(`Error in listAlertSubscribers:`, error);

      switch (error.code) {
        case 'E_NOT_FOUND': {
          return exits.notFound({ error: error.message });
        }
        case 'E_BAD_REQUEST': {
          return exits.badRequest({ error: error.message });
        }
        default: {
          return exits.serverError(error);
        }
      }
    }
  }
};
