const alertSubscriptionService = require('../../services/alerts/alertSubscription/alertSubscription.service');
const subscriptionUtil = require('../../utils/alerts/subscription.util');

module.exports = {
  friendlyName: 'Unsubscribe from Smart Alert',
  description: 'Unsubscribe a user from a smart alert',

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    },
    siteId: {
      type: 'string',
      required: true,
      description: 'Site to unsubscribe from smart alert'
    },
    alertId: {
      type: 'number',
      required: true,
      description: 'Alert to unsubscribe from'
    }
  },

  exits: {
    success: {
      description: 'Successfully unsubscribed from alert',
    },
    notFound: {
      statusCode: 404,
      description: 'Alert or subscription not found'
    },
    badRequest: {
      statusCode: 400,
      description: 'Invalid request parameters'
    },
    serverError: {
      statusCode: 500,
      description: 'Server error occurred'
    }
  },

  fn: async function (inputs, exits) {
    try {
      const { alertId, siteId, _userMeta: { id: subscriberId } } = inputs;

      const validatedInputs = subscriptionUtil.validateSubscriptionInputs({
        subscriberId,
        alertId,
        siteId
      });

      const result = await alertSubscriptionService.unsubscribe(validatedInputs);
      return exits.success(result);
    } catch (error) {
      sails.log.error(`Error in unsubscribeSmartAlert: ${error}`);

      switch (error.code) {
        case 'E_NOT_FOUND': {
          return exits.notFound({ error: error.message });
        }
        case 'E_BAD_REQUEST': {
          return exits.badRequest({ error: error.message });
        }
        default: {
          return exits.serverError(error);
        }
      }
    }
  }
};
