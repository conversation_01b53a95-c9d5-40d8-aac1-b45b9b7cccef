const alertSubscriptionService = require('../../services/alerts/alertSubscription/alertSubscription.service');
const subscriptionUtil = require('../../utils/alerts/subscription.util');
const moment = require('moment-timezone');

module.exports = {
  friendlyName: 'Subscribe to Smart Alert',
  description: 'Subscribe a user to a smart alert with notification preferences. For CPA alerts, defaults to WhatsApp notifications. For all other alerts, defaults to email notifications.',

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    },
    siteId: {
      type: 'string',
      required: true,
      description: 'Site to subscribe to smart alert'
    },
    alertId: {
      type: 'number',
      required: true,
      description: 'Alert to subscribe to'
    },
    channels: {
      type: 'json',
      description: 'Optional notification channels. If not provided, defaults based on alert category: WhatsApp for CPA alerts, email for others.',
      example: ['email', 'whatsapp']
    }
  },

  exits: {
    success: {
      description: 'Successfully subscribed to alert',
    },
    notFound: {
      statusCode: 404,
      description: '<PERSON><PERSON> not found'
    },
    badRequest: {
      statusCode: 400,
      description: 'Invalid request parameters'
    },
    serverError: {
      statusCode: 500,
      description: 'Server error occurred'
    }
  },

  fn: async function (inputs, exits) {
    try {
      const { siteId, alertId, channels, _userMeta: { id: subscriberId } } = inputs;

      const validatedInputs = subscriptionUtil.validateSubscriptionInputs({
        subscriberId,
        alertId,
        siteId
      });

      const validatedChannels = channels ? subscriptionUtil.validateChannels(channels) : [];

      const subscription = await alertSubscriptionService.subscribe({
        ...validatedInputs,
        channels: validatedChannels
      });

      return exits.success({
        alertId: subscription.alert_id,
        subscriberId: subscription.subscriber_id,
        channels: {
          email: subscription.notify_on_email,
          sms: subscription.notify_on_sms,
          whatsapp: subscription.notify_on_whatsapp
        },
        subscribedAt: subscription.subscribed_at
      });
    } catch (error) {
      sails.log.error(`Error in subscribeSmartAlert: ${error}`);

      switch (error.code) {
        case 'E_NOT_FOUND': {
          return exits.notFound({ error: error.message });
        }
        case 'E_BAD_REQUEST': {
          return exits.badRequest({ error: error.message });
        }
        default: {
          return exits.serverError(error);
        }
      }
    }
  }
};
