const flaverr = require("flaverr");
const alertInventoryService = require('../../services/alertInventory/alertInventory.service');
const { subscriptionCriteriaValidator } = require('../../utils/alertInventory/alertInventory.util');
const userSiteMapService = require('../../services/userSiteMap/userSiteMap.service');

module.exports = {
  friendlyName: 'Subscribe alert',
  description: 'User can subscribe existing alert for provided alert name from provided site & device',
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    },
    userId: {
      type: "string",
      required: true,
      description: "The user id of the user who is subscribing the alert",
    },
    alertId: {
      type: "string",
      required: true,
      description: "name of the alert that user wants to be subscribed",
      example: "UNABLE_TO_TURN_ON_ASSET",
    },
    priority: {
      type: 'number',
      required: false,
      defaultTo: 1,
      description: '0=>critical,1=>normal'
    },
    subscriptionCriteria: {
      type: "json",
      required: true,
      description: "The site(s) wise device(s) on which the alert needs to be subscribed",
      example: [
        {
          siteId: "site1",
          deviceIds: ["device1", "device2"]
        },
        {
          siteId: "site2",
          deviceIds: "*"
        }
      ]
    }
  },


  exits: {
    serverError: {
      responseType: 'serverError',
    },
    badRequest: {
      responseType: 'badRequest',
    },
    unauthorized: {
      responseType: 'unauthorized',
    },
    created: {
      statusCode: 201,
    },
  },


  fn: async function (inputs, exits) {
    try {
      const { userId, alertId, priority, subscriptionCriteria } = inputs;
      if (subscriptionCriteriaValidator(subscriptionCriteria).error) {
        return exits.badRequest({ error: "subscriptionCriteria can be either * (for all sites/devices) or an array of objects with siteId & deviceIds" });
      }
      if (subscriptionCriteria != '*') {
        const inputSites = subscriptionCriteria.map(site => site.siteId);
        const validSites = await Sites.find();
        const areSitesValid = inputSites.every(site => validSites.some(validSite => validSite.siteId === site));
        if (!areSitesValid) {
          throw flaverr("E_NOT_FOUND", new Error("Some of the sites provided are invalid"));
        }
      }
      await alertInventoryService.subscribe(this.req.body);
      return exits.created({
        message: 'You have successfully subscribed. You will start getting alert after 15 mins'
      })
    } catch (err) {
      if (err.code === "E_NOT_FOUND") {
        return exits.badRequest({ message: err.message });
      }
      return exits.serverError(err);
    }
  }
};
