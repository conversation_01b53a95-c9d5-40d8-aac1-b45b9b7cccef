const flaverr = require("flaverr");
const { delete:deleteAlertInstance } = require("../../services/alertInventory/alertInventory.service");

module.exports = {
  friendlyName: "Delete alert instance",
  description: "This API will soft delete the alert instance from database. All the subscribers of the that alert instance will stop receiving the alert notification on any channel",
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    },
    alertId: {
      type: "string",
      description: "The name of the alert that needs to be deleted",
      required: true,
      example: "UNABLE_TO_TURN_ON_ASSET_OCCURRED",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[alerts > delete-alert-instance] Server Error!",
    },
    badRequest: {
      statusCode: 400,
      responseType: "badRequest",
      description: "[alerts > delete-alert-instance] Bad Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[alerts > delete-alert-instance] Not Found",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { alertId } = inputs;
      if (!alertId || _.isEmpty(alertId.trim())) {
        throw flaverr("E_INVALID_ARGUMENT", new Error("Alert ID is required"));
      }
      await deleteAlertInstance(alertId);
      return exits.success({
        message: `Alert(${alertId}) deleted successfully`,
      });
    } catch (err) {
      if (err.code === "E_NOT_FOUND") {
        return exits.notFound({
          error: err.message,
        });
      } else if (err.code === "E_INVALID_ARGUMENT") {
        return exits.badRequest({
          error: err.message,
        });
      } else {
        sails.log("alerts > delete-alert-instance");
        sails.log.error(err);
        return exits.serverError(err);
      }
    }
  },
};
