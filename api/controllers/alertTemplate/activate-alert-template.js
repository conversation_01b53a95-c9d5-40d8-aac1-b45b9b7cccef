const alertInventoryService = require('../../services/alertInventory/alertInventory.service');
module.exports = {
  friendlyName: 'activateAlert',
  description: 'activate alert ',
  inputs: {
    _userMeta: {
      type: 'json',
      description: '',
      required: true,
    },
    alertId: {
      type: 'number',
      description: 'alert id',
      required: true
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[alertInventory > activateAlert] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[alertInventory > activateAlert] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[alertInventory > activateAlert] unauthorized!',
    },
    notFound: {
      statusCode: 404,
      description: '[alertInventory > activateAlert] alert not found'
    },
    success: {
      statusCode: 200,
      description: '[alertInventory > activateAlert] alert has been successfully activated'
    }
  },
  async fn(inputs, exits) {
    try {
      // noinspection JSUnresolvedVariable
      const { alertId } = inputs;
      const response = await alertInventoryService.activate(alertId);
      return exits.success(response);
    } catch (e) {
      if (e.code === 'E_ALERT_NOT_FOUND') {
        return exits.notFound({ message: e.message });
      } else {
        return exits.serverError(e);
      }
    }
  },
};
