const alertInventoryService = require('../../services/alertInventory/alertInventory.service');
module.exports = {
  friendlyName: 'registerAlert',
  description: 'this api will create new alert',
  inputs: {
    _userMeta: {
      type: 'json',
      description: '',
      required: true,
    },
    alert_name: {
      type: 'string',
      required: true
    },
    observer_source: {
      type: 'string',
      required: true,
      isIn: ['OTHER', 'CPA']
    },
    message_template_id: {
      type: 'number',
      required: true
    },
    priority: {
      type: 'number',
      required: false,
      defaultTo: 1,
      description: '0=>critical,1=>normal'

    }
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[AlertInventory > create] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[AlertInventory > create] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[AlertInventory > create] unauthorized!',
    },
    created: {
      statusCode: 201,
      description: '[AlertInventory > create] Alert has been created'
    },
  },
  async fn(inputs, exits) {
    try {
      // noinspection JSUnresolvedVariable
      const { body: param } = this.req;
      //TODO: write a validation rule for new alert template
      //if observer_source is other then config should not be empty and it should some keys like
      //object should be {
      //   "siteId": "{{siteId}}",
      //   "chillerId": "{{chillerId}}",
      //   "eventKeyMap": {
      //     "OCCURRED": "{{occurredEventKey}}",
      //     "RESOLVED": "{{resolvedEventKey}}"
      //   }
      // }
      let {
        id: alert_id,
        uid: alert_uid,
        description,
        observer_source
      } = await alertInventoryService.register(param);
      return exits.created({
        alert_id,
        alert_uid,
        description,
        observer_source
      });
    } catch (e) {
      if (e.code === 'INPUT_VALIDATION_ERROR' || e.code === 'E_MESSAGE_TEMPLATE_NOT_FOUND') {
        return exits.badRequest({ message: e.message });
      } else if(e.code == "E_UNIQUE"){
        return exits.badRequest({message: `alert name should be unique`})
      }
      else {
        return exits.serverError(e);
      }
    }
  },
};
