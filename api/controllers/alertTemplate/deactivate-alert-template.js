const alertInventoryService = require('../../services/alertInventory/alertInventory.service');
module.exports = {
  friendlyName: 'deactivateAlert',
  description: 'deactivate alert ',
  inputs: {
    _userMeta: {
      type: 'json',
      description: '',
      required: true,
    },
    alertId: {
      type: 'number',
      description: 'alert id',
      required: true
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[alertInventory > deactivateAlert] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[alertInventory > deactivateAlert] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[alertInventory > deactivateAlert] unauthorized!',
    },
    notFound: {
      statusCode: 404,
      description: '[alertInventory > deactivateAlert] alert not found'
    },
    success: {
      statusCode: 200,
      description: '[alertInventory > deactivateAlert] alert has been successfully deactivated'
    }
  },
  async fn(inputs, exits) {
    try {
      // noinspection JSUnresolvedVariable
      const { alertId } = inputs;
      const response = await alertInventoryService.deactivate(alertId);
      return exits.success(response);
    } catch (e) {
      if (e.code === 'E_ALERT_NOT_FOUND') {
        return exits.notFound({ message: e.message });
      } else {
        return exits.serverError(e);
      }
    }
  },
};
