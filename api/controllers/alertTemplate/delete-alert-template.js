const alertInventoryService = require('../../services/alertInventory/alertInventory.service');
module.exports = {
  friendlyName: 'deleteAlert',
  description: 'This DELETE REST API is deleting the alert',
  inputs: {
    _userMeta: {
      type: 'json',
      description: '',
      required: true,
    },
    alertId: {
      type: 'number',
      description: 'alert id',
      required: true
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[alert > delete] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[alert > delete] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[alert > delete] unauthorized!',
    },
    deleted: {
      statusCode: 204,
      description: '[alert > delete] Alert has been deleted'
    },
  },
  async fn(inputs, exits) {
    try {
      // noinspection JSUnresolvedVariable
      const { alertId } = inputs;
      const response = await alertInventoryService.delete(alertId);
      return exits.deleted(response);
    } catch (e) {
      return exits.serverError(e);
    }
  },
};
