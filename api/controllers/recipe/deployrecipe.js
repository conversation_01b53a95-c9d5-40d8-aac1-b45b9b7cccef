
const utils = require('../../utils/recipe/utils');
const self = require('../../services/recipe/recipe.service');

const globalHelper = require('../../utils/globalhelper');

module.exports = {


  friendlyName: 'Deployrecipe',
  description: 'Deployrecipe recipe configration & its schedules to controller/server. DO NOTHING IF ALREADY DEPLOYED',

  example: [
    `curl -X POST -H "Content-Type: application/json" --data '{"rid": "3410ee75-af92-45b5-b3c1-574d726a3980", "siteId": "ssh" }' 0:1337/recipe`,
  ],

  inputs: {
    rid: {
      type: 'string',
      example: '3410ee75-af92-45b5-b3c1-574d726a3980',
      description: 'Unique recipe ID of the recipe',
      required: true
    },
    siteId: {
      type: 'string',
      example: 'ssh',
      description: 'The site ID of the recipe',
      required: true
    }
  },


  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[recipe > deploy-recipe] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[recipe > deploy-recipe] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[recipe > deploy-recipe] forbidden Request!',
    }
  },
  // exits.serverError("Error ")

  fn: async function (inputs, exits) {

    try {
      let { rid, siteId } = inputs;
      let recipe = await self.findOne({ rid, siteId });

      if (!recipe) {
        return exits.forbidden({ problems: ['Recipe Doesnt exist'] });
      }
      if (recipe.isStage === '0') {
        // 0 means recipe is already deployed
        return exits.forbidden({ problems: ['Recipe already deployed. Please schedule'] });
      }

      let schedules = globalHelper.toArray(recipe.scheduled);
      let sendController = recipe['runOn'];
      let { appType = 'recipe' } = recipe;

      if (sendController === 'server') {
        await self.sendRecipeToserver(recipe, schedules);
        return exits.success({status: 'Deployed'});
      } else {
        let configObject;
        switch(appType){
          case 'recipe':
            configObject = utils.parseRecipeObjectToSendToController(recipe);
            break;

          case 'thermal':
            configObject = utils.parseThermalObjectToSendToController(recipe);
            break;

          default:
            return exits.forbidden({problems: ['Invalid App Type']});
        }
        let sendRecipeConfig = true; // send full configObject to controller
        let response = await self.sendRecipeToController(configObject, schedules, siteId, sendRecipeConfig);
        if (response.problems) return exits.forbidden(response);
      }

      return exits.success({status: 'in-process'});

    } catch (e) {
      sails.log.error(e);
      exits.serverError(e);
    }
  }
};
