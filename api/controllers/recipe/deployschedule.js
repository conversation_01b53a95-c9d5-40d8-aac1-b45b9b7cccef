const utils = require('../../utils/recipe/utils');
const deployScheduleUtil = require('./../../utils/recipe/deployschedule.util');
const globalHelper = require('../../utils/globalhelper');

const self = require('../../services/recipe/recipe.service');


module.exports = {


  friendlyName: 'Deployschedule',
  description: 'Deploy schedule of an already deployed recipe. WILL NOT SEND RECIPE CONFIGRATION',

  example: [

  ],

  inputs: {
    rid: {
      type: 'string',
      example: '3410ee75-af92-45b5-b3c1-574d726a3980',
      description: 'Unique recipe ID of the recipe',
      required: true
    },
    sid: {
      type: 'string',
      example: '25216298-c455-496d-997b-5b0a17ae8270',
      description: 'Unique schedule ID of the schedule',
      required: true
    },
    siteId: {
      type: 'string',
      example: 'ssh',
      description: 'The site ID of the recipe',
      required: true
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[recipe > create-recipe] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[recipe > create-recipe] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[recipe > create-recipe] forbidden Request!',
    }
  },
  // exits.serverError("Error ")

  fn: async function (inputs, exits) {
    try {
      let { rid, siteId, sid } = inputs;
      let runOn;
      let recipe = await self.findOne({ rid, siteId });

      /* Recipe specific check, put these into function */
      if (!recipe) return exits.badRequest({ problems: ['No recipe'] });
      if (recipe.isStage === '1') {
        return exits.forbidden({ problems: ['Recipe Not yet deployed'] });
      }

      /* Schedule specific check, put these into function */
      let schedules = globalHelper.toArray(recipe.scheduled);
      schedules = schedules ? schedules : [];
      if (!schedules.includes(sid)) {
        return exits.forbidden({ problems: ['schedule doesnt belong to this recipe'] });
      }
      let schedule = await self.schedules.findOne({rid, sid});
      if (!schedule) return exits.badRequest({ problems: ['Schedule donot exist'] });
      if (schedule['isDeployed'] === '1') {
        return exits.forbidden({ problems: ['Schedule already deployed'] });
      }

      /* Business Logic */
      runOn = recipe['runOn'];
      if (runOn === 'server') {
        await self.sendRecipeToserver(recipe, [ sid ] );
      } else {
        let configObj = {runOn, rid };
        let sendRecipeConfig = false; // donot send recipe config to controller
        let response = await self.sendRecipeToController(configObj, [sid], siteId, sendRecipeConfig);
        if (response.problems) return exits.forbidden(response);
      }
      return exits.success({'status': 'in-process'});

    } catch (e) {
      sails.log.error('Error in deployschedule: ' + e);
      return exits.serverError(e);
    }
  }
};
