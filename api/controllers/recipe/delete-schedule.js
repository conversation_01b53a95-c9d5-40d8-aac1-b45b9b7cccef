const recipeService = require('../../services/recipe/recipe.service');
const globalHelper = require('../../utils/globalhelper');

module.exports = {
  friendlyName: 'deleteSchedule',
  description: '',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    siteId: {
      type: 'string',
      example: 'Id of site',
      required: true
    },
    rid: {
      type: 'string',
      example: '3410ee75-af92-45b5-b3c1-574d726a3980',
      description: 'Unique recipe ID of the recipe',
      required: true
    },
    sid: {
      type: 'string',
      example: '3410ee75-af92-45b5-b3c1-574d726a3980',
      description: 'Unique schedule ID to indetify each schedule differently',
      required: true
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[recipe > deleteSchedule] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[recipe > deleteSchedule] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[recipe > deleteSchedule] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try {
      let { sid, rid, siteId } = inputs;
      let recipe = await recipeService.findOne({ rid, siteId });

      if (!recipe) {
        return exits.badRequest({ problems: ['Recipe dont exist'] });
      }

      let { isStage, runOn } = recipe;
      let schedules = globalHelper.toArray(recipe['scheduled']);
      if (!schedules.includes(sid)) {
        return exits.forbidden({ problems: ['Schedule  is deleted already'] });
      }

      let data = { rid, sid };
      if (isStage === '1' || runOn === 'server') {
        let response = await recipeService.deleteSchedule(siteId, data);
        if (response) return exits.success({ 'status': 'ok' });
        else throw 'Unable to delete schedule';
      } else {

        recipeService.invokeFunctionOnController(
          data,
          'deleteSchedule',
          runOn,
          siteId
        );
        return exits.success({ 'status': 'in-process' });
      }
    } catch (error) {
      sails.log.error('[recipe > deleteSchedule] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
