const recipeService = require('../../services/recipe/recipe.service.js');
const globalHelper = require('../../utils/globalhelper.js');
const selfUtils = require('../../utils/recipe/find-schedule.util.js');

module.exports = {
  friendlyName: 'findScheduleBySiteId',
  description: 'Get list of all the configured schedules on a site',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    filter: {
      type: 'json',
      example: 'All the applicable filters'
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[recipe > findScheduleBySiteId] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[recipe > findScheduleBySiteId] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[recipe > findScheduleBySiteId] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try {
      let { siteId } = this.req.params;
      let { filter } = inputs;
      let query = {};
      query['siteId'] = siteId;
      query = {...query, ...filter};
      let parsedSchedulesList = [];
      let schedules = await recipeService.schedules.find(query);

      if (!schedules || schedules.length === 0) {
        return exits.success(parsedSchedulesList);
      }

      for (let schedule of schedules) {
        let parsedSchedule = selfUtils.parseScheduleObjectForFrontEnd(schedule);
        let isDeployed = schedule.isDeployed === '1' ? true : false;

        parsedSchedulesList.push({
          id: schedule.sid,
          deploy: isDeployed,
          'repeat_interval': schedule.repeat_type,
          'all_day': globalHelper.toBoolean(schedule.schedule),
          rid: schedule.rid,
          ...parsedSchedule,
        });
      }

      return exits.success(parsedSchedulesList);

    } catch (error) {
      sails.log.error('[recipe > findScheduleBySiteId] Error!');
      sails.log.error(error);
      return exits.serverError(error);
    }
  }
};
