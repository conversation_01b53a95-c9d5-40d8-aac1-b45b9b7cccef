
const recipeService = require('../../services/recipe/recipe.service');
const globalHelper = require('../../utils/globalhelper');
const selfUtils = require('../../utils/recipe/bulk-recipe-generation.util.js');
const utils = require('../../utils/recipe/utils.js');
const SheetService = require("../../services/google/bulk-configuration.service.js");

module.exports = {
  friendlyName: 'bulkRecipeGeneration',
  description : '',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    siteId: {
      type: 'string',
      required: false,
      example: 'sjo-del',
      description: 'SiteId if mentioned explicitly, overwrites the siteId inherited from the token.',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[recipe > bulkRecipeGeneration] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[recipe > bulkRecipeGeneration] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[recipe > bulkRecipeGeneration] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      const {
        _userMeta: { _site }, siteId: siteIdInBodyParams
      } = inputs;
      const siteId = siteIdInBodyParams ? siteIdInBodyParams : _site;

      // Reading values from the google sheet.
      let sheet = new SheetService(sheetId);
      await sheet.init();
      const rows = await sheet.getAllRows();

      return exits.success();
    } catch(error) {
      sails.log.error('[recipe > bulkRecipeGeneration] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
