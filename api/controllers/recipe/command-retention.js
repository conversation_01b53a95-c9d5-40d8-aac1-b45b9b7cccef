const CommandRetention = require('../../services/recipe/lib/CommandRetention');
const { validateInput, formatSaveCommandRetention } = require('../../utils/recipe/command-retention.util');

module.exports = {
  friendlyName: 'Enable command retention',
  description: 'Enable command retention',
  example: [
    
  ],
  inputs: {
  _userMeta: {
    type: 'json',
    required: true,
    example: { id: 'userId', _role: 'role', _site: 'siteId' },
  },
   siteId: {
    type: 'string',
   },
   componentId: {
    type: 'string',
   },
   controls: { // TODO: 
    type: 'ref',
    example: [{
     controlAbbr: 'turnOnOff',
     commandRetentionStatus:1
    },{
      controlAbbr: 'setFrequency',
      commandRetentionStatus:0
    }]
   }
  },


  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[recipe > deploy-recipe] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[recipe > deploy-recipe] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[recipe > deploy-recipe] forbidden Request!',
    }
  },

  fn: async function (inputs, exits) {
    try {
      const {
        siteId,
        componentId,
        controls,
        _userMeta: {id: userId}
      } = inputs;
      await validateInput({siteId, componentId, controls})

      const commandRN = new CommandRetention();
      commandRN.setUserId(userId)
      commandRN.setSiteId(siteId)
      await commandRN.setAssetInfoById(componentId)
      const {enableCommands, disableCommands} = await commandRN.save(controls);
      const response = formatSaveCommandRetention(enableCommands, disableCommands)
      return exits.success({
        msg: 'Your request has been successfully submitted. We are processing it, please wait.',
        data: response
      })

    } catch (e) {
        if (e.HTTP_STATUS_CODE == 400) {
          return exits.badRequest({
          err: e.message
        });
      } else {
      sails.log.error(e);
      return exits.serverError({
        err: 'Server has encountered an issue.Please contact the administrator'
      });
    }
  }
  }
};
