const updateRecipeUtil = require('../../utils/recipe/updateRecipe.util');
const recipeService = require('../../services/recipe/recipe.service');
const globalHelper = require('../../utils/globalhelper');
const utils = require('../../utils/recipe/utils');
const self = require('../../services/recipe/recipe.service');
const {
  syncRecipeInSmartAlert
} = require('../../services/smartAlert/smartAlert.public');

module.exports = {

  friendlyName: 'Updaterecipe',
  description: 'Updaterecipe recipe.',

  example: [
    `curl -X PUT -H "Content-Type: application/json" --data '{ "formula": "P||$4||#1||$2||0,60||$5||", "label": "test-recipe-1-r3", "type": "nonRoutine", "recipelabel": ["Energy Diagnostic", "Energy Optimization"], "neo": "simple", "isSchedule": false, "operators": { "$1": "-", "$2": ">=", "$3": ">", "$4": "(", "$5": ")" }, "params": { "#1": "ssh_42.kva" }, "siteId": "ssh", "startNow": true, "maxDataNeeded": "10", "actionable": [{ "title": "yeahyeah", "description": "yeah", "notify": ["<EMAIL>"], "accountable": ["<EMAIL>"], "type": "alert", "priority": 0, "uniqId": "c1a1b558f4786c0656588e13f07738e7" }], "rid": "33e3211f-c48c-46b4-bba9-9a1b8a0789a7", "_userMeta": {"id": "test"} }' 0:1337/recipe`,
  ],

  inputs: {
    rid: {
      type: 'string',
      example: '3410ee75-af92-45b5-b3c1-574d726a3980',
      description: 'Recipe Id required to refrence recipe to update',
      required: true
    },
    label: {
      type: 'string',
      example: 'Name of recipe',
      required: true
      // updatable
    },
    siteId: {
      type: 'string',
      example: 'ssh',
      required: true
      // required to refrence recipe (secondary key)
    },
    type: {
      type: 'string',
      example: 'Comfort',
      required: true,
      isIn: [...utils.RECIPE_TYPES, ...utils.ALERT_TYPES]
    },
    maxDataNeeded: {
      type: 'number',
      example: 20,
      description: 'Data needed of last N minute',
    },
    isSchedule: {
      type: 'string',
      example: 'true/false',
      description: 'true means there is observable false means not'
    },
    actionable: {
      type: ['ref'],
      example: [{
        'title': 'test-1',
        'description': 'none',
        'notify': ['<EMAIL>'],
        'accountable': ['<EMAIL>'],
        'type': 'alert',
        'priority': 0
      }],
      required: true
    },
    recipelabel: {
      type: ['ref'],
      example: ['Energy Diagnostic'],
      description: 'Category this recipe belong to',
      required: true
    },
    neo: {
      type: 'string',
      example: 'chiller',
      description: 'enum of components:[chiller, ahu, coolingTower] to group recipe on this key'
    },
    _userMeta: {
      type: {},
      example: { 'id': 'userName' },
      required: true // this fields auto add authentication
    },
    formula: {
      type: 'string',
      example: '||#1||$3||1',
      description: 'Recipe formula'
    },
    params: {
      type: {},
      example: { '#1': 'ssh_6.ikwtr' }
    },
    operators: {
      type: {},
      example: {
        '$1': '-',
        '$2': '/',
        '$3': '>',
        '$4': '(',
        '$5': ')'
      }
    },
    appType: {
      type: 'string',
      example: 'thermal',
      description: 'Distinguish between a thermostat and a normal recipe'
    },
    misc: {
      type: {},
      example: {
        'appSettings': {},
        'controlSettings': {},
        'dataExpression': {}
      },
      description: 'Special configuration needed for thermostat or any other service in future'
    },
    componentId: {
      type: 'string',
      example: 'ssh_6',
      description: 'Component ID, of the component on which PID is being configured'
    },
    componentsType: {
      type: ['ref'],
      example: ['Heat Pump', 'Chiller'],
      description: 'It is the type of component this recipe belongs to'
    },

  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[recipe > update-recipe] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[recipe > update-recipe] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[recipe > update-recipe] forbidden Request!',
    }
  },
  // exits.serverError("Error ")e
  fn: async function (inputs, exits) {

    /**
     * Keys that are not updateable:
     * appType, rid, siteId, isActive, scheduled, switchOff
     *
     *
     * Logic:
     * update recipe request to controller with setting isSchedule=1
     * on update isSchedule=0 again
     */
    try {
      let {
        rid,
        siteId
      } = inputs;

      let oldRecipeObject = await recipeService.findOne({
        rid,
        siteId
      });
      if (!oldRecipeObject) {
        return exits.badRequest({ problems: ['Logic doesnt exist'] });
      }

      let recipeObject = updateRecipeUtil.buildInitialPacket(inputs, oldRecipeObject);
      if (recipeObject.problems) {
        return exits.badRequest(recipeObject);
      }

      let {
        actionable,
        isSchedule,
        appType,
        componentId
      } = recipeObject;
      let recipeObjectForController;
      let devicesIncluded = utils.getDevicesInsideActionable(actionable);

      if (isSchedule === 'true') {
        // Donot have formula
        let runOn = await recipeService.getRunOn(devicesIncluded, recipeObject.params);
        recipeObject['runOn'] = runOn;
        recipeObject['dependentOnOthers'] = devicesIncluded;
        recipeObjectForController = { ...recipeObject };

      } else if (isSchedule === 'false') {
        // Have formula
        switch (appType) {
          case 'recipe':
            let {
              formula,
              params,
              operators,
              maxDataNeeded,
              runOn = ''
            } = recipeObject;
            recipeObject['oldObservable'] = formula;
            formula = utils.stripFormula(formula);

            let {
              dids,
              devicesToSubscribe,
              parsedFormula,
              runOnServer
            } = await recipeService.v1ParseFormula(formula, params, operators, maxDataNeeded);
            runOn = await recipeService.getRunOn([...dids, ...devicesIncluded], recipeObject.params);

            recipeObject['runOn'] = runOn;
            recipeObject['formula'] = parsedFormula;
            recipeObject['params'] = recipeObject.params;
            recipeObject['operator'] = recipeObject.operators;
            recipeObject['everyMinuteTopics'] = devicesToSubscribe;
            recipeObject['dependentOnOthers'] = dids;

            if (!utils.isFormulaCorrect(recipeObject)) {
              return exits.forbidden({ problems: ['Incorrect formula'] });
            }
            recipeObjectForController = { ...recipeObject };
            break;

          case 'thermal':
            let thermostatConfig = globalHelper.toJson(recipeObject.misc);
            if (!thermostatConfig.controlSettings || !thermostatConfig.appSettings || !thermostatConfig.dataExpression || !componentId) {
              return exits.badRequest({ problems: ['Invalid Thermostat Configuration'] });
            }
            let deviceParamArray = utils.getDeviceParamListFromThermostatExpression(
              thermostatConfig.dataExpression
            );
            let devicesArray = deviceParamArray.map(deviceParam => deviceParam['deviceId']);
            let topicsArray = utils.getTopicsArray(deviceParamArray);
            recipeObject['runOn'] = await self.getThermalRecipeRunOn(
              [...devicesArray, ...devicesIncluded],
              null,
              null,
            );
            recipeObject['everyMinuteTopics'] = globalHelper.toString(topicsArray);
            recipeObject['dependentOnOthers'] = globalHelper.toString(devicesArray);
            recipeObjectForController = utils.parseThermalObjectToSendToController(recipeObject);
            recipeObjectForController['misc'] = globalHelper.toJson(recipeObject['misc']);   // Doing this so that formula is in @|| format
            break;

          default:
            return exits.badRequest({ problems: ['Invalid App Type'] });
        }

        // For appType == 'thermal', if config is updated its the same process,
        // else if we just want to issue an update AHU setpoint, we
        // can create a funcType: updateAhuSetpoint and instead of whole process
        // just update the misc values
        // So in thermal , if update is on controlSettings or appSettings
        // do nothing just call funcType updateSetting and nothing fancy
        // in case of dataExpression, we need to rerun it all

      } else {
        return exits.badRequest({ problems: ['Invalid value of isSchedule'] });
      }

      if (oldRecipeObject['isStage'] === '1' || oldRecipeObject['runOn'] === 'server') {
        // previously logic was only on server not on controller
        let objectToUpdate = { ...recipeObjectForController };
        for (let key in objectToUpdate) {
          objectToUpdate[key] = globalHelper.toString(objectToUpdate[key]);
        }
        delete objectToUpdate.siteId;
        delete objectToUpdate.rid;
        utils.stringifyRecipeToSaveInDB(objectToUpdate); // This can be removed
        await recipeService.update({
          rid,
          siteId
        }, objectToUpdate);
        if (recipeObject?.actionable?.[0]?.type === 'alert') {
          try {
            await syncRecipeInSmartAlert(rid);
          } catch (e) {
            sails.log.error('[Register-Recipe-In-Smart-Alert]');
            sails.log.error(e);
          }
        }
      } else {
        // logic is on some controller
        let oldRun = oldRecipeObject['runOn'];

        // Earlier we were staging the recipe here, now it is been done when the feedback comes from the controller.
        try {
          await recipeService.invokeFunctionOnController(
            recipeObjectForController,
            'updateRecipe',
            oldRun,
            siteId
          );
          return exits.success({ status: 'in-process' });
        } catch (e) {
          // RECOVER PREVIOUS STATE
          // We are actually not able to recover isDeployed in recipe to previous
          // state now because changeDeployedStatusOfSchedulesOfRecipe(recipeObject, '1'); will
          // actually set all schedules of recipe to 1. Maybe some of them are actually not
          // deployed. Way to solve this is using dynamo db transaction
          throw e;
        }
      }
      return exits.success(recipeObject);
    } catch (error) {
      sails.log.error('[recipe > update-recipe]', error);
      if (error?.code?.includes('E_PROCESS_RECIPE_ERROR')) {
        return exits.badRequest({
          message: error?.message,
          code: error?.code,
        });
      }
      return exits.serverError(error);
    }
  }
};

