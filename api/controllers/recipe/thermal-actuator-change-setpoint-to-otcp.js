const RecipeService = require('../../services/recipe/recipe.service')

module.exports = {
    friendlyName: 'changeThermostatModeInOTPC',
    description: 'This api is setting up a flag to control the Temperature setPoint of AHU from OT Control Panel',
    example: [''],
    inputs: {
        componentId: {
            type: 'string',
            required: true,
            example: 'mgch_1',
        },
        siteId: {
            type: 'string',
            required: true,
            example: 'mgch',
        },
        _userMeta: {
            type: 'json',
            required: true,
            example: {
                id: 'userId',
                _role: 'role',
                _site: 'siteId'
            },
            description: 'User meta information added by default to authenticated routes',
        },
        isOTPanelControllingSetpoint: {
            type: 'number',
            required: true,
            isIn: [1, 0],
            description: '1=>Allowing OT panel to change the temperature setpoint,0=>Disabling OT Panel to change the temperature Setpoint'
        }

    },
    exits: {
        serverError: {
            responseType: 'serverError',
            description: '[recipe > changeThermostatModeInOTPC] Server Error!',
        },
        badRequest: {
            responseType: 'badRequest',
            description: '[recipe > changeThermostatModeInOTPC] Bad Request!',
        },
        unauthorized: {
            responseType: 'unauthorized',
            description: '[recipe > changeThermostatModeInOTPC] unauthorized!',
        },
        success: {
            statusCode: 202
        }
    },
    async fn(inputs, exits) {
        try {
            const { componentId, siteId, isOTPanelControllingSetpoint } = inputs
            const response = await RecipeService.sendOTPCConfigToController({ componentId, siteId, isOTPanelControllingSetpoint })
            return exits.success(response)
        } catch (error) {
            if (error && error.hasOwnProperty('HTTP_STATUS_CODE') && error.HTTP_STATUS_CODE === 400) {
                return exits.badRequest({
                    error: error.code,
                    message: error.message
                })
            }
            sails.log.error('[recipe > changeThermostatModeInOTPC] Error!');
            sails.log.error(error);
            return exits.serverError(error);
        }
    },
};
