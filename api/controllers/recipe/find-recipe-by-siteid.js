
const recipeService = require('../../services/recipe/recipe.service.js');
const selfUtils = require('../../utils/recipe/find-recipe.util.js');

module.exports = {
  friendlyName: 'findRecipeBySiteId',
  description: 'Find list of configured recipe for a particular site',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    filter: {
      type: 'json',
      example: 'All the applicable filters'
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[recipe > findRecipeBySiteId] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[recipe > findRecipeBySiteId] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[recipe > findRecipeBySiteId] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try {
      let { siteId } = this.req.params;
      let { filter } = inputs;
      let query = {};
      query['siteId'] = siteId;
      query = {...query, ...filter};
      let recipes = await recipeService.find(query);
      let objectForFrontEnd = {
        'abstract': {},
        'single': {},
        'errors': []
      };
      if (!recipes || recipes.length === 0) {
        return exits.success(objectForFrontEnd);
      }

      let isActiveArray = await Promise.all(recipes.map((eachRecipe) => {
        return recipeService.getRecipeIsActive(eachRecipe['rid']);
      }));

      for (let [index, recipe] of recipes.entries()) {
        let { appType = 'recipe', isSchedule, rid } = recipe;
        recipe['isActive'] = isActiveArray[index];
        if (appType === 'recipe') {
          if (isSchedule === 'true' || isSchedule === 'false') {
            objectForFrontEnd.single[rid] = selfUtils.JSONifyRecipeObjectForFrontEnd(recipe);
          } else {
            objectForFrontEnd.errors.push(recipe);
          }
        } else if (appType === 'thermal') {
          objectForFrontEnd.single[rid] = selfUtils.JSONifyRecipeObjectForFrontEnd(recipe);
        } else {
          objectForFrontEnd.errors.push(recipe);
        }
      }
      return exits.success(objectForFrontEnd);

    } catch (error) {
      sails.log.error('[recipe > findRecipeBySiteId] Error!');
      sails.log.error(error);
      return exits.serverError(error);
    }
  }
};
