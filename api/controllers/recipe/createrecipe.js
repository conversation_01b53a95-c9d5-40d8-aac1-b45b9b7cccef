const utils = require('../../utils/recipe/utils');
const selfUtils = require('../../utils/recipe/createrecipe.util');
const globalHelper = require('../../utils/globalhelper');

const self = require('../../services/recipe/recipe.service');
const { registerRecipeInSmartAlert } = require('../../services/smartAlert/smartAlert.public');
const {
  toJson,
  safeJsonParse
} = require('../../utils/globalhelper');

module.exports = {
  friendlyName: 'Createrecipe',
  description: 'Insert new recipe configration from frontend to database.',

  example: [
    // alert only
    'curl -X POST -H "Content-Type: application/json" --data \'{"siteId":"123","recipelabel":["reciperoutine"],"label":"1","maxDataNeeded":"2","actionable":[{ "title": "test-1", "description": "none", "notify": ["<EMAIL>"], "accountable": ["<EMAIL>"], "type": "alert", "priority": 0 }], "neo":"wowoa", "isSchedule":\'true\', "type":"routine", componentsType:["ahu"], runInterval: 1}\' 0:1337/recipe',
    // formula + alert
    'curl -X POST -H "Content-Type: application/json" --data \'{ "formula": "||#1||$3||1", "label": "test", "type": "nonRoutine", "recipelabel": ["comfort"], "neo": "simple", "isSchedule": \'false\', "operators": { "$1": "-", "$2": "/", "$3": ">", "$4": "(", "$5": ")" }, "params": { "#1": "ssh_6.kvah" }, "siteId": "ssh", "startNow": true, "maxDataNeeded": 1, "actionable": [{ "title": "e", "description": "a", "notify": ["<EMAIL>"], "accountable": ["<EMAIL>"], "type": "alert", "priority": 0 }], componentsType:["ahu"], runInterval: 1 }\' 0:1337/recipe',
    // action only
  ],

  inputs: {
    label: {
      type: 'string',
      example: 'Name of recipe',
      required: true,
    },
    siteId: {
      type: 'string',
      example: 'ssh',
      required: true,
    },
    type: {
      type: 'string',
      example: 'Comfort',
      required: true,
      isIn: [...utils.RECIPE_TYPES, ...utils.ALERT_TYPES],
    },
    maxDataNeeded: {
      type: 'number',
      example: 20,
      description: 'Data needed of last N minute',
      // required: true
    },
    isSchedule: {
      type: 'string',
      example: 'true/false',
      description: 'false means there is observable true means not',
      required: true,
    },
    actionable: {
      type: ['ref'],
      example: [
        {
          title: 'test-1',
          description: 'none',
          notify: ['<EMAIL>'],
          accountable: ['<EMAIL>'],
          type: 'alert',
          priority: 0,
        },
      ],
      required: true,
    },
    recipelabel: {
      type: ['ref'],
      example: ['Energy Diagnostic'],
      description: 'Category this recipe belong to',
      required: true,
    },
    neo: {
      type: 'string',
      example: 'chiller',
      description: 'enum of components:[chiller, ahu, coolingTower] to group recipe on this key',
      required: true,
    },

    _userMeta: {
      type: {},
      example: { id: 'userName' },
      required: true, // this fields auto add authentication
    },
    formula: {
      type: 'string',
      example: '||#1||$3||1',
      description: 'Recipe formula',
    },
    params: {
      type: {},
      example: { '#1': 'ssh_6.ikwtr' },
    },
    operators: {
      type: {},
      example: {
        $1: '-',
        $2: '/',
        $3: '>',
        $4: '(',
        $5: ')',
      },
    },
    appType: {
      type: 'string',
      example: 'thermal',
      description: 'Distinguish between a thermostat and a normal recipe',
    },
    misc: {
      type: {},
      example: {
        appSettings: {},
        controlSettings: {},
        dataExpression: {}
      },
      description: 'Special configuration needed for thermostat or any other service in future',
    },
    componentId: {
      type: 'string',
      example: 'ssh_6',
      description: 'Component ID, of the component on which PID is being configured',
    },
    attachments: {
      type: ['ref'],
      example: ['https://s3.randomurl'],
      description: 'URL of the attachment',
    },
    componentsType: {
      type: ['ref'],
      example: ['Heat Pump', 'Chiller'],
      required: true,
      description: 'It is the type of component this recipe belongs to',
    },
    runInterval: {
      type: 'number',
      example: 30,
      required: true,
      description: 'Time interval at which recipe will run',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[recipe > create-recipe] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[recipe > create-recipe] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[recipe > create-recipe] forbidden Request!',
    },
  },

  async fn(inputs, exits) {
    try {
      const recipeObject = selfUtils.buildInitialPacket(inputs);
      if (recipeObject.problems) {
        return exits.badRequest(recipeObject);
      }
      let {
        actionable,
        isSchedule,
        appType,
        componentId,
        isHypercritical
      } = recipeObject;
      actionable = globalHelper.toArray(actionable);
      if (actionable === undefined || actionable.length == 0) {
        return exits.badRequest({ problems: ['Actionable array cannot be empty'] });
      }
      const devicesIncluded = utils.getDevicesInsideActionable(actionable);
      if (isHypercritical) {
        try {
          await self.hypercriticalAlertConfigModification(actionable);
          recipeObject['actionable'] = JSON.stringify(actionable);
        } catch (error) {
          sails.log.error(
            '[createRecipe] >> Error attaching phone numbers in case of hyper critical alerts!',
            error
          );
        }
      }

      if (isSchedule === 'true') {
        /**Without Formula*/
        const runOn = await self.getRunOn(devicesIncluded, recipeObject.params);
        recipeObject.runOn = runOn;
        recipeObject.dependentOnOthers = globalHelper.toString(devicesIncluded);
      } else if (isSchedule === 'false') {
        /**With Formula*/
        switch (appType) {
          case 'recipe':
            /**Normal Recipe Logic*/
            let {
              formula,
              params,
              operators,
              maxDataNeeded,
              runOn = ''
            } = recipeObject;
            recipeObject.oldObservable = formula;
            formula = utils.stripFormula(formula);

            let {
              dids,
              devicesToSubscribe,
              parsedFormula,
              runOnServer
            } =
              await self.v1ParseFormula(formula, params, operators, maxDataNeeded);

            if (isHypercritical) runOnServer = false;
            runOn = await self.getRunOn([...dids, ...devicesIncluded], recipeObject.params);
            recipeObject.runOn = runOn;
            recipeObject.formula = parsedFormula;
            recipeObject.params = globalHelper.toString(recipeObject.params);
            recipeObject.operator = globalHelper.toString(recipeObject.operators);
            recipeObject.everyMinuteTopics = globalHelper.toString(devicesToSubscribe);
            recipeObject.dependentOnOthers = globalHelper.toString(dids);

            let isFormulaCorrectStatus = utils.isFormulaCorrect(recipeObject, isHypercritical);
            if (!isFormulaCorrectStatus.status) {
              return exits.forbidden({ problems: isFormulaCorrectStatus.problems });
            }
            break;

          case 'thermal':
            /**Thermostat Logic*/
            const thermostatConfig = globalHelper.toJson(recipeObject.misc);
            if (
              !thermostatConfig.controlSettings ||
              !thermostatConfig.appSettings ||
              !thermostatConfig.dataExpression ||
              !componentId
            ) {
              return exits.badRequest({ problems: ['Invalid Thermostat Configuration'] });
            }
            const deviceParamArray = utils.getDeviceParamListFromThermostatExpression(
              thermostatConfig.dataExpression
            );
            const devicesArray = deviceParamArray.map((deviceParam) => deviceParam.deviceId);
            const topicsArray = utils.getTopicsArray(deviceParamArray);
            recipeObject.runOn = await self.getThermalRecipeRunOn(
              [...devicesArray, ...devicesIncluded],
              null,
              null
            );
            recipeObject.everyMinuteTopics = globalHelper.toString(topicsArray);
            recipeObject.dependentOnOthers = globalHelper.toString(devicesArray);
            break;

          default:
            return exits.badRequest({ problems: ['Invalid App Type'] });
        }
      } else {
        return exits.badRequest({ problems: ['Invalid value of isSchedule'] });
      }

      await self.create(recipeObject); // Need to save to Database here.

      if (recipeObject) {
        const actionable = safeJsonParse(recipeObject?.actionable);
        if (actionable?.[0]?.type === 'alert') {
          try {
            await registerRecipeInSmartAlert(recipeObject?.rid);
          } catch (e) {
            sails.log.error('[Register-Recipe-In-Smart-Alert]');
            sails.log.error(e);
          }
        }
        return exits.success(recipeObject);
      }
      return exits.badRequest({ problems: ['Problem saving recipe to database'] });
    } catch (error) {
      sails.log.error('[recipe > create-recipe]', error);
      if (error?.code?.includes('E_PROCESS_RECIPE_ERROR')) {
        return exits.badRequest({
          message: error?.message,
          code: error?.code,
        });
      }
      return exits.serverError(error);
    }
  },
};
