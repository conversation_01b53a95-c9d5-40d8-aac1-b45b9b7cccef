const recipeService = require('../../services/recipe/recipe.service');
const globalhelper = require('../../utils/globalhelper');
const {
  registerRecipeInSmartAlert,
  deleteRecipeFromSmartAlert
} = require('../../services/smartAlert/smartAlert.public');
const { safeJsonParse } = require('../../utils/globalhelper');

module.exports = {
  friendlyName: 'deleteRecipe',
  description: '',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    siteId: {
      type: 'string',
      example: 'Id of site',
      required: true
    },
    rid: {
      type: 'string',
      example: '3410ee75-af92-45b5-b3c1-574d726a3980',
      description: 'Unique recipe ID of the recipe',
      required: true
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[recipe > deleteRecipe] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[recipe > deleteRecipe] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[recipe > deleteRecipe] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try {
      let {
        siteId,
        rid
      } = inputs;
      let recipe = await recipeService.findOne({
        rid,
        siteId
      });

      if (!recipe) {
        return exits.badRequest({ problems: ['Recipe not found'] });
      }
      let {
        runOn,
        isStage,
        scheduled
      } = recipe;

      if (isStage === '1' || runOn === 'server' || runOn === undefined) {
        scheduled = globalhelper.toArray(scheduled);
        scheduled = scheduled ? scheduled : [];
        const actionable = safeJsonParse(recipe?.actionable);
        if (actionable?.[0]?.type === 'alert') {
          try {
            await deleteRecipeFromSmartAlert(rid);
          } catch (e) {
            sails.log.error('[Delete-Recipe-From-Smart-Alert]');
            sails.log.error(e);
          }
        }
        await recipeService.deleteSchedules(rid, scheduled);
        await recipeService.destroy({
          rid,
          siteId
        });
        return exits.success({ 'status': 'ok' });
      } else {
        await recipeService.invokeFunctionOnController(
          { rid },
          'deleteRecipe',
          runOn,
          siteId
        );
        return exits.success({ 'status': 'in-process' });
      }

    } catch (error) {
      sails.log.error('[recipe > deleteRecipe] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
