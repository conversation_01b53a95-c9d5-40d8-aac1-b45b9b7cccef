
const recipeService = require('../../services/recipe/recipe.service');
const eventService = require('../../services/event/event.public');
const globalHelper = require('../../utils/globalhelper');

module.exports = {
  friendlyName: 'pauseRecipe',
  description: 'Pausing a recipe for a certain amount of time',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    siteId: {
      type: 'string',
      example: 'ssh',
      required: true
    },
    rid: {
      type: 'string',
      exmaple: '04772c0d-303a-498f-86bd-3568a4fe41ea',
      required: true
    },
    from: {
      type: 'string',
      example: '2018-12-10 12:00',
      required: true
    },
    to: {
      type: 'string',
      example: '2018-12-10 12:00',
      required: true
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[recipe > pauseRecipe] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[recipe > pauseRecipe] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[recipe > pauseRecipe] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {

    try {
      let { rid, siteId, from, to } = inputs;
      let recipe = await recipeService.findOne({ rid, siteId });

      if (!recipe) {
        return exits.forbidden({ problems: ['Recipe doesn\'t exist'] });
      }

      let { runOn, isStage } = recipe;
      if (isStage === '1') {
        return exits.badRequest({ problems: ['Recipe is not staged'] });
      }

      try{
        from = globalHelper.formatDateTime(from);
        to = globalHelper.formatDateTime(to);
      } catch (e) {
        sails.log.error('Error in pause-recipe: ' + e);
        return exits.forbidden({ problems: ['Invalid time format'] });
      }
      let fromTo = [from, to];
      let updateObject = {
        func: 'pauseRecipe',
        operation: 'recipeControl',
        data: {
          rid,
          fromTo
        }
      };
      let topic = `${siteId}/config/${runOn}/recipecontrol`;
      await eventService.publish(topic, JSON.stringify(updateObject));

      return exits.success({'status': 'in-process'});

    } catch (error) {
      sails.log.error('[recipe > pause-recipe] Error!');
      sails.log.error(error);
      return exits.serverError(error);
    }
  }
};
