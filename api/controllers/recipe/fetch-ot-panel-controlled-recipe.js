const RecipeService = require('../../services/recipe/recipe.service')
module.exports = {
    friendlyName: 'fetchThermostatRecipeInOTCM',
    description: '',
    inputs: {
        siteId: {
            type: 'string',
            example: 'Id of site',
            required: true
        },
        componentId: {
            type: 'string',
            example: 'mgch_1',
            description: 'criticalAHU and ahu component Id',
            required: true
        },
    },

    exits: {
        serverError: {
            responseType: 'serverError',
            description: '[recipe > deleteRecipe] Server Error!',
        },
        badRequest: {
            responseType: 'badRequest',
            description: '[recipe > deleteRecipe] Bad Request!',
        },
        forbidden: {
            responseType: 'forbidden',
            description: '[recipe > deleteRecipe] forbidden Request!',
        },
    },

    fn: async function ({ componentId, siteId }, exits) {
        try {
            const activeOTPanelRecipeList = await RecipeService.fetchOTControlPanelModeRecipe({ componentId, siteId })
            return exits.success(activeOTPanelRecipeList.map(it=>it.rid))
        } catch (error) {
            sails.log.error('[recipe > deleteRecipe] Error!');
            sails.log.error(error);
            exits.serverError(error);
        }
    }
};
