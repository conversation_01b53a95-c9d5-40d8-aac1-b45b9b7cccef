const CommandRetention = require('../../services/recipe/lib/CommandRetention');
const { validateFetchCommandRetentionInput } = require('../../utils/recipe/command-retention.util');

module.exports = {
  friendlyName: 'Fetch command retention',
  description: 'Fetch command retention',
  example: [
    
  ],
  inputs: {
   siteId: {
    type: 'string',
   },
   componentId: {
    type: 'string',
   },
  },


  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[command-retention > Fetch-command-retention] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[command-retention > Fetch-command-retention] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[command-retention > Fetch-command-retention] forbidden Request!',
    }
  },

  fn: async function (inputs, exits) {
    try {
      const {
        siteId,
        componentId,
      } = inputs;
      await validateFetchCommandRetentionInput({siteId, componentId})

      const commandRN = new CommandRetention();
      commandRN.setSiteId(siteId)
      await commandRN.setAssetInfoById(componentId)
      const response = await commandRN.fetch();
      return exits.success(response);
    } catch (e) {
      if (e.HTTP_STATUS_CODE == 400) {
          return exits.badRequest({
          err: e.message
        });
      } else {
        sails.log.error(e);
        return exits.serverError({
          err: 'Server has encountered an issue.Please contact the administrator'
        });
     }
  }
  }
};
