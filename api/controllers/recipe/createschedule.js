const selfUtils = require('../../utils/recipe/createschedule.util');
const utils = require('../../utils/recipe/utils');
const globalHelpers = require('../../utils/globalhelper');

const recipeService = require('../../services/recipe/recipe.service');

module.exports = {


  friendlyName: 'Createschedule',
  description: 'create a schedule/timing to run the recipe from time1 to time2.',

  example: [
    `curl -X POST -H "Content-Type: application/json" --data '{"rid": "3410ee75-af92-45b5-b3c1-574d726a3980",	"startDate": "2019-02-24", "endDate": "2019-12-24", "startTime": "00:00", "endTime": "22:30", "allDay": true, "repeatInterval": "daily", "siteId": "ssh" }' 0:1337/m2/recipe/v2/schedule`,
    `curl -X POST -H "Content-Type: application/json" --data '{"rid": "3410ee75-af92-45b5-b3c1-574d726a3980",      "startDate": "2019-02-24", "endDate": "2019-12-24", "startTime": "00:00", "endTime": "22:30", "allDay": true, "repeatInterval": "custom", "siteId": "ssh","customDays":["mon","fri"] }' 0:1337/m2/recipe/v2/schedule`
  ],

  inputs: {
    rid: {
      type: 'string',
      example: '3410ee75-af92-45b5-b3c1-574d726a3980',
      description: 'Unique recipe ID of the recipe',
      required: true
    },
    startDate: {
      type: 'string',
      example: '2019-02-24',
      description: 'The start date of the recipe',
      required: true
    },
    endDate: {
      type: 'string',
      example: '2019-12-24',
      description: 'The end date of the recipe',
      required: true
    },
    startTime: {
      type: 'string',
      example: '00:00',
      description: 'The start time of the recipe',
      required: true
    },
    endTime: {
      type: 'string',
      example: '22:30',
      description: 'The end time of the recipe',
      required: true
    },
    allDay: {
      type: 'boolean',
      example: true,
      description: 'Whether to run it all day from 00:01 to 23:59 or not',
      required: true
    },
    customDays: {
      type: ['ref'],
      example: ['mon', 'fri', 'sat'],
      custom: selfUtils.isValidDays,
      description: 'If repeatInterval=custom, this is array of days',
    },
    repeatInterval: {
      type: 'string',
      example: 'daily',
      isIn: ['daily', 'dailySlot', 'custom'],
      description: 'Whether it should run daily or on weekdays from stime to etime',
      required: true
    },
    siteId: {
      type: 'string',
      example: 'ssh',
      description: 'The site ID of the recipe',
      required: true
    }
  },


  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[recipe > create-schedule] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[recipe > create-schedule] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[recipe > create-schedule] forbidden Request!',
    }
  },
  // exits.serverError("Error ")

  fn: async function (inputs, exits) {
    // req.body = {
    // 	"rid": "3410ee75-af92-45b5-b3c1-574d726a3980",
    // 	startDate: "2019-02-24",
    // 	endDate: "2019-12-24",
    // 	startTime: "00:00",
    // 	endTime: "22:30",
    // 	allDay: true, // just used to set start time = 00:00 and endtime to 23:59 thats it not to confused with repeat_type's allDay
    // 	// true means we will give stime and etime

    // 	repeatInterval: 'daily', // tells if one time schdule or will it be repeating more than 1 day
    // 	// vals can be daily meaning from start time to end time daily
    // 	// daily means daily strt time to end time
    // 	// weekdays meaning MON-SAT from start time to end time
    // 	// weekends meaning SAT-SUN from start time to end time
    // 	// custom meaning value-came-from-FE from start time to end time
    // 	siteId: 'ssh'
    // };

    try {

      let { rid, startDate, endDate, startTime, cronTabs = [],
        endTime, allDay, siteId, repeatInterval, customDays } = inputs;

      // Check if not valid date/time is given
      startDate = selfUtils.isValidDate(startDate);
      endDate = selfUtils.isValidDate(endDate);
      if (!startDate || !endDate) {
        return exits.badRequest({ problems: ['Wrong Date format'] });
      }
      if (!selfUtils.validTime(startTime)
        || !selfUtils.validTime(endTime)) {
        return exits.badRequest({ problems: ['Wrong Time'] });
      }
      if (repeatInterval === 'custom' && customDays === undefined) {
        return exits.badRequest({ problems: ['Invalid customDays'] });
      }

      // startDate =
      //   endDate =

      // Business Logic
      switch (repeatInterval) {
        case 'dailySlot':
          cronTabs = selfUtils.createNoIntervalCron(startDate, endDate, startTime, endTime);
          break;
        case 'daily':
          cronTabs = selfUtils.createDailyCron(startDate, endDate, startTime, endTime);
          break;
        case 'custom':
          cronTabs = selfUtils.createCustomCron(startDate, endDate, startTime, endTime, customDays);
          break;
        default:
          return exits.badRequest({ problems: ['Invalid repeat interval'] });
      }

      let schedule = {
        sid: utils.uuid(),
        rid,
        siteId,
        repeat_type: repeatInterval,
        ts: JSON.stringify(cronTabs),
        schedule: allDay
      };

      let createdSchedule = await recipeService.createScheduleForRecipe(schedule);
      if (createdSchedule.problems) {
        return exits.badRequest(createdSchedule);
      } else {
        return exits.success(createdSchedule);
      }

    } catch (e) {
      sails.log.error(e);
      return exits.serverError(e);
    }
  }
};
