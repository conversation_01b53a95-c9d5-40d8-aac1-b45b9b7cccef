const RecipeService = require('../../services/recipe/recipe.service')

module.exports = {
    friendlyName: 'changeThermostatActuatorSetPoint',
    description: 'This api can change the setpoint of running recipe',
    example: [''],
    inputs: {
        rid: {
            type: 'string',
            required: true,
            example: 'uuid',
        },
        setPoint: {
            type: 'number',
            required: true,
            example: 18,
        },
        siteId: {
            type: 'string',
            required: true,
            example: 'suh-hyd',
        },
        componentId: {
            type: 'string',
            required: true,
            example: 'suh-hyd_37',
        },
        _userMeta: {
            type: 'json',
            required: true,
            example: {
                id: 'userId',
                _role: 'role',
                _site: 'siteId'
            },
            description: 'User meta information added by default to authenticated routes',
        },
    },
    exits: {
        serverError: {
            responseType: 'serverError',
            description: '[recipe > changeThermostatActuatorSetPoint] Server Error!',
        },
        badRequest: {
            responseType: 'badRequest',
            description: '[recipe > changeThermostatActuatorSetPoint] Bad Request!',
        },
        unauthorized: {
            responseType: 'unauthorized',
            description: '[recipe > changeThermostatActuatorSetPoint] unauthorized!',
        },
        success: {
            statusCode: 202
        }
    },
    async fn(inputs, exits) {
        try {
            const { rid, setPoint,siteId,componentId } = inputs
            const response = await RecipeService.initiateThermostatActuatorSetPointChangeRequest({ rid, setPoint, siteId, componentId })
            return exits.success({ rid: response })
        } catch (error) {
            if (error && error.hasOwnProperty('HTTP_STATUS_CODE') && error.HTTP_STATUS_CODE === 400) {
                return exits.badRequest({
                    error: error.code,
                    message: error.message
                })
            }
            sails.log.error('[recipe > changeThermostatModeInOTPC] Error!');
            sails.log.error(error);
            return exits.serverError(error);
        }
    },
};
