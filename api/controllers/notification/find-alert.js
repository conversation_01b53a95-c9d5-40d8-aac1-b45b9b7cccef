
const notificationService = require('../../services/notification/notification.service');
const globalHelpers = require('../../utils/globalhelper');
const selfUtils =  require('../../utils/notification/find-alert.util');

module.exports = {
  friendlyName: 'findAlert',
  description : 'Find alerts with various filters like get site"s alert, users alert etc',
  example: [
    `curl "0:1337/m2/notification/v2/notification/?siteId=xyz&from=2020-11-10%2012:00&to=2020-11-10%2012:01&userId=<EMAIL>"`,
  ],

  inputs: {
    siteId: {
      type: 'string',
      description: 'Unique site id',
      example: 'ssh',
      required: true
    },
    userId: {
      type: 'string',
      description: 'user id',
      example: '<EMAIL>',
    },
    from: {
      type: 'string',
      description: 'timestamp to get alerts from',
      example: '2020-11-10 00:01',
      custom: globalHelpers.isValidDateTime,
      required: true
    },
    to: {
      type: 'string',
      description: 'end time to get alerts till',
      example: '2020-11-10 23:59',
      custom: globalHelpers.isValidDateTime,
      required: true
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[notification > findAlert] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[notification > findAlert] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[notification > findAlert] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      let { siteId, userId, from, to } = inputs;
      let id = userId !== undefined ? `${siteId}_${userId}` : siteId;
      from = globalHelpers.toUnixTimeStamp(from);
      to = globalHelpers.toUnixTimeStamp(to);
      let compositeKey = {id, timestamp: {in: [from, to]}};

      let notifications = await notificationService.find(compositeKey);
      let formattedNotification = selfUtils.formatNotifications(notifications);

      return exits.success(formattedNotification);

    } catch(error) {
      sails.log.error('[notification > findAlert] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
