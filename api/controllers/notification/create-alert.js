const notificationService = require('../../services/notification/notification.service');
const globalhelper = require('../../utils/globalhelper');
const utils = require('../../utils/notification/utils.js');

module.exports = {
  friendlyName: 'createAlert',
  description: 'Create a new recipe/nudge or any type of alert. It adds alert to notification table and send sms/mail & notification',
  example: [
    'curl -X POST 0:1337/m2/notification/v2/notification --data \'{ "siteId": "xyz", "timestamp": "2020-11-10 12:00", "title": "xyz", "description": "xyz", "category": ["xyz"], "notify": ["<EMAIL>"], "alertId": "xyz", "sms": ["<EMAIL>"], "sourceId": "xyz", "priority": 2, "processes": ["xyz"], "type": "RECIPE" }\' -H "Content-Type: application/json"',
    'curl "0:1337/m2/notification/v2/notification"  --data \'{"siteId": "gknmh", "timestamp": "2021-01-27 17:45:24", "title": "DataNotComing", "description": "Data not coming for component type : chiller and Device Id : gknmh_13.", "sourceId": "gknmh_nudge", "alertId": "gknmh_DataNotComing_1820", "type": "NUDGE", "notify": ["<EMAIL>"], "sms": [], "email": [], "category": []}\'',
  ],

  inputs: {
    siteId: {
      type: 'string',
      description: 'Unique site id',
      example: 'Id of site',
      required: true,
    },
    timestamp: {
      type: 'string',
      description: 'Time of notification',
      example: '2020-10-10 12:23',
      custom: globalhelper.isValidDateTime,
      required: true,
    },
    type: {
      type: 'string',
      description: 'type of alert, ie recipe, nudge etc',
      example: 'RECIPE',
      required: true,
      isIn: utils.TYPES_OF_NOTIFICATION,
    },
    title: {
      type: 'string',
      description: 'title of the alert',
      example: 'This is recipe alert from xyz',
      required: true,
    },
    description: {
      type: 'string',
      description: 'description of the alert',
      example: 'This is alert description',
      required: true,
    },
    notify: {
      type: ['ref'],
      description: 'userIds of users to email and show dejoule notification of',
      example: ['<EMAIL>'],
      required: true,
    },
    sms: {
      type: ['ref'],
      description: 'userIds of users to send sms to',
      example: ['<EMAIL>'],
    },
    alertId: {
      type: 'string',
      description: 'Id of alert unique to alert',
      example: '912a-ew23qe-132123-121',
      required: true,
    },
    sourceId: {
      type: 'string',
      description: 'Id of source of alert. Like recipe id or nudge id',
      example: 'aaaa-bbbbb-2324-112313',
      required: true,
    },
    category: {
      type: ['ref'],
      description: 'Category this alert belongs to',
      example: ['datanotcoming', 'maintenance'],
    },
    priority: {
      type: 'string',
      description: 'Priority of alert, required for recipe',
      example: 'high',
      isIn: ['critical', 'high', 'medium', 'low', '0', '1', '2', '3'],
    },
    processes: {
      type: ['ref'],
      description: 'Components/proceeses this alert belong to',
      example: ['ssh_1', 'ssh_3'],
    },
    extra: {
      type: 'json',
      description: 'Any extra keys you need in email/sms. send here',
      example: { name: 'Alert name is something', siteName: 'st. stephan' },
    },
    secret: {
      type: 'string',
      description: 'Secret key required to create alert by other services',
      example: '612fc5c443dbf6db91c',
      required: true,
      isIn: ['ceb98dc7c1373c80a4967d6ce176a9966d3e834b'],
    },

  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[notification > createAlert] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[notification > createAlert] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[notification > createAlert] forbidden Request!',
    },
  },

  async fn(inputs, exits) {
    try {
      const { type, timestamp } = inputs;
      inputs.timestamp = globalhelper.toUnixTimeStamp(timestamp);

      if (type === 'RECIPE') {
        if (inputs.priority === undefined) {
          return exits.badRequest({ problems: ['Missing param priority'] });
        }
        await notificationService.addAlertToRecipeQueue(inputs);
      } else if (type === 'NUDGE') {
        await notificationService.addAlertToNudgeQueue(inputs);
      } else {
        return exits.badRequest({ problems: ['Invalid request type'] });
      }
      return exits.success('Added');
    } catch (error) {
      sails.log.error('[notification > createAlert] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  },
};
