
const energyConsumptionService = require('../../services/energyConsumption/energyConsumption.service');
const globalHelper = require('../../utils/globalhelper');
const selfUtils = require('../../utils/energyConsumption/consumption-page.util.js');
// const utils = require('../../utils/energyConsumption/utils.js');
const userSiteMapService = require('../../services/userSiteMap/userSiteMap.public');
const dynamokeystorePublic = require('../../services/dynamokeystore/dynamokeystore.public');

module.exports = {
  friendlyName: 'consumptionPage',
  description : '',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    groupBy: {
      type: 'string',
      example: 'day, week, month',
      description: 'Group name',
      isIn: ['hour','days', 'weeks', 'months', 'custom'],
      required: true,
    },
    start: {
      type: 'string',
      description: 'start timestamp',
    },
    end: {
      type: 'string',
      description: 'end timestamp',
    },
    isDashboardAPI: {
      type: 'boolean',
      description: 'Value set to true.',
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[energyConsumption > consumptionPage] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[energyConsumption > consumptionPage] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[energyConsumption > consumptionPage] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      const {
        _userMeta: { _site: siteId, id: userId, unitPref: unitPreferences }, groupBy, start, end, isDashboardAPI
      } = inputs;
      let emList;
      let emListFromDyanmoKeyStore, $updateUserPreferenceWithEmList;
      if(isDashboardAPI){
        // API being called from dashboard. Fetching emList from dynamokeystore
        emList = await dynamokeystorePublic.fetchEmList(siteId);
      } else {
        // API being
        //Fetch EM list from user preference
        const emListFromUserPreference = await userSiteMapService.fetchEmList(userId, siteId);
        // const emListFromUserPreference = ["4375"]; // TODO: Used for testing specific deviceIds. To be removed.

        if(!emListFromUserPreference){
          // If EM list not present fetch from dyanmokeystore and update user preference
          emListFromDyanmoKeyStore = await dynamokeystorePublic.fetchEmList(siteId);
          if(emListFromDyanmoKeyStore)
            $updateUserPreferenceWithEmList = userSiteMapService.updateEmList(userId, siteId, emListFromDyanmoKeyStore)
          emList = emListFromDyanmoKeyStore;
        } else emList = emListFromUserPreference;
      }

      // Generate start and end time.
      const generateTimeObject = selfUtils.generateStartAndEndTime(groupBy, start, end);
      if(generateTimeObject.status == false) return exits.badRequest({
        status: false,
        problems: generateTimeObject.problems
      });
      const { startTime, endTime } = generateTimeObject;

      // Calculate consumption for all energy meters.
      let $consumptionPromises = emList.map(deviceId => energyConsumptionService.fetchEnergyConsumptionForEM(deviceId, startTime, endTime));
      let consumptionPromises = await Promise.allSettled($consumptionPromises);
      let responseObject = selfUtils.formatResponseObject(consumptionPromises, unitPreferences);

      exits.success(responseObject);
      await $updateUserPreferenceWithEmList;
    } catch(error) {
      sails.log.error('[energyConsumption > consumptionPage] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
