
const energyConsumptionService = require('../../services/energyConsumption/energyConsumption.service');
const globalHelper = require('../../utils/globalhelper');
const selfUtils = require('../../utils/energyConsumption/monthly-consumption.util.js');
const baselineService = require("../../services/baseline/baseline.service");
const dyanmoKeyStoreService = require("../../services/dynamokeystore/dynamokeystore.service");

module.exports = {
  friendlyName: 'monthlyConsumption',
  description : '',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    siteId: {
      type: 'string',
      example: "mgch",
      description: 'SiteId',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[energyConsumption > monthlyConsumption] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[energyConsumption > monthlyConsumption] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[energyConsumption > monthlyConsumption] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      const {
        _userMeta: { unitPref: unitPreferences }, siteId
      } = inputs;

      // Generate Time Objects
      const timeObjects = selfUtils.generateStartAndEndTime();
      let { startTime, endTime } = timeObjects;

      // Fetch current baseline and init startTime if it exists
      try {
        const currentBaseline = await baselineService.getCurrentBaseline(siteId, endTime);
        if (currentBaseline.hasOwnProperty('startDate')) {
          startTime = currentBaseline.startDate
        }
      } catch(e) {
        sails.log.info('Error > monthlyConsumption fetching current baseline', e)
      }

      // Fetch main meters
      const mainMeterSet = await dyanmoKeyStoreService.findSitesMainMeterSet(siteId);
      const mainMeterList = Array.from(mainMeterSet);
      if(!mainMeterSet || mainMeterList.length == 0) {
        sails.log.info(`[energyConsumption > monthlyConsumption] siteId=${siteId} userId=${inputs._userMeta.id} message="Unable to find mainMeter list for site" mainMeterKey=${ siteId }_mainMeter`);
        return exits.badRequest({
        "message": "Unable to find mainMeter list for site"
      })
    };

      // Calculate consumption
      let $consumptionPromises = mainMeterList.map(deviceId => energyConsumptionService.fetchEnergyConsumptionForEM(deviceId, startTime, endTime));
      let consumptionPromises = await Promise.allSettled($consumptionPromises);
      let responseObject = selfUtils.formatResponseObject(consumptionPromises, unitPreferences);
      return exits.success(responseObject);
    } catch(error) {
        sails.log.error(`[energyConsumption > monthlyConsumption] Error! siteId: ${inputs.siteId} userId=${inputs._userMeta.id}`);
        sails.log.error(error);
        exits.serverError(error);
      }
  }
};
