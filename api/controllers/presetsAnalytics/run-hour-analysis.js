const presetAnalyticsService = require('../../services/presetAnalytics/presetsAnalytics.service')
module.exports = {
  friendlyName: "runHourAnalysis",
  description: "",
  example: [`curl -X GET "http://localhost:1337/`],

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
      description: "User meta information added by default to authenticated routes",
    },
    startTime: {
      type: "string",
      required: true,
      description: "Start time in YYYY-MM-DD format",
    },
    endTime: {
      type: "string",
      required: true,
      description: "Start time in YYYY-MM-DD format",
    },
    groupBy: {
      type: "string",
      required: true,
      description:
        "group by used to aggregate the result. Allowed values are d,h",
    },
    deviceIds: {
      type: "string",
      required: true,
      description: "list of component",
    },
    graphType: {
      type: "string",
      description: "type of graph for response object",
      defaultsTo: "histogram"
    }
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[datadevice > runHourAnalysisHighSide] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[datadevice > runHourAnalysisHighSide] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[datadevice > runHourAnalysisHighSide] forbidden Request!",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const {
        _userMeta: { _site: siteId },
        startTime,
        endTime,
        groupBy,
        deviceIds,
        graphType
      } = inputs;
      let filter = {
        startTime:parseInt(startTime),
        endTime:parseInt(endTime),
        groupBy,
        assetList: deviceIds.split(",")
      }
      let presetResult = await presetAnalyticsService.runHourAnalysis(siteId, filter, graphType)
      return exits.success(presetResult)
    } catch (error) {
      sails.log.error("[datadevice > runHourAnalysisHighSide] Error!");
      sails.log.error(error);
      exits.serverError(error);
    }
  },
};
