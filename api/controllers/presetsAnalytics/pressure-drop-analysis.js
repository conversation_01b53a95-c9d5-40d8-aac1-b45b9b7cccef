const presetAnalyticsService = require('../../services/presetAnalytics/presetsAnalytics.service')
module.exports = {
  friendlyName: "pressureDropAnalysis",
  description: "",
  example: [`curl -X GET "http://localhost:1337/`],

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
      description: "User meta information added by default to authenticated routes",
    },
    startTime: {
      type: "string",
      required: true,
      description: "Start time in YYYY-MM-DD format",
    },
    endTime: {
      type: "string",
      required: true,
      description: "Start time in YYYY-MM-DD format",
    },
    groupBy: {
      type: "string",
      required: true,
      description:
        "group by used to aggregate the result. Allowed values are d,h",
    },
    deviceIds: {
      type: "string",
      required: true,
      description: "list of component",
    },
    graphType: {
      type: "string",
      description: "type of graph for response object",
      defaultsTo: "histogram"
    }
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[datadevice > pressureDropAnalysis] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[datadevice > pressureDropAnalysis] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[datadevice > pressureDropAnalysis] forbidden Request!",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const {
        _userMeta: { _site: siteId, unitPref: unitPreference },
        startTime,
        endTime,
        groupBy,
        deviceIds,
        graphType
      } = inputs;
      const pressureDropUnit = unitPreference.pressure || 'kPa';

      let filter = {
        startTime,
        endTime,
        groupBy,
        assetList: deviceIds.split(",")
      }
      let presetResult = await presetAnalyticsService.pressureDropAnalysis(siteId, filter,pressureDropUnit,graphType)
      return exits.success(presetResult)
    } catch (error) {
      sails.log.error("[datadevice > pressureDropAnalysis] Error!");
      sails.log.error(error);
      exits.serverError(error);
    }
  },
};
