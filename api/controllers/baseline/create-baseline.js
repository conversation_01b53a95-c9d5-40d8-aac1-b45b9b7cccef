const flaverr = require('flaverr');
const baselineService = require('../../services/baseline/baseline.service');
const baselineUtils = require('../../utils/baseline/utils');

module.exports = {
  friendlyName: 'createBaseline',
  description: 'Create Basline for 0th year of project.Users Manually enter baseline\'s for 0th year and dejoule uses this to show baseline for current year',
  example: [
    'curl -X POST "http://localhost:1337/m2/baseline/v2/baseline" -H "Content-Type: application/json" --data \'{"baselines":[{"siteId":"ssh","startDate":"2016-04-14","consumptionValue":1337,"endDate":"2016-05-13","target":30}]}\'',
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    },
    baseline: {
      type: 'json',
      required: true
    },
    siteId: {
      type: 'string',
      example: 'sjo-del'
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[baseline > createBaseline] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[baseline > createBaseline] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[baseline > createBaseline] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[baseline > createBaseline] Not Found'
    },
    success: {
      statusCode: 201,
      description: '[baseline > createBaseline] Success'
    }
  },

  async fn(inputs, exits) {
    try {
      let {baseline, siteId} = inputs;
      baselineUtils.baselineInputValidation(inputs.baseline)
      const baselineData = await baselineService.addOrUpdateBaselineCycle(siteId, baseline)
      return exits.success(baselineData);
    } catch (error) {
      switch (error.code) {
        case 'INVALID_BASELINE_ROW_EXIST':
        case 'INCONSISTENT_SITE_ID_NOT_ALLOWED':
        case 'E_PREV_BASELINE_NOT_FOUND':
        case 'MORE_THAN_12_BASELINE_NOT_ALLOWED':
        case 'OVERLAPPED_BASELINE_ROW_DETECTED': {
            return exits.badRequest({ err: error.message, data: error.raw });
        }


        case 'E_INVALID_DATE':
        case 'E_CYCLE_DAYS_EXCEEDED':
        case 'E_INPUT_VALIDATION':
        case 'E_DAYS_LIMIT_EXCEEDED': {
          return exits.badRequest({ err: error.message });
        }

        case 'E_SITE_NOT_FOUND': {
          return exits.notFound({ err: error.message });
        }


        case 'E_FETCH_QUERY_FAILED':
        case 'E_QUERY_ERROR': {
          return exits.forbidden({
            err: error.message
          })
        }

        case 'E_BASELINE_NOT_SAVED': {
          return exits.forbidden({ err: error.message, data: error.raw });
        }
        default: {
          sails.log.error('[baseline > createBaseline] Error!');
          sails.log.error(error);
          return exits.serverError(error);
        }
      }
    }
  },
};
