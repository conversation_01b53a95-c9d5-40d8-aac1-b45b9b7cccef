const baselineService = require('../../services/baseline/baseline.service');
const globalHelper = require('../../utils/globalhelper');
const utils = require('../../utils/baseline/utils.js');
const siteService = require('../../services/site/site.service');

module.exports = {
  friendlyName: 'findTargets',
  description: 'This API is to populate dynamic targets in calendar when user visit dynamic target page',
  example: [
    'curl "0:1337/m2/baseline/v2/dynamicTargets?date=2021-03-10"',
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    date: {
      type: 'string',
      required: true,
      example: '2021-10-10',
      custom: globalHelper.isValidDateTime,
      description: 'Date in baseline to get targets of',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[baseline > findTargets] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[baseline > findTargets] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[baseline > findTargets] forbidden Request!',
    },
  },

  async fn(inputs, exits) {
    try {
      const { _site: siteId } = inputs._userMeta;
      const { date } = inputs;

      const siteContumptionUnit = await siteService.getSitesConsumptionUnit(siteId);
      const baseline = await baselineService.getCurrentBaseline(siteId, date);

      const adjustments = await baselineService.Adjustment.find({
        siteId,
        timestamp: {
          in: [baseline.startDate, baseline.endDate],
        },
      });

      const previousAdjustment = await baselineService.Adjustment.findOne({
        siteId,
        timestamp: {
          '<': baseline.startDate,
        },
        sort: 'timestamp DESC',
      });

      const monthsBaselineAfterAdjustments = utils
        .getMonthsBaselineAfterAdjustmentsInPrefferedUit(
          baseline,
          previousAdjustment,
          adjustments,
          siteContumptionUnit,
          siteContumptionUnit,
        );
      const targets = await baselineService.getTargetConsumptionBetween2TSInPreferredUnit(
        siteId,
        baseline,
        monthsBaselineAfterAdjustments,
        siteContumptionUnit,
        siteContumptionUnit,
      );

      return exits.success(targets);
    } catch (error) {
      if (error.code === 'E_BASELINE_NOT_FOUND') {
        return exits.badRequest({
          problems: [error.message],
        })
      } else if (error.code === 'E_MULTIPLE_BASELINE_EXIST') {
        const {raw: baselineOverlapping, message} = error;
        return exits.badRequest({
          problems: [message],
          data: baselineOverlapping
        })
      } else {
      sails.log.error('[baseline > findTargets] Error!');
      sails.log.error(error);
      exits.serverError(error);
      }
    }
  },
};
