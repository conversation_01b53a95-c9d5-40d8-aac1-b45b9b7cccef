const baselineService = require('../../services/baseline/baseline.service');
const globalHelper = require('../../utils/globalhelper');

module.exports = {
  friendlyName: 'createAdjustment',
  description: 'Add new adjustment',
  example: [
    'curl  -X POST "0:1337/m2/baseline/v2/adjustment" --data \'{"adjustmentvalue":-1200}\' -H "Content-Type: application/json"',
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    },
    adjustmentvalue: {
      type: 'number',
      required: true,
      example: -20000,
      description: 'adjustment value positive/negative which shows the amount of adjustment',
    },
    description: {
      type: 'string',
      description: 'Information about this adjustment',
      example: 'This is adding 10new AC',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[baseline > createAdjustment] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[baseline > createAdjustment] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[baseline > createAdjustment] forbidden Request!',
    },
  },

  async fn(inputs, exits) {
    try {
      const {
        _userMeta: { _site: siteId },
        adjustmentvalue,
        description,
      } = inputs;

      const today = globalHelper.toMoment().format('YYYY-MM-DD HH:mm:ss');
      const adjustment = {
        siteId,
        timestamp: today,
        relative: adjustmentvalue,
        description,
      };

      const previousKnwonAdjustment = await baselineService.Adjustment.findOne({
        siteId,
        timestamp: {
          '<=': today,
        },
        sort: 'timestamp DESC',
      });

      if (previousKnwonAdjustment !== undefined) {
        adjustment.absolute = previousKnwonAdjustment.absolute + adjustmentvalue;
      } else {
        adjustment.absolute = adjustmentvalue;
      }

      await baselineService.Adjustment.create(adjustment);

      return exits.success(adjustment);
    } catch (error) {
      sails.log.error('[baseline > createAdjustment] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  },
};
