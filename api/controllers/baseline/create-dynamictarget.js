const baselineService = require('../../services/baseline/baseline.service');
const globalHelper = require('../../utils/globalhelper');
const utils = require('../../utils/baseline/utils.js');

module.exports = {
  friendlyName: 'createDynamictarget',
  description: 'This function is to create dynamic target in a baseline. Targets are entered in site consumption unit',
  example: [
    'curl 0:1337/m2/baseline/v2/dynamicTarget -X POST --data \'{"targets":[ { "date": "2021-03-03", "actualTarget": 4000, "target": 3050 }, { "date": "2021-03-04", "actualTarget": 4000, "target": 3150 }, { "date": "2021-03-05", "actualTarget": 4000, "target": 3250 }]}\' -H "Content-Type: application/json"',
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    targets: {
      type: ['ref'],
      required: true,
      example: [
        { date: '2021-03-03', actualTarget: 4000, target: 3050 },
        { date: '2021-03-04', actualTarget: 4000, target: 3150 },
        { date: '2021-03-05', actualTarget: 4000, target: 3250 },
      ],
      description: 'Dates for dynamic Targets with targets of those values',
      custom(targets) {
        for (const target of targets) {
          if (globalHelper.isValidDateTime(target.date) === false) {
            throw new Error(`Invalid Date ${target.date}`);
          }
          if (typeof target.actualTarget !== 'number') {
            throw new Error(`Invalid value of actualTarget ${target.actualTarget}`);
          }
          if (typeof target.target !== 'number') {
            throw new Error(`Invalid value of actualTarget ${target.target}`);
          }
        }
        return true;
      },
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[baseline > createDynamictarget] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[baseline > createDynamictarget] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[baseline > createDynamictarget] forbidden Request!',
    },
  },

  async fn(inputs, exits) {
    try {
      const {
        _userMeta: { _site: siteId },
        targets,
      } = inputs;
      const dynamicTargets = [];

      for (const target of targets) {
        const formatDate = globalHelper.toMoment(target.date).format('YYYY-MM-DD');

        dynamicTargets.push({
          siteId,
          timestamp: formatDate,
          target: target.target,
          actualTarget: target.actualTarget,
        });
      }

      const $dynamicTargets = dynamicTargets
        .map((target) => baselineService.DynamicTarget.create(target));

      await Promise.all($dynamicTargets);

      return exits.success(dynamicTargets);
    } catch (error) {
      sails.log.error('[baseline > createDynamictarget] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  },
};
