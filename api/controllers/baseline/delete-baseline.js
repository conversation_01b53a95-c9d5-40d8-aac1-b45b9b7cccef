const baselineService = require('../../services/baseline/baseline.service');
const baselineUtils = require('../../utils/baseline/utils');
module.exports = {
  friendlyName: 'deleteBaseline',
  description: 'Create Basline for 0th year of project.Users Manually enter baseline\'s for 0th year and dejoule uses this to show baseline for current year',
  example: [
    'curl -X POST "http://localhost:1337/m2/baseline/v2/baseline" -H "Content-Type: application/json" --data \'{"baselines":[{"siteId":"ssh","startDate":"2016-04-14","consumptionValue":1337,"endDate":"2016-05-13","target":30}]}\'',
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    },
    siteId: {
      type: 'string'
    },
    startDate: {
      type: 'string'
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[baseline > deleteBaseline] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[baseline > deleteBaseline] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[baseline > deleteBaseline] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[baseline > deleteBaseline] Not Found'
    },
    deletedSuccessfully: {
      statusCode: 204,
      description: '[baseline > deleteBaseline] Deleted Successfully'
    }
  },

  async fn({siteId, startDate}, exits) {
    try {
      baselineUtils.deleteBaselineInputValidation({siteId, startDate})
      await baselineService.deleteBaseline(siteId, startDate)
      return exits.success({
        status: 'success',
        msg: 'deleted successfully'
      });
    } catch (error) {
      switch(error.code) {
        case 'E_INPUT_VALIDATION':{
          return exits.badRequest({
            err: error.message,
          })
        }
        case 'E_BASELINE_NOT_FOUND': {
          return exits.notFound({
            err: error.message,
          })
        }
        case 'E_BASELINE_NOT_DELETABLE': {
          return exits.forbidden({
            err: error.message,
          })
        }
        default: {
          sails.log.error('[baseline > deleteBaseline] Error!');
          sails.log.error(error);
          return exits.serverError(error);
        }
      }

    }
  },
};
