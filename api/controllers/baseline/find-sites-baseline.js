const baselineService = require('../../services/baseline/baseline.service');
module.exports = {
  friendlyName: 'findSitesBaseline',
  description: 'Find API to query baseline table',
  example: [
    'curl -X GET "http://localhost:1337/m2/baseline/v2/baseline',
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    siteId: {
      type: 'string',
      example: 'sjo-del',
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[baseline > findSitesBaseline] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[baseline > findSitesBaseline] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[baseline > findSitesBaseline] forbidden Request!',
    },
  },

  async fn({siteId}, exits) {
    try {
      const baselines = await baselineService.getBaselineBySiteId(siteId)
      return exits.success(baselines);
    } catch (error) {
      sails.log.error('[baseline > findSitesBaseeline] Error!');
      sails.log.error(error);
      return exits.serverError(error);
    }
  },
};
