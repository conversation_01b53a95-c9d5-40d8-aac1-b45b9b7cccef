const baselineService = require('../../services/baseline/baseline.service');
const dailyconsumptionService = require('../../services/dailyconsumption/dailyconsumption.public');
const siteService = require('../../services/site/site.public');
const globalHelper = require('../../utils/globalhelper');
const utils = require('../../utils/baseline/utils.js');

module.exports = {
  friendlyName: 'mapPrevyearConsumptionToThisYear',
  description: 'Return consumption info mapped to prev year in site-consumption unit. Used when FE queries last year consumption',
  example: [
    'curl "0:1337/m2/baseline/v2/prevYearConsumtion?startDate=2021-04-14&endDate=2021-05-13"',
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    startDate: {
      type: 'string',
      required: true,
      example: '2021-10-10',
      custom: globalHelper.isValidDateTime,
      description: 'Start date of this year , which will be mapped as startDate of prev year',
    },
    endDate: {
      type: 'string',
      required: true,
      example: '2021-11-10',
      custom: globalHelper.isValidDateTime,
      description: 'End Date of this year , which will be mapped as enddate of prev year',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[baseline > mapPrevyearConsumptionToThisYear] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[baseline > mapPrevyearConsumptionToThisYear] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[baseline > mapPrevyearConsumptionToThisYear] forbidden Request!',
    },
  },

  async fn(inputs, exits) {
    try {
      const {
        _userMeta: { _site: siteId },
        startDate
      } = inputs;

      const startDateMoment = globalHelper.toMoment(startDate);

      const baseline = await baselineService.getCurrentBaseline(siteId, startDateMoment);
      const dailyConsumptionUnit = await siteService.getSitesConsumptionUnit(siteId);
      // users requirement: donot add adjustments in this api
      const monthsBaselineAfterAdjustments = utils
        .getMonthsBaselineAfterAdjustmentsInPrefferedUit(
          baseline,
          undefined,
          [],
          dailyConsumptionUnit,
          dailyConsumptionUnit,
        );

      const baselineStartDateToPreviousYear = globalHelper.toMoment(baseline.startDate).subtract(1, 'year'); // 2020-05-01
      const baselineEndDateToPreviousYear = globalHelper.toMoment(baseline.endDate).subtract(1, 'year'); // 2020-05-31
      const currYearBaselineStartDate = globalHelper.toMoment(baseline.startDate);
      const currYearBaselineEndDate = globalHelper.toMoment(baseline.endDate);
      const prevYearConsumptionToCurrentYear = [];

      // Get dailyconsumption in site consumption unit. Originally daily. cons. is stored in kvah
      const consumptions = await dailyconsumptionService
        .getDayWiseConsumptionOfSiteBetween2TSInPreferredUnit(
          siteId,
          baselineStartDateToPreviousYear,
          baselineEndDateToPreviousYear,
          dailyConsumptionUnit,
        );

      const dateConsumptionMap = consumptions.reduce((acc, consumption) => {
        acc[consumption['timestamp']] = consumption;
        return acc;
      }, {});

      const tempDate = currYearBaselineStartDate.clone();
      for (; tempDate.diff(currYearBaselineEndDate, 'days') <= 0; tempDate.add(1, 'day')) {
        let tempDateTranslatedToPrevYear = utils.translateToPreviousYearDate(
          tempDate,
          baselineStartDateToPreviousYear,
          baselineEndDateToPreviousYear
        );
        let tempDateTranslatedToPrevYearFormat = tempDateTranslatedToPrevYear.format('YYYY-MM-DD');
        let tempDateFormatted = tempDate.format('YYYY-MM-DD');
        const baselineActualTarget = monthsBaselineAfterAdjustments[tempDateFormatted];
        const baselineTarget = globalHelper.roundNumber((baseline.target / 100) * baselineActualTarget);

        if (dateConsumptionMap[tempDateTranslatedToPrevYearFormat] !== undefined) {
          const { actual } = dateConsumptionMap[tempDateTranslatedToPrevYearFormat];
          prevYearConsumptionToCurrentYear.push(
            { date: tempDateFormatted, actualTarget: actual, target: actual, baselineTarget, baselineActualTarget },
          );
        } else {
          prevYearConsumptionToCurrentYear.push(
            { date: tempDateFormatted, actualTarget: null, target: null, baselineTarget, baselineActualTarget },
          );
        }
      }

      return exits.success(prevYearConsumptionToCurrentYear);
    } catch (error) {
      if (error.code === 'E_BASELINE_NOT_FOUND') {
        return exits.badRequest({
          problems: [error.message],
        })
      } else if (error.code === 'E_MULTIPLE_BASELINE_EXIST') {
        const {raw: baselineOverlapping, message} = error;
        return exits.badRequest({
          problems: [message],
          data: baselineOverlapping
        })
      } else {
      sails.log.error('[baseline > mapPrevyearConsumptionToThisYear] Error!');
      sails.log.error(error);
      exits.serverError(error);
      }
    }
  },
};
