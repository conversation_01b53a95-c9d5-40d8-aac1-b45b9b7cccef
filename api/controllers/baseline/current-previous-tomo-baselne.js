const baselineService = require('../../services/baseline/baseline.service');
const globalHelper = require('../../utils/globalhelper');

module.exports = {
  friendlyName: 'currentPreviousTomoBaselne',
  description: 'Get current baseline, next and previous baseline for user input timestamp',
  example: [
    'curl "http://localhost:1337/m2/baseline/v2/currentPrevNext?timestamp=2021-04-23"',
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    timestamp: {
      type: 'string',
      required: true,
      example: '2021-04-21',
      description: 'Timestamp for querying today tomo and yesterday baseline',
      custom: globalHelper.isValidDateTime,
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[baseline > currentPreviousTomoBaselne] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[baseline > currentPreviousTomoBaselne] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[baseline > currentPreviousTomoBaselne] forbidden Request!',
    },
  },

  async fn(inputs, exits) {
    try {
      const {
        _userMeta: { _site: siteId },
        timestamp,
      } = inputs;

      const todayMoment = globalHelper.toMoment(timestamp);
      const todayFormatted = todayMoment.format('YYYY-MM-DD');

      const todayBaseline = await baselineService.getCurrentBaseline(siteId, todayFormatted)
      const {startDate, endDate} = todayBaseline
      const previousBaselineDate = globalHelper.toMoment(startDate)
        .subtract(1, 'day')
        .format('YYYY-MM-DD');
      const nextBaselineDateDate = globalHelper.toMoment(endDate)
        .add(1, 'day')
        .format('YYYY-MM-DD');

      let [
        previousBaseline,
        nextBaseline
      ]= await Promise.allSettled([
        baselineService
        .getCurrentBaseline(siteId, previousBaselineDate),
        baselineService
        .getCurrentBaseline(siteId, nextBaselineDateDate)
      ]);

      if(previousBaseline.status === 'rejected') {
        const {reason } = previousBaseline
        previousBaseline = {
          problems: reason.message
        }
      } else {
        const {value} = previousBaseline
        previousBaseline = value
      }

      if(nextBaseline.status === 'rejected') {
        const {reason } = nextBaseline
        nextBaseline = {
          problems: reason.message
        }
      } else {
        const {value} = nextBaseline
        nextBaseline = value
      }


      return exits.success({
        previousBaseline,
        nextBaseline,
        todayBaseline,
      });
    } catch (error) {
      if (error.code === 'E_BASELINE_NOT_FOUND') {
        return exits.badRequest({
          problems: [error.message],
        })
      } else if (error.code === 'E_MULTIPLE_BASELINE_EXIST') {
        const {raw: baselineOverlapping, message} = error;
        return exits.badRequest({
          problems: [message],
          data: baselineOverlapping
        })
      }  else {
        sails.log.error('[baseline > currentPreviousTomoBaselne] Error!');
        sails.log.error(error);
        exits.serverError(error);
      }
    }
  },
};
