const baselineService = require('../../services/baseline/baseline.service');
const globalHelper = require('../../utils/globalhelper');

module.exports = {
  friendlyName: 'findAdjustment',
  description: 'Get the last done adjustment in baseline',
  example: [
    'curl -X GET "http://localhost:1337/',
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[baseline > findAdjustment] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[baseline > findAdjustment] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[baseline > findAdjustment] forbidden Request!',
    },
  },

  async fn(inputs, exits) {
    try {
      const {
        _userMeta: { _site: siteId },
      } = inputs;

      const today = globalHelper.formatDateTime();
      const adjustment = await baselineService.Adjustment.findOne({
        siteId,
        timestamp: {
          '<=': today,
        },
        sort: 'timestamp DESC',
      });

      return exits.success({ adjustment });
    } catch (error) {
      sails.log.error('[baseline > findAdjustment] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  },
};
