const baselineService = require('../../services/baseline/baseline.service');
const dailyconsumptionService = require('../../services/dailyconsumption/dailyconsumption.public');
const siteService = require('../../services/site/site.public');
const globalHelper = require('../../utils/globalhelper');
const utils = require('../../utils/baseline/utils');
module.exports = {
  friendlyName: 'findbaseline',
  description: 'This API is used to show baseline, target and actual cons. on dashboard graph',
  example: ['curl "0:1337/m2/baseline/v2/consumptionAndBaseline?currentDate=2021-04-22"'],

  inputs: {
    currentDate: {
      type: 'string',
      description: 'current date for baseline in YYYY-MM-DD',
      required: true,
      example: 'YYYY-MM-DD',
      custom: globalHelper.isValidDateTime,
    },
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    },

  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[baseline > findbaseline] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[baseline > findbaseline] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[baseline > findbaseline] unauthorized!',
    },
    forbidden: {
      statusCode: 403,
      description: '[baseline > findbaseline] Forbidden'
    },
    notFound: {
      statusCode: 404,
      description: '[baseline > findBaseline] not found!',
    }
  },
  async fn(inputs, exits) {
    try {
      const { currentDate } = inputs;
      const { _site: siteId, unitPref: unitPreference } = inputs._userMeta;
      {
        /**
         * MOCKING DASHBOARD API FOR IBMS PRESENTATION ONLY. We are using mgch site for demo purposes
         * */
        const sunshineIBMSSites = ['be-hyd', 'suh-hyd'];
        if (sunshineIBMSSites.indexOf(siteId) !== -1) {
          siteId = "mgch"
        }
      }

      const userConsumptionUnit = unitPreference.cons || 'kvah';

      const baseline = await baselineService.getCurrentBaseline(siteId,currentDate);
      const dailyConsumptionUnit = await siteService.getSitesConsumptionUnit(siteId);
      // this is unit the sites stores daily consumption (defaults to kvah)
      // and baselines are stored in this unit too. basicaly this is what this site consumption
      // unit is
      const adjustments = await baselineService.Adjustment.find({
        siteId,
        timestamp: {
          in: [baseline.startDate, baseline.endDate],
        },
      });

      const previousAdjustment = await baselineService.Adjustment.findOne({
        siteId,
        timestamp: {
          '<': baseline.startDate,
        },
        sort: 'timestamp DESC',
      });
      // adjustments and baselines are stored in dailyConsumptionUnit(site cons. unit)
      // we need to change them to userConsumptionUnit
      const monthsBaselineAfterAdjustments = utils
        .getMonthsBaselineAfterAdjustmentsInPrefferedUit(
          baseline,
          previousAdjustment,
          adjustments,
          userConsumptionUnit,
          dailyConsumptionUnit,
        );

      // dailyconsumption are stored in kvah & we need in userConsumptionUnit
      let actualConsumptionInBaselineDates = await dailyconsumptionService
        .getDayWiseConsumptionOfSiteBetween2TSInPreferredUnit(
          siteId,
          baseline.startDate,
          baseline.endDate,
          userConsumptionUnit,
        );

      const targetConsumptionInBaselineDates = await baselineService
        .getTargetConsumptionBetween2TSInPreferredUnit(
          siteId,
          baseline,
          monthsBaselineAfterAdjustments,
          userConsumptionUnit,
          dailyConsumptionUnit,
        );

      /*
      *
      * */

      if(siteId === "lmw-coi") {
        actualConsumptionInBaselineDates = await baselineService.generatingMovingEfficiencyGraphForLMW(baseline.startDate,baseline.endDate, actualConsumptionInBaselineDates)
      }
      return exits.success({
        monthsBaselineAfterAdjustments,
        actualConsumptionInBaselineDates,
        targetConsumptionInBaselineDates,
      });
    } catch (error) {
      if (error.code === 'E_BASELINE_NOT_FOUND') {
        return exits.badRequest({
          problems: [error.message],
        })
      } else if (error.code === 'E_MULTIPLE_BASELINE_EXIST') {
        const {raw: baselineOverlapping, message} = error;
        return exits.badRequest({
          problems: [message],
          data: baselineOverlapping
        })
      } else {
        sails.log.error('[baseline > findbaseline] Error!');
        sails.log.error(error);
        exits.serverError(error);
      }
    }
  },

};
