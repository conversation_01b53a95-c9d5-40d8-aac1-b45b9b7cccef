const messageTemplateService = require("../../services/messageTemplate/messageTemplate.service");
module.exports = {
  friendlyName: "updateMessageById",
  description:
    "This PUT api will update the title,body or vendor id of a message",
  inputs: {
    _userMeta: {
      type: "json",
      description: "",
      required: true,
    },
    messageTemplateId: {
      type: "number",
      description: "",
      required: true,
    },
    messageId: {
      type: "number",
      description: "",
      required: true,
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[messageTemplate > updateMessageById] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[messageTemplate > updateMessageById] Bad Request!",
    },
    unauthorized: {
      responseType: "unauthorized",
      description: "[messageTemplate > updateMessageById] unauthorized!",
    },
    success: {
      statusCode: 200,
      description:
        "[messageTemplate > updateMessageById] Message has been updated successfully",
    },
    messageTemplateNotFound: {
      statusCode: 404,
      description:
        "[messageTemplate > updateMessageById] Message template not found",
    },
    messageNotFound: {
      statusCode: 404,
      description:
        "[messageTemplate > updateMessageById] Message not found",
    },
  },
  async fn(inputs, exits) {
    try {
      // noinspection JSUnresolvedVariable
      const { body: param } = this.req;
      const { body, title, vendor_id, messageTemplateId, messageId } = param;
      if(await messageTemplateService.isMessageTemplateExist(messageTemplateId)){
        return exits.messageTemplateNotFound({meesage:"message template not found"})
      }
      const response = await messageTemplateService.updateMessageById(mid, {
        body,
        title,
        vendor_id,
      });
      return exits.success(response);
    } catch (e) {
      if (e.code === "INPUT_VALIDATION_ERROR") {
        return exits.badRequest({ message: e.message });
      } else if (e.code === "DUPLICATE_MESSAGE_TEMPLATE") {
        return exits.badRequest({ message: e.message });
      } else {
        return exits.serverError(e);
      }
    }
  },
};
