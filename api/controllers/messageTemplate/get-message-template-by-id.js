const messageTemplateService = require('../../services/messageTemplate/messageTemplate.service');
module.exports = {
  friendlyName: 'deleteMessageTemplate',
  description: 'This DELETE REST API is deleting the message template',
  inputs: {
    _userMeta: {
      type: 'json',
      description: '',
      required: true,
    },
    templateId: {
      type: 'number',
      description: 'message template id',
      required: true
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[messageTemplate > findOne] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[messageTemplate > findOne] Bad Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[messageTemplate > findOne] messageTemplate not found'
    }
  },
  async fn(inputs, exits) {
    try {
      // noinspection JSUnresolvedVariable
      const { templateId } = inputs;
      const messageTemplate = await messageTemplateService.fetchMessageTemplateById(templateId);
      if (messageTemplate) {
        return exits.success(messageTemplate);
      } else {
        return exits.notFound();
      }
    } catch (e) {
      return exits.serverError(e);
    }
  },
};
