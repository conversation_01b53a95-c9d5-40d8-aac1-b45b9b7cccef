const messageTemplateService = require("../../services/messageTemplate/messageTemplate.service");
module.exports = {
  friendlyName: "createMessageTemplate",
  description: "This POST API is registering new a new message template",
  inputs: {
    _userMeta: {
      type: "json",
      description: "",
      required: true,
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[messageTemplate > create] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[messageTemplate > create] Bad Request!",
    },
    unauthorized: {
      responseType: "unauthorized",
      description: "[messageTemplate > create] unauthorized!",
    },
    created: {
      statusCode: 201,
      description:
        "[messageTemplate > create] Message Template has been created",
    },
    success: {
      statusCode: 200,
      description:
        "[messageTemplate > create] Message Template has been created",
    },
  },
  async fn(inputs, exits) {
    try {
      // noinspection JSUnresolvedVariable
      const { body: param } = this.req;
      const { template_name, event_type, channel, body, title,vendor_template_id } = param;
      const response = await messageTemplateService.register({
        template_name,
        event_type,
        channel,
        body,
        title,
        vendor_template_id
      });
      return exits.created(response);
    } catch (e) {
      if (e.code === "INPUT_VALIDATION_ERROR") {
        return exits.badRequest({ message: e.message });
      } else if (e.code === "DUPLICATE_MESSAGE_TEMPLATE") {
        return exits.badRequest({ message: e.message });
      } else {
        return exits.serverError(e);
      }
    }
  },
};
