const messageTemplateService = require('../../services/messageTemplate/messageTemplate.service');
module.exports = {
  friendlyName: 'deleteMessageTemplate',
  description: 'This DELETE REST API is deleting the message template',
  inputs: {
    _userMeta: {
      type: 'json',
      description: '',
      required: true,
    },
    templateId: {
      type: 'number',
      description: 'message template id',
      required: true
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[messageTemplate > delete] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[messageTemplate > delete] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[messageTemplate > delete] unauthorized!',
    },
    deleted: {
      statusCode: 204,
      description: '[messageTemplate > delete] Message Template has been deleted'
    },
  },
  async fn(inputs, exits) {
    try {
      // noinspection JSUnresolvedVariable
      const { templateId } = inputs;
      const response = await messageTemplateService.deleteMessageTemplate(templateId);
      return exits.deleted(response);
    } catch (e) {
      return exits.serverError(e);
    }
  },
};
