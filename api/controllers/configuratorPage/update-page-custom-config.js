const configuratorPageService = require("../../services/configuratorPage/configuratorPage.service");
const requestValidator = require("../../utils/configuratorPage/requestValidator.util");

module.exports = {
  friendlyName: "Update page configuration with misc key in database",

  description: "Updates the misc column of a specific page with custom config data.",

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: {
        id: "userId",
        _role: "role",
        _site: "siteId",
      },
      description: "User meta information added by default to authenticated routes",
    },
    pageId: {
      type: "string",
      required: true,
      description: "Subsystem pageId",
      example: "42",
    },
    customConfig: {
      type: "json",
      required: true,
      description: "Custom CSS/JS can be injected at Client Side for custom manipulation",
      example: {
        css: [
          {
            selector: ".svg-container",
            styles: { padding: "40px" },
          },
        ],
      },
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[configurator > update-page-custom-config] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[configurator > update-page-custom-config] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[configurator > update-page-custom-config] forbidden Request!",
    },
    success: {
      statusCode: 200,
      description: "[configurator > update-page-custom-config] Success",
    },
    notFound: {
      statusCode: 404,
      description: "[configurator > update-page-custom-config] Not found",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const {
        customConfig,
        pageId,
        _userMeta: { _site: siteId, id: userId },
      } = inputs;
      requestValidator.validateCustomConfigRequest({
        siteId,
        pageId,
        customConfig,
      });
      const result = await configuratorPageService.updatePageCustomConfig(
        pageId,
        customConfig,
        userId,
      );
      return exits.success({
        message: result,
      });
    } catch (error) {
      switch (error.code) {
        case "E_PAGE_CUSTOM_CONFIG_UPDATE_FAILED":
          return exits.badRequest({ error: error.message });
        case "E_INPUT_VALIDATION":
          return exits.badRequest({ error: error.message });
        case "E_PAGE_NOT_FOUND":
          return exits.notFound({ error: error.message });
        default:
          sails.log("configurator > update-page-custom-config");
          sails.log.error(error);
          return exits.serverError(error);
      }
    }
  },
};
