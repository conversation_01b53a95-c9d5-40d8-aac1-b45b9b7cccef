const configuratorPageService = require('../../services/configuratorPage/configuratorPage.service');
const { validatePublishConfiguratorPageRequest } = require('../../utils/configuratorPage/requestValidator.util');
const auditEventLogService = require('../../services/auditEventLog/auditEventLog.public');

module.exports = {
  friendlyName: 'Publish configurator page',
  description: '',
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    },
    siteId: {
      type: 'string'
    },
    pageId: {
      type: 'string'
    }
  },


  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[configuratorPage > publish-configurator-page] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[configuratorPage > publish-configurator-page] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[configuratorPage > publish-configurator-page] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[configuratorPage > publish-configurator-page] Not Found'
    },
  },


  fn: async function (inputs, exits) {
    try {
      validatePublishConfiguratorPageRequest(inputs);
      const { _userMeta: { id: userId },...publishPageObj } = inputs;
      publishPageObj.userId = userId
      const published = await configuratorPageService.publishPage(publishPageObj);

      const auditPayload = {
        event_name: "state_publish_configurator-page",
        user_id: userId,
        site_id: publishPageObj?.siteId,
        asset_id: 'configurator-page-' + publishPageObj?.pageId,
        req: this.req,
        prev_state: null,
        curr_state: published,
      };

      auditEventLogService.emit(auditPayload);

      return exits.success({ message: `Page(${published.title}) published successfully` });
    } catch (err) {
      sails.log.error('[configuratorPage > publish-configurator-page]',err);
      if(err.code == 'E_INPUT_VALIDATION' || err.code == 'E_INVALID_PAGE_TYPE') {
        return exits.badRequest({ error: err.message });
      }
      if (err.code == 'E_SITE_NOT_FOUND' || err.code == 'E_PAGE_NOT_FOUND') {
        return exits.notFound({ error: err.message });
      }
      return exits.serverError(err);
    }
  }


};
