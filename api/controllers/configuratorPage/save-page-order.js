const configuratorPageService = require('../../services/configuratorPage/configuratorPage.service.js');
const { validateSavePageOrderRequest } = require('../../utils/configuratorPage/requestValidator.util.js');

module.exports = {
  friendlyName: 'Save page order',
  description: '',
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    },
    siteId: {
      type: 'string',
    },
    subsystemId: {
      type: 'string',
    },
    pageIds: {
      type: 'json',
    }
  },


  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[configuratorPage > save-page-order] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[configuratorPage > save-page-order] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[configuratorPage > save-page-order] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[configuratorPage > save-page-order] Not Found'
    },
    success: {
      statusCode: 200,
      description: '[configuratorPage > save-page-order] Page created successfully'
    }
  },


  fn: async function (inputs, exits) {
    try {
      const { _userMeta:{_id:userId}, ...filteredInputs } = inputs;
      validateSavePageOrderRequest(filteredInputs);
      await configuratorPageService.savePageOrder(inputs,userId);
      return exits.success({ message: 'Page order has been successfully updated.' });
    } catch (error) {
      sails.log.error('[configuratorPage > save-page-order]');
      sails.log.error(error);
      if (error.code === 'E_INPUT_VALIDATION' || error.code === 'E_INVALID_SUBSYSTEM') {
        return exits.badRequest({ error: error.message });
      }
      if (error.code === 'E_SYSTEM_NOT_FOUND') {
        return exits.notFound({ error: error.message });
      }
      return exits.serverError(error);
    }
  }


};
