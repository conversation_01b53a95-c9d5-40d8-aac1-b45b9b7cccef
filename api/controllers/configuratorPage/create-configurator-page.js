const configuratorPageService = require('../../services/configuratorPage/configuratorPage.service');
const { validatePageCreateRequest } = require('../../utils/configuratorPage/requestValidator.util');

module.exports = {

  friendlyName: 'Create configurator page',
  description: '',
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    },
    siteId: {
      type: 'string'
    },
    subsystemId: {
      type: 'string',
    },
    type: {
      type: 'number',
      required: true
    },
    title: {
      type: 'ref',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[configuratorPage > create-configurator-page] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[configuratorPage > create-configurator-page] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[configuratorPage > create-configurator-page] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[configuratorPage > create-configurator-page] Not Found'
    },
    success: {
      statusCode: 201,
      description: '[configuratorPage > create-configurator-page] Page created successfully'
    }
  },

  fn: async function (inputs, exits) {
    try {
      validatePageCreateRequest(inputs);
      const {_userMeta:{id:userId },...createPageObj} = inputs;
      createPageObj.userId = userId;
      const createdPage = await configuratorPageService.createPage(createPageObj);
      return exits.success({ pageId: createdPage.id });
    } catch (err) {
      if (err.code == 'E_INPUT_VALIDATION' || err.code == 'E_PAGE_ALREADY_EXISTS') {
        return exits.badRequest({ error: err.message });
      }
      if (err.code == 'E_SYSTEM_NOT_FOUND') {
        return exits.notFound({ error: err.message });
      }
      sails.log.error(err);
      return exits.serverError(err);
    }
  }
};
