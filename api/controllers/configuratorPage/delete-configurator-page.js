const configuratorPageService = require('../../services/configuratorPage/configuratorPage.service');
const { validatePageDeleteRequest } = require('../../utils/configuratorPage/requestValidator.util');
const auditEventLogService = require('../../services/auditEventLog/auditEventLog.public');

module.exports = {
  friendlyName: 'Delete configurator page',
  description: '',
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    },
    siteId: {
      type: 'string',
    },
    subsystemId: {
      type: 'string',
    },
    pageId: {
      type: 'string',
      required: true
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[configuratorPage > delete-configurator-page] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[configuratorPage > delete-configurator-page] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[configuratorPage > delete-configurator-page] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[configuratorPage > delete-configurator-page] Not Found'
    },
    success: {
      statusCode: 200,
      description: '[configuratorPage > delete-configurator-page] Page deleted successfully'
    }
  },

  fn: async function (inputs, exits) {
    try {
      validatePageDeleteRequest(inputs);
      const { _userMeta: { id: userId }, ...deletePageObj } = inputs;
      deletePageObj.userId = userId;
      await configuratorPageService.deletePage(deletePageObj);

      const auditPayload = {
        event_name: "state_delete_configurator-page",
        user_id: userId,
        site_id: deletePageObj?.siteId,
        asset_id: 'configurator-page-' + deletePageObj?.pageId,
        req: this.req,
        prev_state: deletePageObj,
        curr_state: null,
      };

      auditEventLogService.emit(auditPayload);

      return exits.success({ message: "Page deleted successfully" });
    } catch (err) {
      sails.log.error('[configuratorPage > delete-configurator-page]',err);
      if (err.code == 'E_INPUT_VALIDATION') {
        return exits.badRequest({ error: err.message });
      }
      if(err.code == 'E_SYSTEM_NOT_FOUND') {
        return exits.notFound({ error: err.message });
      }
      if (err.code == 'E_PAGE_NOT_FOUND') {
        return exits.notFound({ error: err.message });
      }
      return exits.serverError(err);
    }
  }
};
