const configuratorPageService = require('../../services/configuratorPage/configuratorPage.service');
const {validateFetchSystem} = require('../../utils/configuratorPage/requestValidator.util')
module.exports = {
  friendlyName: "Fetch System Page Detail",
  description: "Fetch system page detail of a subsystem",
  example: [],
  inputs: {
    siteId: {
      type: 'string'
    },
    systemId:{
      type:'string'
    },
    pageId: {
      type: 'string'
    }
  },
  exits: {
    serverError: {
      statusCode: 500,
      responseType: "serverError",
      description: "[configurator > fetchSystemPageDetail] Server Error",
    },
    forbidden: {
      statusCode: 403,
      description: "[configurator > fetchSystemPageDetail] Invalid User",
    },
    notFound: {
      statusCode: 404,
      description: "[configurator > fetchSystemPageDetail] Not Found",
    },
    success: {
      statusCode: 200,
      description: "[configurator > fetchSystemPageDetail] Successfully",
    },
    badRequest: {
      statusCode: 400,
      description: "[configurator > fetchSystemPageDetail] Bad Request",
    },
  },
  fn: async function (inputs, exits) {
    try {
      const {siteId, pageId} = inputs;
      validateFetchSystem({siteId, pageId});
      const systemDetail = await configuratorPageService.fetchPageDetailById(siteId, pageId);
      return exits.success(systemDetail);
    } catch (err) {
      switch(err.code) {
        case 'INPUT_VALIDATION_ERROR': {
          return exits.badRequest({
            err: err.message
          })
        }
        case 'E_INVALID_SITE': {
          return exits.badRequest({
            err: err.message
          })
        }
        case 'E_INVALID_SUBSYSTEM_ID': {
          return exits.badRequest({
            err: err.message
          })
        }

        case 'E_INVALID_PAGE_TYPE': {
          return exits.badRequest({
            err: err.message
          })
        }

        case 'E_SYSTEM_NOT_FOUND': {
          return exits.notFound({
            err: err.message
          })
        }

        case 'E_INVALID_PAGE': {
          return exits.badRequest({
            err: err.message
          })
        }

        case 'INVALID_GRAPH': {
          return exits.badRequest({
            err: err.message
          })
        }
        default: {
          sails.log("configurator > fetch-system");
          sails.log.error(err);
          return exits.serverError(err);

        }
      }

    }
  },
};
