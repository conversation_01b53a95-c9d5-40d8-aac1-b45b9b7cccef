const configuratorPageService = require('../../services/configuratorPage/configuratorPage.service');
const { validateUnpublishConfiguratorPageRequest } = require('../../utils/configuratorPage/requestValidator.util');
const auditEventLogService = require('../../services/auditEventLog/auditEventLog.public');

module.exports = {
  friendlyName: 'Unpublish configurator page',
  description: '',
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    },
    siteId: {
      type: 'string'
    },
    pageId: {
      type: 'string'
    }
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[configuratorPage > unpublish-configurator-page] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[configuratorPage > unpublish-configurator-page] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[configuratorPage > unpublish-configurator-page] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[configuratorPage > unpublish-configurator-page] Not Found'
    },
  },


  fn: async function (inputs, exits) {
    try {
      validateUnpublishConfiguratorPageRequest(inputs);
      const { _userMeta: { id: userId }, ...unPublishPageObj } = inputs;
      unPublishPageObj.userId = userId
      const unpublished = await configuratorPageService.unpublishPage(unPublishPageObj);

      const auditPayload = {
        event_name: "state_unpublish_configurator-page",
        user_id: userId,
        site_id: unPublishPageObj?.siteId,
        asset_id: 'configurator-page-' + unPublishPageObj?.pageId,
        req: this.req,
        prev_state: unPublishPageObj,
        curr_state: unpublished,
      };

      auditEventLogService.emit(auditPayload);

      return exits.success({ message: `Page(${unpublished.title}) unpublished successfully` });
    } catch (err) {
      sails.log.error('[configuratorPage > unpublish-configurator-page]',err);
      if (err.code == 'E_INPUT_VALIDATION' || err.code == 'E_INVALID_PAGE_TYPE') {
        return exits.badRequest({ error: err.message });
      }
      if (err.code == 'E_SITE_NOT_FOUND' || err.code == 'E_PAGE_NOT_FOUND') {
        return exits.notFound({ error: err.message });
      }
      return exits.serverError(err);
    }
  }


};
