const configuratorPageService = require("../../services/configuratorPage/configuratorPage.service");
const { validateSaveSVGRequest } = require("../../utils/configuratorPage/requestValidator.util");

module.exports = {
  friendlyName: "Save configurator graphic page svg",
  description: "Save configurator graphic page svg",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: {
        id: "userId",
        _role: "role",
        _site: "siteId",
      },
    },
    siteId: {
      type: "string",
    },
    pageId: {
      type: "string",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[configuratorPage > upload-svg-for-graphic-page] Server Error!",
    },
    badRequest: {
      statusCode: 400,
      responseType: "badRequest",
      description: "[configuratorPage > upload-svg-for-graphic-page] Bad Request!",
    },
    forbidden: {
      statusCode: 403,
      responseType: "forbidden",
      description: "[configuratorPage > upload-svg-for-graphic-page] forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[configuratorPage > upload-svg-for-graphic-page] Not Found",
    },
    success: {
      statusCode: 201,
      description: "[configuratorPage > upload-svg-for-graphic-page] Page created successfully",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { siteId, pageId } = inputs;
      const {
        _userMeta: { id: userId },
        ...updatePageObj
      } = inputs;
      validateSaveSVGRequest(updatePageObj);
      this.req.file("systemSVG").upload(
        {
          maxBytes: sails.config.custom.SVG_STORAGE_LIMIT,
        },

        async (err, svgUploadedData) => {
          if (_.isEmpty(svgUploadedData)) {
            return exits.badRequest({
              err: "system svg file is missing",
            });
          }

          if (err && err.code === "E_EXCEEDS_UPLOAD_LIMIT") {
            return exits.badRequest({
              err: `File size limit exceeded`,
            });
          }
          if (err) {
            return exits.serverError(err);
          }

          if (svgUploadedData && svgUploadedData.length > 1) {
            return exits.badRequest({
              err: `multiple file upload is not allowed`,
            });
          }

          try {
            const response = await configuratorPageService.uploadSVGtoGraphicTypePage({
              siteId,
              pageId,
              userId,
              svgFilePath: svgUploadedData[0],
            });
            return exits.success({
              message: "Successfully svg uploaded",
              link: response.svgURL,
              configuratorPageSVGId: response.configuratorPageSVGId,
            });
          } catch (error) {
            switch (error.code) {
              case "E_SITE_NOT_FOUND": {
                return exits.notFound({
                  err: "site not found",
                });
              }
              case "E_SYSTEM_NOT_FOUND": {
                return exits.notFound({
                  err: "subsystem not found",
                });
              }
              case "E_INVALID_PAGE_TYPE": {
                return exits.badRequest({
                  err: error.message,
                });
              }
              case "E_FILE_NOT_UPLOADED": {
                return exits.notFound({
                  err: "file not found",
                });
              }
              case "E_SVG_NOT_UPDATED": {
                return exits.forbidden({
                  err: "svg not updated please try again later",
                });
              }
              case "E_SYSTEM_NOT_IN_EDITABLE_MODE": {
                return exits.badRequest({
                  err: error.message,
                });
              }
              case "E_INVALID_FILE_FORMAT" || "E_INVALID_FILE_CONTENT": {
                return exits.badRequest({
                  err: error.message,
                });
              }

              case "E_INVALID_PAGE": {
                return exits.badRequest({
                  err: error.message,
                });
              }

              case "E_INVALID_SITE": {
                return exits.badRequest({
                  err: error.message,
                });
              }

              default: {
                sails.log.error("[systems > saveSystemSVG] Error!");
                sails.log.error(error);
                return exits.serverError(error);
              }
            }
          }
        },
      );
    } catch (e) {
      if (e.code === "INPUT_VALIDATION_ERROR") {
        return exits.badRequest({ message: e.message });
      }
      return exits.serverError(e);
    }
  },
};
