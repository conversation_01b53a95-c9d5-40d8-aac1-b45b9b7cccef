const systemService = require("../../services/ConfiguratorSystem/configuratorSystem.service");
const requestValidator = require("../../utils/configuratorSystem/requestValidator.util");

module.exports = {
  friendlyName: "resetSVGDataTagging",
  description: "reset SVG data tagging",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: {
        id: "userId",
        _role: "role",
        _site: "siteId",
      },
      description: "User meta information added by default to authenticated routes",
    },
    siteId: {
      type: "string",
    },
    subSystemId: {
      type: "string",
      description: "System id",
    },
    pageId: {
      type: "string",
      description: "Subsystem pageId",
    },
    dataTaggerDetail: {
      type: "json",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[configurator > resetSystemSVGDataTagging] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[configurator > resetSystemSVGDataTagging] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[configurator > resetSystemSVGDataTagging] forbidden Request!",
    },
    success: {
      statusCode: 200,
      description: "[configurator > resetSystemSVGDataTagging] Success",
    },
    notFound: {
      statusCode: 404,
      description: "[configurator > resetSystemSVGDataTagging] Not found",
    },
    invalidSystemState: {
      statusCode: 422,
      description: "[systems > createSystem] system is not in edit mode",
    },
    deleteComponentTaggingFailed: {
      statusCode: 409,
      description:
        "[system > resetSystemSVGDataTagging] unable to removed the component tagging from the system",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const {
        subSystemId,
        siteId,
        pageId,
        dataTaggerDetail,
        _userMeta: { id: userId },
      } = inputs;
      requestValidator.validateInputRequest({
        subSystemId,
        siteId,
        pageId,
        dataTaggerDetail,
      });
      await systemService.saveSystemDataTagging(
        {
          siteId,
          subSystemId,
          pageId,
          dataTaggerDetail,
        },
        userId,
      );
      return exits.success({
        message: "System svg tagging has been saved Successfully",
      });
    } catch (err) {
      switch (err.code) {
        case "E_INVALID_CONFIGURATOR_SYSTEM": {
          return exits.badRequest({
            err: err.message,
          });
        }

        case "E_INVALID_SYSTEM_SITE_MAPPED": {
          return exits.badRequest({
            err: err.message,
          });
        }

        case "E_COMPONENT_REMOVED": {
          return exits.deleteComponentTaggingFailed({
            err: err.message,
          });
        }

        case "E_INVALID_SUBSYSTEM_PAGE": {
          return exits.badRequest({
            err: err.message,
          });
        }
        case "E_SVG_LOCATION_ALREADY_TAGGED": {
          return exits.badRequest({
            err: err.message,
            data: err.data,
          });
        }
        case "E_INVALID_ABBR_NOT_ALLOWED": {
          return exits.badRequest({
            err: err.message,
            data: err.data,
          });
        }
        case "E_INVALID_DATA_CONTROL_ABBR_NOT_ALLOWED": {
          return exits.badRequest({
            err: err.message,
            data: err.data,
          });
        }
        case "E_INVALID_DEVICE_DATA_ABBR_NOT_ALLOWED": {
          return exits.badRequest({
            err: err.message,
            data: err.data,
          });
        }
        case "E_INPUT_VALIDATION": {
          return exits.badRequest({
            err: err.message,
            data: err.data,
          });
        }
        case "E_INPUT_OPERATION": {
          return exits.badRequest({
            err: err.message,
          });
        }
        case "E_SYSTEM_NOT_FOUND": {
          return exits.notFound({
            err: err.message,
          });
        }
        case "E_DEVICES_NOT_EXIST": {
          return exits.notFound({
            err: err.message,
            data: err.data,
          });
        }
        case "E_COMPONENT_NOT_FOUND": {
          return exits.badRequest({
            err: err.message,
            data: err.data,
          });
        }
        case "E_DUPLICATE_COMPONENT": {
          return exits.badRequest({
            err: err.message,
            data: err.data,
          });
        }
        case "E_DEVICE_RELATIONSHIP_NOT_FOUND": {
          return exits.notFound({
            err: err.message,
          });
        }
        case "E_SYSTEM_TAGGING_ERROR": {
          return exits.serverError(err);
        }

        case "E_SYSTEM_NOT_IN_EDITABLE_MODE": {
          return exits.invalidSystemState({
            err: err.message,
          });
        }
        case "INPUT_VALIDATION_ERROR": {
          return exits.badRequest({
            err: err.message,
          });
        }

        case "E_DEVICE_RELATIONSHIP_ABBR_FOUND": {
          return exits.notFound({
            err: err.message,
            data: err.data,
          });
        }
        case "E_INVALID_TAGGING_ID": {
          return exits.badRequest({
            err: err.message,
          });
        }
        case "E_INVALID_JSON_STRING": {
          return exits.badRequest({
            err: err.message,
          });
        }
        case "COMPONENT_ID_NOT_EXIST": {
          return exits.notFound({
            err: err.message,
          });
        }

        case "E_CONFIGURATOR_TAGGING_ALREADY_EXIST": {
          return exits.badRequest({
            err: err.message,
          });
        }

        case "E_COMPONENTS_NOT_FOUND": {
          return exits.badRequest({
            err: err.message,
            data: err.data,
          });
        }
        case "E_INVALID_NEW_RECORD": {
          return exits.badRequest({
            err: err.details,
          });
        }
        case "E_UNIQUE": {
          return exits.invalidSystemState({
            err: err.message,
          });
        }

        default: {
          sails.log("configurator > reset svg system data tagging");
          sails.log.error(err);
          return exits.serverError(err);
        }
      }
    }
  },
};
