const configuratorPageService = require('../../services/configuratorPage/configuratorPage.service');
const { validatePageUpdateRequest } = require('../../utils/configuratorPage/requestValidator.util');

module.exports = {
  friendlyName: 'Update configurator page',
  description: '',
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    },
    siteId: {
      type: 'string'
    },
    subsystemId: {
      type: 'string',
    },
    pageId: {
      type: 'string',
      required: true
    },
    title: {
      type: 'string',
      required: true
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[configuratorPage > update-configurator-page] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[configuratorPage > update-configurator-page] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[configuratorPage > update-configurator-page] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[configuratorPage > update-configurator-page] Not Found'
    },
    success: {
      statusCode: 200,
      description: '[configuratorPage > update-configurator-page] Page title updated successfully'
    }
  },

  fn: async function (inputs, exits) {
    try {
      validatePageUpdateRequest(inputs);
      const { _userMeta: { id: userId }, ...updatePageObj } = inputs;
      updatePageObj.userId = userId;

      const updatedPage = await configuratorPageService.updatePage(updatePageObj);
      return exits.success(updatedPage);
    } catch (err) {
      if (err.code == 'E_INPUT_VALIDATION' || err.code == 'E_PAGE_ALREADY_EXISTS') {
        return exits.badRequest({ error: err.message });
      }
      if (err.code == 'E_PAGE_NOT_FOUND' || err.code == 'E_SYSTEM_NOT_FOUND') {
        return exits.notFound({ error: err.message });
      }
      sails.log.error(err);
      return exits.serverError(err);
    }
  }
};
