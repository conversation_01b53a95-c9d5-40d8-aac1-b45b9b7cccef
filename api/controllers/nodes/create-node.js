
const configurationHierarchyService = require('../../services/configurationHierarchy/configurationHierarchy.service');
const sitePublic = require('../../services/site/site.public');
const selfUtils = require('../../utils/nodes/create-node.util.js');
const auditEventLogService = require('../../services/auditEventLog/auditEventLog.public');

module.exports = {
  friendlyName: 'createNode',
  description : 'Creates a layer or leaf (controller/ Component) in the nodes table after checking \
  in the systems definition table if it is correct according to the defined rules.',
  example: [
    `curl --location --request POST 'http://localhost:1338/m2/configuration/v1/system/20/node' \
    --header 'Authorization: Bearer ' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "parentId": 0,
        "name": "Controller in: Room: 101",
        "levelType": "controller",
        "deviceId": "1234",
        "siteId": "by-hyd"
    }'`,
    `curl --location --request POST 'http://localhost:1338/m2/configuration/v1/system/20/node' \
    --data-raw '{
        "parentId": 2,
        "name": "Room: 101",
        "levelType": "layer",
        "layerType": "Room",
        "siteId": "be-hyd"
    }'`
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    layerType: {
      type: 'string',
      // required: true, // Not required when adding a leaf, ie, controller/ component
      example: "room",
      description: 'Layer type which has been defined in system definition table',
    },
    levelType: {
      type: 'string',
      required: true,
      example: "component",
      description: 'Defining if it\'s a component/controller/layer',
    },
    systemId: {
      type: 'number',
      required: true,
      example: 21,
      description: 'ID of the system.',
    },
    parentId: {
      type: 'number',
      required: true,
      example: 57,
      description: 'Node ID of the parent.',
    },
    name: {
      type: 'string',
      required: true,
      example: "Chiller 3",
      description: 'Name of the controller/component',
    },
    deviceId: {
      type: 'string',
      // required: true, // Not required when adding a layer.
      example: "be-hyd_11",
      description: 'deviceId of the controller or the componentId of the controller',
    },
    siteId: {
      type: 'string',
      // required: true, // Not required as siteId can be picked up from the token.
      example: "be-hyd",
      description: 'siteId of the controller/component. Usually taken from token, but can be overwritten if this query param is passed.',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[nodes > createNode] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[nodes > createNode] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[nodes > createNode] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      const {
        _userMeta: { _site: siteIdInToken, id:userId }, levelType, systemId, parentId, name, deviceId, siteId: siteIdInQueryParam, layerType
      } = inputs;

      let siteId = siteIdInQueryParam ? siteIdInQueryParam : siteIdInToken;
      const isLayer = levelType == "layer" ? true : false;

      let $doesSiteExist = sitePublic.siteExists(siteId);
      let $systemInfo = configurationHierarchyService.systems.findOne({ id:systemId });
      let $parentNodeInfo = configurationHierarchyService.nodes.findOne({ id: parentId, site_id: siteId });

      let $parentHierarchyInfo, parentHierarchyInfo; // layer specific checks
      let $sameEntryCheck, sameEntryCheck,  $systemDefinition, systemDefinition; // controller/ component specific checks
      if(isLayer){
        $parentHierarchyInfo = configurationHierarchyService.systemNodes.findOne({ layer_type: layerType, system_id: systemId })
        .then(childHierarchyInfo => {
          if(!childHierarchyInfo) return null;
          if(childHierarchyInfo.parent_id == 0) return true;
          return configurationHierarchyService.systemNodes.findOne({ id: childHierarchyInfo.parent_id });
        });
        $sameEntryCheck = configurationHierarchyService.nodes.findOne({
          system_id: systemId,
          parent_id: parentId,
          name: name,
          site_id: siteId,
        });
      } else {
        $sameEntryCheck = configurationHierarchyService.nodes.find({
          system_id: systemId,
          parent_id: parentId,
          device_id: deviceId,
          site_id: siteId,
        });
        $systemDefinition = configurationHierarchyService.systemNodes.find({ system_id: systemId });
      }
      let doesSiteExist = await $doesSiteExist;
      let systemInfo = await $systemInfo;
      let parentNodeInfo = await $parentNodeInfo;
      sameEntryCheck = await $sameEntryCheck;

      if (isLayer){
        parentHierarchyInfo = await $parentHierarchyInfo;
      } else {
        systemDefinition = await $systemDefinition;
      }

      let inputCheckOutput = selfUtils.checkInput(inputs, doesSiteExist, systemInfo, parentNodeInfo, sameEntryCheck, systemDefinition, parentHierarchyInfo, isLayer);
      if(!inputCheckOutput.status)
        return exits.badRequest({
          "status": false,
          problems: inputCheckOutput.errors
        });

      let createObject = {
        system_id: systemId,
        parent_id: parentId,
        level_type: levelType,
        name,
        site_id: siteId
      };
      if (isLayer) createObject["layer_type"] = layerType;
      else createObject["device_id"] = deviceId;
      await configurationHierarchyService.nodes.create(createObject);
      let createdNode;
      if(isLayer){
        let createdObject = await configurationHierarchyService.nodes.findOne({
          system_id: systemId,
          site_id: siteId,
          parent_id: parentId,
          name
        });
        createdNode = {
          id: createdObject.id,
          name: createdObject.name,
          layerType: createdObject.layer_type,
          child: [],
          levelType: createdObject.level_type,
        }
      }
      else {
        let createdObject = await configurationHierarchyService.nodes.findOne({
          system_id: systemId,
          site_id: siteId,
          device_id: deviceId,
          is_deleted: false
        });
        createdNode = {
          id: createdObject.id,
          name: createdObject.name,
          levelType: createdObject.level_type,
          deviceId: createdObject.device_id
        };
      }

      const auditPayload = {
        event_name: "state_create_ibms-leaf-node",
        user_id: userId,
        site_id: siteId,
        asset_id: createdNode?.deviceId,
        req: this.req,
        prev_state: null,
        curr_state: createdNode,
      };

      auditEventLogService.emit(auditPayload);

      return exits.success({
        'status': true,
        createdNode
      });
    } catch(error) {
      sails.log.error('[nodes > createNode] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
