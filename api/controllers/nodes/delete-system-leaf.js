
const configurationHierarchyService = require('../../services/configurationHierarchy/configurationHierarchy.service');
const selfUtils = require('../../utils/nodes/delete-system-leaf.util.js');
const auditEventLogService = require('../../services/auditEventLog/auditEventLog.public');

module.exports = {
  friendlyName: 'deleteSystemLeaf',
  description : 'Used to delete a controller/ component. Layers cannot be deleted currently. Will need to be manually deleted if configured incorrectly.',
  example: [
    `curl --location --request DELETE 'http://localhost:1338/m2/configuration/v1/system/1/node/leaf' \
    --header 'Authorization: Bearer ' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "nodeId": "90"
    }'`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    nodeId: {
      type: 'number',
      required: true,
      example: 17,
      description: 'System ID which needs to be deleted.',
    },
    deviceId: {
      type: 'string',
      example: 'suh-hyd_226',
      description: 'Node attached with device Id which needs to be deleted.',
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[nodes > deleteSystemLeaf] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[nodes > deleteSystemLeaf] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[nodes > deleteSystemLeaf] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      const { nodeId, deviceId=null, _userMeta: { _site: siteId, id:userId } } = inputs;

      if(!_.isEmpty(deviceId)){
        const nodesInfo = await configurationHierarchyService.nodes.find({device_id:deviceId,is_deleted:false});
        if (!nodesInfo) {
          return exits.badRequest({ status: false, problems: ['Node not found or already deleted'] });
        }

        const response = { success: [], error: [] };

        const updatePromises = nodesInfo.map(async (node) => {
          try {
            const res = await Nodes.update({ id: node?.id }).set({ is_deleted: true }).fetch()
            response.success.push(res);
          } catch (err) {
            response.error.push({ nodeId: node?.id, error: err });
          }
        });
        await Promise.all(updatePromises);
        return exits.success({ status: true, response });
      }

      let nodeInfo = await configurationHierarchyService.nodes.findOne({ id: nodeId,is_deleted:false });
      let inputCheckOutput = selfUtils.checkInput(inputs, nodeInfo);
      if(!inputCheckOutput.status)
        return exits.badRequest({
          "status": false,
          problems: inputCheckOutput.errors
        });

      await configurationHierarchyService.nodes.update({ id: nodeId }, { is_deleted:true });

      const auditPayload = {
        event_name: "state_delete_system_leaf",
        user_id: userId,
        site_id: siteId,
        asset_id: deviceId,
        req: this.req,
        prev_state: nodeInfo,
        curr_state: null,
      };

      auditEventLogService.emit(auditPayload);

      return exits.success({
        "status": true
      });
    } catch(error) {
      sails.log.error('[nodes > deleteSystemLeaf] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
