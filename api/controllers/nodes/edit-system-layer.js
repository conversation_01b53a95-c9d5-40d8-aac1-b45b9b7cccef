
const configurationHierarchyService = require('../../services/configurationHierarchy/configurationHierarchy.service');
// const selfUtils = require('../../utils/nodes/edit-system-layer.util.js');

module.exports = {
  friendlyName: 'editSystemLayer',
  description : 'Edits the name of a layer in the nodes table.',
  example: [
    `curl --location --request PUT 'http://localhost:1338/m2/configuration/v1/system/1/node/layer' \
    --header 'Authorization: Bearer ' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "nodeId": "10",
        "name": "New Room Name"
    }'`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    nodeId: {
      type: 'string',
      required: true,
      example: '10',
      description: 'ID of the node configured.',
    },
    name: {
      type: 'string',
      required: true,
      example: "New Room Name",
      description: 'New name of the layer.',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[nodes > editSystemLayer] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[nodes > editSystemLayer] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[nodes > editSystemLayer] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      const { nodeId, name } = inputs;
      await configurationHierarchyService.nodes.update({ id: nodeId },{ name });
      return exits.success({ "status": true });
    } catch(error) {
      sails.log.error('[nodes > editSystemLayer] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
