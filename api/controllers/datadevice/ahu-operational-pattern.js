const moment = require("moment-timezone");
const datadeviceService = require("../../services/datadevice/datadevice.service.js");
const selfUtils = require("../../utils/datadevice/ahu-operational-pattern.util.js");
const componentService = require('../../services/component/component.public.js');

module.exports = {
  friendlyName: "ahu operational pattern",
  description: "",

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
      description: "User meta information added by default to authenticated routes",
    },
    siteId: {
      type: "string",
    },
    deviceType: {
      type: "string",
      required: true,
    },
    latestBy: {
      type: "string",
      required: true
    },
    use: {
      type: "string",
      required: true,
    }
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[datadevice > ahu-operational-pattern] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[datadevice > ahu-operational-pattern] Bad Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[datadevice > ahu-operational-pattern] Not Found!",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const isValidRequest = selfUtils.requestValidator(inputs);
      if (!isValidRequest) return exits.badRequest({ error: "Invalid request" });
      const { siteId, deviceType, latestBy, use } = inputs;
      const startTime = moment().startOf("day").subtract(1, "day").subtract(1, latestBy).format("YYYY-MM-DDTHH:mm:ss.SSSZ");
      const endTime = moment().startOf("day").subtract(1, "day").format("YYYY-MM-DDTHH:mm:ss.SSSZ");
      const allowedUse = ["actuator", "outputfrequency"];
      const param = use.toLowerCase();
      if (!allowedUse.includes(param)) return exits.badRequest({ error: "Invalid parameters" });
      const componentIds = (await componentService.find({ siteId, deviceType })).map(it => it.deviceId);
      const res = await datadeviceService.generateAhuOperationalPattern(
        siteId,
        componentIds,
        startTime,
        endTime,
        param
      );
      return exits.success(res);
    } catch (error) {
      sails.log.error("[datadevice > ahu-operational-pattern] Error!");
      sails.log.error(error);
      exits.serverError(error);
    }
  },
};
