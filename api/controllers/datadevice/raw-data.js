
const datadeviceService = require('../../services/datadevice/datadevice.service');
const globalHelper = require('../../utils/globalhelper');
const selfUtils = require('../../utils/datadevice/raw-data.util.js');
const utils = require('../../utils/datadevice/utils.js');

module.exports = {
  friendlyName: 'rawData',
  description : '',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    deviceId: {
      type: 'string',
      required: true,
      example: "296",
      description: 'DeviceId',
    },
    start: {
      type: 'string',
      example: "296",
      description: 'Start timestamp',
    },
    end: {
      type: 'string',
      example: "296",
      description: 'End Timestamp',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[datadevice > rawData] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[datadevice > rawData] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[datadevice > rawData] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      const {
        deviceId, start, end, _userMeta: { _site }
      } = inputs;

      const formattedTimestamps = selfUtils.formatTimestamps(start, end);
      if(formattedTimestamps.status == false) return exits.badRequest({
        // Error message as per previous API.
        err: "Parameters not valid",
        data: null,
        problems: formattedTimestamps.problems
      });
      const { startTime, endTime } = formattedTimestamps;
      const sortInDescending = true;
      const data = await datadeviceService.getDataBetweenTwoTimeStamps(deviceId, startTime, endTime, sortInDescending, _site);
      return exits.success({
        err: null,
        data
      });
    } catch(error) {
      sails.log.error('[datadevice > rawData] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
