const cacheService = require("../../services/cache/cache.service");
const dataDeviceService = require("../../services/datadevice/datadevice.service");
const siteService = require('../../services/site/site.public');
const dashboardUtils = require('../../utils/datadevice/dashboard');

module.exports = {
    friendlyName: 'Dashboard',
    description: 'Dashboard page consumption data',
    inputs: {
        _userMeta: {
            type: 'json',
            required: true,
        },
    },
    exits: {
        serverError: {
            statusCode: 500,
            responseType: "serverError",
            description: 'Server error',
        },
        badRequest: {
            statusCode: 400,
            description: 'Bad request',
        },
        success: {
            statusCode: 200,
            description: 'Success',
        },
        notFound: {
            statusCode: 404,
            description: 'Not found',
        }
    },
    fn: async function (inputs, exits) {
        try {
            const siteId = inputs._userMeta._site;
            const _site = await siteService.findOne({
                siteId
            })
            if (!_site) {
                return exits.badRequest({
                    code: 'E_BAD_REQUEST',
                    message: 'Site not found',
                    problems: ['Site not found']
                })
            }
            const timezoneOffset = _site.timezone
            const deviceList = await dataDeviceService.getMainEMList(siteId);
            if (_.isEmpty(deviceList)) {
                return exits.badRequest({
                    code: 'E_BAD_REQUEST',
                    message: 'Energy meter not found',
                    problems: ['Energy meter not found']
                })
            }
            const lastTimestamp = await dataDeviceService.cachedLastDataTime(siteId);
            const userPrefConsUnit = inputs._userMeta.unitPref.cons;
            if (!userPrefConsUnit) {
                return exits.badRequest({
                    code: 'E_BAD_REQUEST',
                    message: 'User preference consumption unit not found',
                    problems: ['User preference consumption unit not found']
                })
            }
            const {expertPro, nf29} = await dataDeviceService.getDeviceFieldByType(deviceList, siteId)
            const getDashboardConsumption = async function(devices, fields, deviceObj) {
                const {
                    siteId,
                    type
                } = deviceObj;
                const hourlyWiseConsumptionData = await dataDeviceService.hourlyWiseConsumption(devices, fields, {
                    userPrefConsUnit,
                    siteId,
                });
                const lastDayConsumption = await dataDeviceService.lastDayConsumption(devices, fields, {
                    userPrefConsUnit,
                    siteId,
                })
                const weeklyConsumptionData = await dataDeviceService.getWeeklyConsumption(devices, siteId,  {
                    userPrefConsUnit,
                    siteId,
                    type
                });
                const dashboardConsumptionData = dashboardUtils.dashboardConsumptionData({
                    weeklyConsumptionData,
                    hourlyWiseConsumptionData,
                    lastDayConsumption,
                    lastTimestamp
                })
                return dashboardConsumptionData;
            }
            let $performBatchOnDeviceTypes = [], dashboardConsumptionData = {};
            if (!_.isEmpty(expertPro.devices) && !_.isEmpty(nf29.devices)) {
                $performBatchOnDeviceTypes.push(getDashboardConsumption(expertPro.devices, expertPro.fields, {
                    userPrefConsUnit,
                    siteId,
                    type: 'expertPro'
                }));
                $performBatchOnDeviceTypes.push(getDashboardConsumption(nf29.devices, nf29.fields, {
                    userPrefConsUnit,
                    siteId,
                    type: 'nf29'
                }));
            } else if (!_.isEmpty(nf29.devices)) {
                $performBatchOnDeviceTypes.push(getDashboardConsumption(nf29.devices, nf29.fields, {
                    userPrefConsUnit,
                    siteId,
                    type: 'nf29'
                }));
            } else if(!_.isEmpty(expertPro.devices)) {
                $performBatchOnDeviceTypes.push(getDashboardConsumption(expertPro.devices, expertPro.fields, {
                    userPrefConsUnit,
                    siteId,
                    type: 'expertPro'
                }));
            }
            dashboardConsumptionData = await Promise.all($performBatchOnDeviceTypes);
            dashboardConsumptionData = dashboardUtils.mergeDataDeviceType(dashboardConsumptionData)
            return exits.success(dashboardConsumptionData)
        } catch (e) {
            sails.log('error > dashboardConsumptionData', e.message)
            return exits.serverError(e)
        }
    }
}
