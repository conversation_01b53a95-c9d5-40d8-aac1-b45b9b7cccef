const _ = require('@sailshq/lodash');

const datadeviceService = require('../../services/datadevice/datadevice.service');
const globalHelper = require('../../utils/globalhelper');
const selfUtils = require('../../utils/datadevice/top-worst-ahus.util.js');
const utils = require('../../utils/datadevice/utils.js');
const componentService = require('../../services/component/component.public');
const { exists } = require('grunt');

module.exports = {
  friendlyName: 'topWorstAhus',
  description: 'Gets list of top 5 top performing ahu and top 5 ahu performing below average',
  example: [
    `curl -X POST --data "siteId=ssh&&component=ahu&groupBy=day&use=OutputFrequency" localhost:1337/m2/analytic/v2/presets/ahuPerformace`,
  ],

  inputs: {
    siteId: {
      type: 'string',
      description: 'Unique site id',
      example: 'Id of site',
      required: true
    },
    component: {
      type: 'string',
      description: 'device type of component',
      example: 'ahu',
      required: true,
      isIn: ['ahu']
    },
    groupBy: {
      type: 'string',
      exmaple: 'hour',
      isIn: ['day', 'week', 'month'],
      required: true,
      description: 'group by parameter on day/week/month'
    },
    use: {
      type: 'string',
      example: 'OutputFrequency',
      isIn: ['OutputFrequency', 'Actuator'],
      description: 'Parameter to use to find top/worse AHU',
      required: true,
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[datadevice > topWorstAhus] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[datadevice > topWorstAhus] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[datadevice > topWorstAhus] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try {
      let { siteId, component, groupBy, use: param } = inputs;
      param = param.toLowerCase();
      let best = [], worst = [];
      let currendDateMoment = globalHelper.toMoment().startOf(groupBy);
      let endTime = currendDateMoment.clone().format();
      let startTime = currendDateMoment.clone().subtract(1, groupBy).format();

      let ahuComponents = await componentService.find({ siteId, deviceType: component });
      let validAHUs;

      switch (param) {
        case 'actuator':
          validAHUs = ahuComponents.filter(ahu => {
            return Components.isComponentWithActuatorInstalled(ahu.data);
          });
          break;
        case 'outputfrequency':
          validAHUs = ahuComponents.filter(ahu => {
            return Components.isComponentWithVFDInstalled(ahu.data);
          });
          break;
        default:
          return exists.badRequest({ problems: [`Invalid parameter ${param}`] });
      }

      let ahuIds = validAHUs.map(ahu => ahu.deviceId);
      let $ahusData = ahuIds.map(ahuId => {
        return datadeviceService.getDeviceParamsDataBetween2TimestampInPreferredUnit(
          ahuId, [param], siteId, startTime, endTime, {}
        );
      });
      let ahusData = await Promise.all($ahusData);
      let ahuIdDataObj = _.zipObject(ahuIds, ahusData);
      let ahuIdStandardDeviationObjList = [];

      for (let deviceId in ahuIdDataObj) {
        let data = ahuIdDataObj[deviceId];
        let SD = selfUtils.calculateStandardDeviation(data, param);
        if (SD !== null) {
          ahuIdStandardDeviationObjList.push({ deviceId, SD });
        }
      }

      let { bestAhuIds, worstAhuIds } = selfUtils
        .getNBestAndWorstAhuIdsOnBasesOfSD(ahuIdStandardDeviationObjList, 5);

      best = bestAhuIds.map(bestAhuId => {
        let plotData = ahuIdDataObj[bestAhuId]
          .map(ahuData => [
            globalHelper.toMoment(ahuData.timestamp).unix() * 1000,
            ahuData.data[param]
          ]);
        return { [bestAhuId]: plotData };
      });
      worst = worstAhuIds.map(worstAhuId => {
        let plotData = ahuIdDataObj[worstAhuId]
          .map(ahuData => [
            globalHelper.toMoment(ahuData.timestamp).unix() * 1000,
            ahuData.data[param]
          ]);
        return { [worstAhuId]: plotData }
      });


      return exits.success({ best, worst });

    } catch (error) {
      sails.log.error('[datadevice > topWorstAhus] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
