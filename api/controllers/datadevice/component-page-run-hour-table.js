const datadeviceService = require('../../services/datadevice/datadevice.service');

module.exports = {
  friendlyName: 'asset-run-hour-table',
  description: 'Query last minute\'s data for a list of deviceIds',
  example: [
    "curl --location 'localhost:1337/m2/site/suh-hyd/asset-run-hour-table' \
--header 'Authorization: Bearer D6GGSY'",
  ],

  inputs: {
   componentId: {
    type: 'string',
   },
   siteId: {
    type: 'string'
   }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[datadevices > asset-run-hour-table] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[datadevices > asset-run-hour-table] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[datadevices > asset-run-hour-table] forbidden Request!',
    },
  },

  async fn(inputs, exits) {
    try {
      const { componentId, siteId } = inputs;
      if (!siteId.trim().length) {
        return exits.badRequest({
          err: 'Invalid Site ID'
        })
      }

      if (!componentId.trim().length) {
        return exits.badRequest({
          err: 'Invalid component ID'
        })
      }
    
      const deviceRecentRunningHr = await datadeviceService.getAssetRunMinuteTableData(siteId, componentId);
      return exits.success(deviceRecentRunningHr);
    } catch (error) {
      if (error.code == 'notFound') {
        return exits.badRequest({
          err: error.raw
        })
      }
      sails.log.error('[datadevices > asset-run-hour-table] Error!');
      sails.log.error(error);
      return exits.serverError('Server has encountered an issue. Please contact admin to resolve this issue.');
    }
  },
};