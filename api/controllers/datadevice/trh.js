
const datadeviceService = require('../../services/datadevice/datadevice.service');
const selfUtils = require('../../utils/datadevice/trh.util.js');
const utils = require('../../utils/datadevice/utils.js');
const { dataParameter } = require('../../services/component/cachingService');
const moment = require('moment');


module.exports = {
  friendlyName: 'trh',
  description : '',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: false, // TODO: Revert to true post development
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    startTimestamp: {
      type: 'string',
      required: true,
      example: "2021-07-28",
      description: 'Start Timestamp of when TRH needs to be calculated.',
    },
    endTimestamp: {
      type: 'string',
      required: true,
      example: "2021-07-29",
      description: 'End Timestamp of when TRH needs to be calculated.',
    },
    componentId: {
      type: 'json',
      required: true,
      example: ["kims_21", "kims_22", "kims_111"],
      description: 'Array of componentIds for which TRH needs to be calculated.',
    },
    groupBy: {
      type: 'string',
      required: true,
      example: 'hour',
      example2: 'day',
      description: 'On what time interval the values need to be grouped by. Possible values: "hour/ day',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[datadevice > trh] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[datadevice > trh] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[datadevice > trh] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      let { startTimestamp, endTimestamp, componentId, groupBy,_userMeta } = inputs;
      const siteId = _userMeta._site;
      const timeFormat = "YYYY-MM-DDTHH:mm:00Z";
      startTimestamp = moment(startTimestamp).format(timeFormat);
      endTimestamp = moment(endTimestamp).format(timeFormat);
      let componentList = Array.from(componentId);
      try {
        selfUtils.checkInput(groupBy);
      } catch (error) {
        return exits.badRequest(error);
      }

      //creating a map for trh related parameter and componentId
      const componentTrhMap = await datadeviceService.componentsTrhKeyMap(siteId, componentList)
      componentList.forEach((_component,index)=>{
        if(!componentTrhMap.hasOwnProperty(_component)){
          componentList[index]=null;
        }
      })
      componentList = componentList.filter(Boolean);
      if(componentList.length < 1) {
        return exits.badRequest(`COMPONENTS_NOT_CONFIGURED_FOR_TRH`);
      }

      //fetching data from influx
      const getTrhDataFromInflux = async (component,startTimestamp,endTimestamp)=>{
        let result = datadeviceService.getChillerTonnageDeliveredGraph(siteId,component,startTimestamp, endTimestamp,groupBy)
        return {
          trh:await result,
          componentId:component
        }
      }

      // fetch trh data for all the chiller

      const queryResolver = [];
      Object.keys(componentTrhMap).forEach(component=>{
        queryResolver.push(getTrhDataFromInflux(component,startTimestamp,endTimestamp))
      })

      const chillerWiseTonnageDeliveredPromise =  Promise.all(queryResolver)
      const totalPlantRoomTonnageDeliveredPromise = datadeviceService.getTotalTonnageDeliveredGraph(siteId,componentList, startTimestamp,endTimestamp,groupBy)
      const [chillerWiseTonnageDelivered, totalPlantRoomTonnageDelivered] = await Promise.all([chillerWiseTonnageDeliveredPromise, totalPlantRoomTonnageDeliveredPromise])
      return exits.success(selfUtils.formatResponseObject(chillerWiseTonnageDelivered, totalPlantRoomTonnageDelivered));
    } catch(error) {
      sails.log.error('[datadevice > trh] Error!');
      sails.log.error(error);
      return exits.serverError(error);
    }
  }
};
