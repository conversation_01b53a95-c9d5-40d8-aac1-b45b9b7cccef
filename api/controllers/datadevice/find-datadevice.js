
const datadeviceService = require('../../services/datadevice/datadevice.service');
const globalHelper = require('../../utils/globalhelper');

module.exports = {
  friendlyName: 'findDatadevice',
  description: 'Query data from data device table for deviceId/components between start and endtime',
  example: [
    `curl "0:1337/m2/analytics/v2/?deviceId=gknmh_6&startTime=2019-07-28%2020:00"`,
  ],

  inputs: {
    deviceId: {
      type: 'string',
      example: 'ssh_2',
      description: 'device/component id',
      required: true
    },
    startTime: {
      type: 'string',
      example: 'YYYY-MM-DD HH:mm',
      description: 'start Time to get data from',
      required: true
    },
    endTime: {
      type: 'string',
      example: 'YYYY-MM-DD HH:mm',
      description: 'end Time to get data to',
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[datadevices > findDatadevice] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[datadevices > findDatadevice] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[datadevices > findDatadevice] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try {
      let { deviceId, startTime, endTime } = inputs;

      if (!endTime) {
        endTime = globalHelper.getCurrentFormattedDate();
      }

      let deviceData = await datadeviceService.getDataBetweenTwoTimeStamps(
        deviceId,
        startTime,
        endTime
      );

      if (deviceData && deviceData.problems) {
        return exits.badRequest(deviceData);
      } else {
        return exits.success(deviceData);
      }

    } catch (error) {
      sails.log.error('[datadevices > findDatadevice] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
