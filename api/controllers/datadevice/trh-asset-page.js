
const datadeviceService = require('../../services/datadevice/datadevice.service');
// const globalHelper = require('../../utils/globalhelper');
const selfUtils = require('../../utils/datadevice/trh-asset-page.util.js');


module.exports = {
  friendlyName: 'trhAssetPage',
  description : '',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      // required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    componentId: {
      type: 'string',
      required: true,
      example: 'kims_21',
      description: '',
    },
    componentList: {
      type: ['ref'],
      required: true,
      example: ['kims_21', 'kims_22'],
      description: '',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[datadevice > trhAssetPage] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[datadevice > trhAssetPage] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[datadevice > trhAssetPage] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try {
      let { componentId, componentList, _userMeta } = inputs;
      const siteId = _userMeta._site;
      let isAssetConfiguredAtTr = false;
      const allowedTonnageDeliveredParams = new Set(['tr','tptr'])

      const componentTrhMap = await datadeviceService.componentsTrhKeyMap(siteId, [componentId]);
      if(!allowedTonnageDeliveredParams.has(componentTrhMap[componentId])) return exits.badRequest({err: `${componentId} - COMPONENTS_NOT_CONFIGURED_FOR_TONNAGE_CALCULATION`});

      const tonnageDeliveredParameter = componentTrhMap[componentId];

      if(tonnageDeliveredParameter ==  'tr' ){
        isAssetConfiguredAtTr = true;
      }

      //fetching data from influx
      const getTrhDataFromInflux = async (component, trhParameter,durations)=>{
        const resultHolder = [];
        for(let _time in durations){
          if(trhParameter == 'tptr'){
            resultHolder.push( datadeviceService.getTotalTonnageDelivered(siteId,componentList, durations[_time].start, durations[_time].end, _time),  )
          } else if(trhParameter == 'tr'){
            resultHolder.push( datadeviceService.getChillerTonnageDelivered(siteId,component, durations[_time].start, durations[_time].end, _time),  )
          }
        }
        return {
          trh:await Promise.all(resultHolder),
          componentId:component
        }
      }

      //get timestamp
      let trhDuration = selfUtils.getTimestamp();

      //setting up default value to 0 for each duration
      const durationDefaultMap = Object.keys(trhDuration).reduce((acm,curr)=>{
        acm[curr]=0
        return acm;
      },{})

      //fetch raw trh data
      const queryResolver = [];
      queryResolver.push(getTrhDataFromInflux(componentId,'tptr',trhDuration))
      if(isAssetConfiguredAtTr){
        queryResolver.push(getTrhDataFromInflux(componentId,'tr',trhDuration))
      }

      const _result = await Promise.all(queryResolver)


      const trhData = selfUtils.trhSiteWiseDataMap(_result,durationDefaultMap);


      const responseObject = selfUtils.generateResponseObject(isAssetConfiguredAtTr,durationDefaultMap,trhData)
      return exits.success(responseObject);
    } catch(error) {
      console.error(error);
      sails.log.error('[datadevice > trhAssetPage] Error!');
      sails.log.error(error);
      return exits.serverError(error);
    }
  }
};
