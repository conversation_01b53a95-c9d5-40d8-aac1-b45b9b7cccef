const moment = require("moment-timezone");
const datadeviceService = require("../../services/datadevice/datadevice.service.js");
const selfUtils = require("../../utils/datadevice/devices-analytics-line.util.js");

module.exports = {
  friendlyName: "Devices analytics line",
  description: "",

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
      description: "User meta information added by default to authenticated routes",
    },
    siteId: {
      type: "string",
    },
    startTime: {
      type: "string",
      required: true,
      description: "Start time in date format",
    },
    endTime: {
      type: "string",
      required: true,
      description: "Start time in date format",
    },
    groupBy: {
      type: "string",
      required: true,
      description: "group by used to aggregate the result. Allowed values are d,h,m",
    },
    deviceIds: {
      type: "json",
      required: true,
      description: "list of device Ids",
    },
    params: {
      type: "string",
      required: true,
      description: "parameter name e.g kw,pf,vpp,a_amp,kva,kvar",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[datadevice > devices-analytics-line] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[datadevice > devices-analytics-line] Bad Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[datadevice > devices-analytics-line] Not Found!",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const isValidRequest = selfUtils.requestValidator(inputs);
      if (!isValidRequest) return exits.badRequest({ error: "Invalid request" });
      const { siteId, startTime, endTime, groupBy, deviceIds, params } = inputs;
      const timezone = await sails.helpers.getSiteTimezone.with({ siteId });
      const _startTimestamp = moment.tz(startTime, timezone).format('YYYY-MM-DDTHH:mm:ss.SSSZ');
      const _endTimestamp = moment.tz(endTime, timezone).format('YYYY-MM-DDTHH:mm:ss.SSSZ');
      const paramList = params.split(",").map((p) => p.trim());
      const allowedParams = ["kw", "pf", "vpp", "a_amp", "kva", "kvar"];
      const inputParams = paramList.filter((param) => allowedParams.includes(param));
      if (!inputParams.length) {
        return exits.badRequest({ error: "Invalid parameters" });
      }
      const res = await datadeviceService.generateDevicesAnalyticsData(
        siteId,
        deviceIds,
        _startTimestamp,
        _endTimestamp,
        groupBy,
        inputParams
      );
      return exits.success(res);
    } catch (error) {
      sails.log.error("[datadevice > devices-analytics-line] Error!");
      sails.log.error(error);
      exits.serverError(error);
    }
  },
};
