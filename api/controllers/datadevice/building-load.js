const dynamokeystoreService = require('../../services/dynamokeystore/dynamokeystore.service.js');
const datadeviceService = require('../../services/datadevice/datadevice.service.js');
const buildingLoadUtil = require('../../utils/datadevice/buildingload.util.js');

module.exports = {
  friendlyName: 'Building Load',
  description: 'Fetch building energy load pattern',

  inputs: {
    siteId: {
      type: 'string',
      required: true
    },
    days: {
      type: 'string'
    },
    week: {
      type: 'string'
    }
  },

  exits: {
    success: {
      description: 'Building load data retrieved successfully',
      responseType: 'ok'
    },
    databaseError: {
      description: 'Error occurred while querying the database',
      responseType: 'serverError'
    },
    invalidTimeRange: {
      description: 'The provided time range (days or week) is invalid',
      responseType: 'badRequest'
    },
    serverError: {
      description: 'An unexpected error occurred on the server',
      responseType: 'serverError'
    },
    badRequest: {
      description: 'Invalid request parameters',
      responseType: 'badRequest'
    }
  },

  fn: async function (inputs, exits) {
    try {
      const { siteId, days, week } = inputs;
      if ((days && !Array.isArray(days.split(','))) || (week && !Array.isArray(week.split(',')))) {
        return exits.invalidTimeRange('Invalid format for days or week');
      }
      const mainMeterSet = await dynamokeystoreService.findSitesMainMeterSet(siteId);
      const mainMeterList = Array.from(mainMeterSet);
      if (!mainMeterList.length) {
        sails.log.info(`[datadevice > building-load] siteId=${siteId} message="No main meter found for site"`);
        return exits.success({});
      }
      const consumptionTimeRanges = buildingLoadUtil.getTimeRangesForBuildingLoad(days, week);
      const buildingLoadByHour = await datadeviceService.fetchBuildingLoadByHour(siteId, mainMeterList, consumptionTimeRanges);
      const res = datadeviceService.getBuildingLoadByDay(buildingLoadByHour, mainMeterList.length, siteId);
      return exits.success(res);
    } catch (error) {
      sails.log.error(error);
      switch (error.code) {
        case 'E_SITE_NOT_FOUND':
          return exits.notFound({
            error: error.message
          });
        case 'E_INVALID_PARAMS':
          return exits.badRequest({
            error: error.message
          });
        default:
          return exits.databaseError({
            error: error.message
          });
      }
    }
  }
};
