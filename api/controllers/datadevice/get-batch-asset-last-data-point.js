const datadeviceService = require("../../services/datadevice/datadevice.service");
const util = require("../../utils/datadevice/recentdata.util");
const moment = require("moment");

module.exports = {
  friendlyName: "Get batch asset last data point",
  description: "Query last minute's data for a list of devices/components.",

  inputs: {
    assetList: {
      type: "json",
      required: true,
      example: ["abh-mys_1", 20539],
    },
    siteId: {
      type: "string",
      example: "abh-mys",
    },
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[datadevices > get-batch-asset-last-data-poin] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[datadevices > get-batch-asset-last-data-poin] Bad Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[datadevices > get-batch-asset-last-data-poin] Not Found!",
    }
  },

  async fn({ siteId, assetList }, exits) {
    try {
      if (!Array.isArray(assetList)) {
        return exits.badRequest({
          error: "Invalid asset list",
        });
      }
      const deviceList = assetList.filter(id => !isNaN(id));
      const componentList = assetList.filter(id => isNaN(id));
      const cachedData = await datadeviceService.getBatchCachedRecentData(assetList);
      const uncachedDeviceIds = deviceList.filter(id => !cachedData[id]);
      const uncachedComponentIds = componentList.filter(id =>!cachedData[id]);
      const fetchedData = await Promise.all([
        datadeviceService.getBatchDeviceLastDataPoint(siteId, uncachedDeviceIds),
        datadeviceService.getBatchComponentLastDataPoint(siteId, uncachedComponentIds)
      ]);
      const batchLastDataPoint = [...fetchedData[0], ...fetchedData[1], ...Object.values(cachedData)].filter(Boolean);
      return exits.success(batchLastDataPoint);
    } catch (error) {
      sails.log.error("[datadevices > get-batch-asset-last-data-point] Error!");
      sails.log.error(error);
      return exits.serverError(error);
    }
  },
};
