const datadeviceService = require('../../services/datadevice/datadevice.service');
const util = require('../../utils/datadevice/recentdata.util');
const moment = require('moment');

module.exports = {
  friendlyName: 'get-component-last-data-point',
  description: 'Query last minute\'s data for a list of deviceIds',
  example: [
    "curl --location 'localhost:1337/m2/site/suh-hdyd//last-data-point' \
--header 'Authorization: Bearer D6GGSY'",
  ],

  inputs: {
   componentId: {
    type: 'string',
   },
   siteId: {
    type: 'string'
   }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[datadevices > get-component-last-data-poin] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[datadevices > get-component-last-data-poin] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[datadevices > get-component-last-data-poin] forbidden Request!',
    },
  },

  async fn(inputs, exits) {
    try {
      const { componentId, siteId } = inputs;
      if (!siteId.trim().length) {
        return exits.badRequest({
          err: 'Invalid Site ID'
        })
      }

      if (!componentId.trim().length) {
        return exits.badRequest({
          err: 'Invalid component ID'
        })
      }
    
      const timezoneOffset =  await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'utcOffsetInMinute'})
      const cachedRecentData = await datadeviceService.getCachedRecentData(componentId);
      if (cachedRecentData) {
        const response = cachedRecentData.data;
        response.timestamp = moment(cachedRecentData.timestamp).utcOffset(timezoneOffset).format("YYYY-MM-DDTHH:mm:ss.SSSZ")
        return exits.success(response);
      }
      
      const startTime = moment().utcOffset(timezoneOffset).subtract(30, 'days').format("YYYY-MM-DDTHH:mm:ss.SSSZ");
      const endTime =moment().utcOffset(timezoneOffset).format("YYYY-MM-DDTHH:mm:ss.SSSZ");
      const deviceRecentData = await datadeviceService.fetchLatestComponentDataInRange(siteId, componentId, startTime, endTime);
      deviceRecentData.timezoneOffset = timezoneOffset;
      const recentData = util.formatRunMinutesData(deviceRecentData);
      return exits.success(recentData);
    } catch (error) {
      if (error.code == 'notFound') {
        return exits.badRequest({
          err: error.raw
        })
      }
      sails.log.error('[datadevices > get-component-last-data-poin] Error!');
      sails.log.error(error);
      return exits.serverError('Server has encountered an issue. Please contact admin to resolve this issue.');
    }
  },
};
