const datadeviceService = require('../../services/datadevice/datadevice.service');
const util = require('../../utils/datadevice/recentdata.util');
const globalhelper = require('../../utils/globalhelper');

module.exports = {
  friendlyName: 'recentData',
  description: 'Query last minute\'s data for a list of deviceIds',
  example: [
    'curl \'http://localhost:1337/m2/data/v2/recentdata\' --data - raw \'{"deviceList":["mgch_22","mgch_23","mgch_26"]}\'',
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    deviceList: {
      type: ['string'],
      example: ['2126', '2127', '2128'],
      description: 'Array of deviceIds',
      required: true,
    },
    timestamp: {
      type: 'string',
      example: 'YYYY-MM-DD HH:mm',
      description: 'timestamp to get data from',
      custom: globalhelper.isValidDateTime,
      required: false,
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[datadevices > recentData] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[datadevices > recentData] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[datadevices > recentData] forbidden Request!',
    },
  },

  async fn(inputs, exits) {
    try {
      const { deviceList,_userMeta } = inputs;
      let { timestamp } = inputs;
      const deviceDataMap = {};
      const $deviceDataMap = {};
      timestamp = timestamp || globalhelper.getCurrentFormattedDate();

      deviceList.forEach((deviceId) => {
        $deviceDataMap[deviceId] = datadeviceService.getDataNearbyTimeStamp(deviceId, timestamp, 2, 2, _userMeta._site);
      });
      try {
        for (const deviceId in $deviceDataMap) {
          deviceDataMap[deviceId] = await $deviceDataMap[deviceId];
        }
      } catch (error) {
        sails.log.error('Error fetching data for devices');
        sails.log.error(error);
        exits.serverError('Error fetching data for devices');
      }

      const returnObject = util.formatDataForFrontend(deviceDataMap);
      return exits.success(returnObject);
    } catch (error) {
      sails.log.error('[datadevices > recentData] Error!');
      sails.log.error(error);
      return exits.serverError(error);
    }
  },
};
