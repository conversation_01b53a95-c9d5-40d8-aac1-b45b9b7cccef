const moment = require("moment-timezone");

const datadeviceService = require("../../services/datadevice/datadevice.service");
const selfUtils = require("../../utils/datadevice/site-consumption.util.js");

module.exports = {
  friendlyName: "Site consumption",
  description: "",

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId", unitPref: { cons: "kwh" } },
      description: "User meta information added by default to authenticated routes",
    },
    startTime: {
      type: "string",
      required: true,
    },
    endTime: {
      type: "string",
      required: true,
    },
    groupBy: {
      type: "string",
      required: true,
      description: "group by used to aggregate the result. Allowed values are hour, day, month.",
    },
    emList: {
      type: "ref",
      required: true,
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[datadevice > site-consumption] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[datadevice > site-consumption] Bad Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[datadevice > site-consumption] Not Found!",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const isValidRequest = selfUtils.requestValidator(inputs);
      if (!isValidRequest) return exits.badRequest({ error: "Invalid request parameters" });
      const {
        _userMeta: {
          _site: siteId,
          unitPref: { cons: unit },
        },
        startTime,
        endTime,
        groupBy,
        emList,
      } = inputs;
      const timezone = await sails.helpers.getSiteTimezone.with({ siteId, timezoneFormat: "tz" });
      const _startTimestamp = moment(startTime).tz(timezone).format("YYYY-MM-DDTHH:mm:ssZ");
      const _endTimestamp = moment(endTime).tz(timezone).format("YYYY-MM-DDTHH:mm:ssZ");
      const res = await datadeviceService.generateSiteConsumptionData(
        siteId,
        emList,
        _startTimestamp,
        _endTimestamp,
        unit,
        groupBy
      );
      return exits.success(res);
    } catch (error) {
      sails.log.error("[datadevice > site-consumption] Error!", error);
      switch (error.status) {
        case 400:
          return exits.badRequest({ error: error.message });
        case 404:
          return exits.notFound({ error: error.message });
        default:
          return exits.serverError(error);
      }
    }
  },
};
