const moment = require("moment-timezone");
moment.tz.add(
  "Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6"
);
moment.tz.setDefault("Asia/Kolkata");

const datadeviceService = require("../../services/datadevice/datadevice.service");
const globalHelper = require("../../utils/globalhelper");
const selfUtils = require("../../utils/datadevice/run-hour-analysis.util.js");
const utils = require("../../utils/datadevice/utils.js");

module.exports = {
  friendlyName: "runHourAnalysis",
  description: "",
  example: [`curl -X GET "http://localhost:1337/`],

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
      description:
        "User meta information added by default to authenticated routes",
    },
    startTime: {
      type: "string",
      required: true,
      description: "Start time in YYYY-MM-DD format",
    },
    endTime: {
      type: "string",
      required: true,
      description: "Start time in YYYY-MM-DD format",
    },
    groupBy: {
      type: "string",
      required: true,
      description:
        "group by used to aggregate the result. Allowed values are d,h",
    },
    deviceIds: {
      type: "string",
      required: true,
      description: "list of component",
    },
    params: {
      type: "string",
      required: true,
      description:
        "parameter name e.g runminutes for run minute and kvah for consumption",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[datadevice > runHourAnalysisHighSide] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[datadevice > runHourAnalysisHighSide] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[datadevice > runHourAnalysisHighSide] forbidden Request!",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const isValidRequest = selfUtils.requestValidator(inputs);
      if (!isValidRequest) return exits.badRequest({problems:["invalid request"]});
      const {
        _userMeta: { _site: siteId },
        startTime,
        endTime,
        groupBy,
        deviceIds,
        params,
      } = inputs;
      let runHourAnalysisData, consumptionAnalysisData;
      const _startTimestamp = moment(startTime).format("YYYY-MM-DDTHH:00:00Z");
      const _endTimestamp = moment(endTime).format("YYYY-MM-DDTHH:00:00Z");
      const componentList = deviceIds.split(",");

      const allowedAnalysisParameter = ["runminutes", "kvah"];
      let inputParams = params.split(",");
      inputParams = allowedAnalysisParameter.filter(
        (param) => inputParams.indexOf(param) !== -1
      );

      if (!inputParams.length) return exits.badRequest({problems:["only runminutes and kvah allowed in params field"]});
      const responseObject = selfUtils.responseObjectFactory(
        inputParams,
        componentList
      );

      if (inputParams.indexOf("runminutes") !== -1) {
        runHourAnalysisData = await (async function generateRunHourAnalysis() {
          const promiseHolder = [];
          componentList.forEach((component) => {
            promiseHolder.push(
              datadeviceService.getRunHourAnalysisGraph({
                startTimestamp: _startTimestamp,
                endTimestamp: _endTimestamp,
                siteId: siteId,
                groupBy: groupBy,
                componentId: component,
              })
            );
          });
          const result = await Promise.all(promiseHolder);
          let runminuteAnalysis=selfUtils.formatRunminuteData(result);
          return runminuteAnalysis;
        })();
      }

      if(inputParams.indexOf("kvah") !== -1){
        consumptionAnalysisData = await datadeviceService.getConsumptionGraph(
          startTime,
          endTime,
          deviceIds,
          groupBy
        )

      }

      if(runHourAnalysisData){
        Object.assign(responseObject,runHourAnalysisData)
      }
      if(consumptionAnalysisData){
        for(let component  in consumptionAnalysisData){
          if(responseObject[component]){
            Object.assign(responseObject[component],consumptionAnalysisData[component])

          }
        }
      }
      return exits.success(responseObject);
    } catch (error) {
      sails.log.error("[datadevice > runHourAnalysisHighSide] Error!");
      sails.log.error(error);
      exits.serverError(error);
    }
  },
};
