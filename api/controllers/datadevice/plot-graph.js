
const datadeviceService = require('../../services/datadevice/datadevice.service');
const globalHelper = require('../../utils/globalhelper');
const selfUtils = require('../../utils/datadevice/plot-graph.util.js');
const utils = require('../../utils/datadevice/utils');

module.exports = {
  friendlyName: 'plotGraph',
  description: '',
  example: [
    `curl '0:1337/m2/analytic/v2/plot/line' --data '{"startTime":"2020-08-05 06:54","endTime":"2020-08-06 06:54","deviceId":"296","group":"day","params":["KW"]}'`,
    `curl '0:1337/m2/analytic/v2/plot/heatmap' --data '{"startTime":"2020-08-04","endTime":"2020-08-05","deviceId":"147","group":"hour","params":["KVA"]}'  -H "Content-Type: application/json"`
  ],

  inputs: {
    deviceId: {
      type: 'string',
      example: 'ssh_2',
      description: 'device/component id',
      required: true
    },
    startTime: {
      type: 'string',
      example: 'YYYY-MM-DD HH:mm',
      description: 'start Time to get data from',
      custom: globalHelper.isValidDateTime,
      required: true
    },
    endTime: {
      type: 'string',
      example: 'YYYY-MM-DD HH:mm',
      description: 'end Time to get data to',
      custom: globalHelper.isValidDateTime,
      required: true
    },
    group: {
      type: 'string',
      exmaple: 'hour',
      isIn: utils.GROUP_BY,
      description: 'group by parameter on min/hour/day'
    },
    params: {
      type: ['ref'],
      example: ['kva'],
      description: 'parameters of the device to plot data of'
    },
    type: {
      type: 'string',
      example: 'line',
      isIn: ['line', 'heatmap', 'buildingLoad', 'spectral'],
      description: 'graph type',
      required: true
    },
    _userMeta: {
      type: {},
      example: { 'id': 'userName' },
      required: true
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[datadevice > plotGraph] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[datadevice > plotGraph] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[datadevice > plotGraph] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try {
      let { params, group, startTime, endTime, deviceId, type } = inputs;
      let { _site: siteId, unitPref: unitPreference } = inputs._userMeta;
      let plotData = [];
      startTime = globalHelper.formatDateTime(startTime);
      endTime = globalHelper.formatDateTime(endTime);
      params = params.map(param => param.toLowerCase());
      let $deviceDatas = datadeviceService
        .getDeviceParamsDataBetween2TimestampInPreferredUnit(
          deviceId, params, siteId, startTime, endTime, unitPreference
        );
      let $metaData = datadeviceService
        .getGraphsMetaDataBetween2Timestamps(
          deviceId, startTime, endTime, params, type
        );
      let [deviceDatas, metaData] = await Promise.all([
        $deviceDatas,
        $metaData
      ]);

      if (deviceDatas.problems) {
        return exits.badRequest(deviceDatas.problems);
      }
      if (metaData.problems) {
        return exits.badRequest(metaData.problems);
      }

      switch (type) {
        case 'line':
          plotData = selfUtils.filterDataForLineGraph(
            deviceDatas, group, params, startTime, endTime
          );
          break;
        case 'heatmap':
          group = 'hour'; // currently we only support hour
          plotData = selfUtils.filterDataForHeatMap(
            deviceDatas, group, params, startTime, endTime
          );
          break;
        case 'spectral':
          plotData = selfUtils.filterDataForSpectralGraph(deviceDatas, params);
          break;
        default:
          return exits.badRequest({ problems: ['Invalid graph type'] });
      }

      let response = {
        [deviceId]: {
          plot: plotData
        },
        ...metaData
      }
      return exits.success(response);


    } catch (error) {
      sails.log.error('[datadevice > plotGraph] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
