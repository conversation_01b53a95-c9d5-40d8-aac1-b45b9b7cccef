const dataDeviceService = require("../../services/datadevice/datadevice.service");

module.exports = {
    friendlyName: 'getConsumptionPageData',
    description: 'Get consumption page data',
    inputs: {
        _userMeta: {
            type: {},
            example: { id: 'userName', _site: 'ssh', _role: 'role' },
            description: 'User related info attached by isAuthorized-policy.',
            required: true,
        },
        groupBy: {
            type: 'string',
            example: 'day, week, month',
            description: 'Group name',  
            isIn: ['hour','days', 'weeks', 'months', 'custom'],
            required: true,
        },
        start: {
            type: 'string',
            description: 'start timestamp',
        },
        end: {
            type: 'string',
            description: 'end timestamp',
        }
    },
    exits: {
        success: {
            description: 'All done.',
        },
        serverError: {
            description: 'Server Error!',
        },
        badRequest: {
            description: 'Bad Request!',
        },
        notFound: {
            description: 'Not Found!',
            statusCode: 404
        }
    },
    fn: async function (inputs, exits) {
        try {
            const { _site: siteId, id: userId} = inputs._userMeta;
            let $isUserPreferencesUpdated;
            const getEMList = await dataDeviceService.setUserPreferences({siteId, userId}, $isUserPreferencesUpdated);
            if (_.isEmpty(getEMList)) {
                return exits.success({
                    err: "Energy Meter Data Not Configured In Keystore",
                    data: null,
                })
            }           
            const devicesConsumptionResponseObject = await dataDeviceService.getConsumptionDashboard({
                siteId, 
                userId, 
                groupBy: inputs.groupBy, 
                start: inputs.start,
                end: inputs.end,
                unitPref: inputs._userMeta.unitPref
            }, getEMList);
            await $isUserPreferencesUpdated
            return exits.success(devicesConsumptionResponseObject);
        } catch(err) {
            sails.log.error(`[consumptionPageData] Server Error!`, err)
            if (err.code === 'E_NOT_EXIST') {
                return exits.notFound({
                    err: "User Preferences Does not exist",
                    data: null,
                })
            }
            return exits.serverError(err);
        }
    }
}