const moment = require("moment-timezone");
moment.tz.add("Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6");
moment.tz.setDefault("Asia/Kolkata");

const datadeviceService = require("../../services/datadevice/datadevice.service");
const globalHelper = require("../../utils/globalhelper");
const selfUtils = require("../../utils/datadevice/run-hour-analysis.util.js");
const utils = require("../../utils/datadevice/utils.js");

module.exports = {
  friendlyName: "runHourAnalysis",
  description: "",
  example: [`curl -X GET "http://localhost:1337/`],

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
      description: "User meta information added by default to authenticated routes",
    },
    siteId: {
      type: "string",
    },
    startTime: {
      type: "string",
      required: true,
      description: "Start time in YYYY-MM-DD format",
    },
    endTime: {
      type: "string",
      required: true,
      description: "Start time in YYYY-MM-DD format",
    },
    groupBy: {
      type: "string",
      required: true,
      description: "group by used to aggregate the result. Allowed values are d,h",
    },
    deviceIds: {
      type: "string",
      required: true,
      description: "list of component",
    },
    params: {
      type: "string",
      required: true,
      description: "parameter name e.g runminutes for run minute and kvah for consumption",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[datadevice > run-hour-analysis] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[datadevice > run-hour-analysis] Bad Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[datadevice > run-hour-analysis] Not Found!",
    }
  },

  fn: async function (inputs, exits) {
    try {
      const isValidRequest = selfUtils.requestValidator(inputs);
      if (!isValidRequest) return exits.badRequest({ error: "Invalid request" });
      const { siteId, startTime, endTime, groupBy, deviceIds, params } = inputs;
      const timezone = await sails.helpers.getSiteTimezone.with({ siteId, timezoneFormat: 'tz' });
      const _startTimestamp = moment(startTime).tz(timezone).format('YYYY-MM-DDTHH:00:00Z');
      const _endTimestamp = moment(endTime).tz(timezone).format('YYYY-MM-DDTHH:00:00Z');
      const componentList = deviceIds.split(",");
      const allowedAnalysisParameter = ["runminutes", "kvah"];
      let inputParams = params.split(",");
      inputParams = allowedAnalysisParameter.filter((param) => inputParams.indexOf(param) !== -1);
      if (!inputParams.length)
        return exits.badRequest({ error: "only runminutes and kvah allowed in params field" });

      let runminuteAnalysis = {},
        consumptionAnalysis = {};
      if (inputParams.indexOf("runminutes") !== -1) {
        runminuteAnalysis = await datadeviceService.generateRunHourAnalysis(
          siteId,
          componentList,
          _startTimestamp,
          _endTimestamp,
          groupBy
        );
      }
      if (inputParams.indexOf("kvah") !== -1) {
        consumptionAnalysis = await datadeviceService.generateConsumptionAnalysis(
          siteId,
          componentList,
          _startTimestamp,
          _endTimestamp,
          groupBy
        );
      }

      const res = {};
      const allComponents = new Set([
          ...Object.keys(runminuteAnalysis),
          ...Object.keys(consumptionAnalysis),
        ])
      for(const component of allComponents) {
        res[component] = {
          runminutes: runminuteAnalysis[component] || [],
          kvah: consumptionAnalysis[component] || [],
        }
      }
      return exits.success(res);
    } catch (error) {
      sails.log.error("[datadevice > run-hour-analysis] Error!");
      sails.log.error(error);
      exits.serverError(error);
    }
  },
};
