

/**
 * This is written to move graphQL to this repo.
 * A similar API exists and need testing./m2/analytic/v2/plot/:type
 * Delete this API once integrated.
 */

const datadeviceService = require('../../services/datadevice/datadevice.service');
const globalHelper = require('../../utils/globalhelper');
const selfUtils = require('../../utils/datadevice/plot-graph.util.js');
const utils = require('../../utils/datadevice/utils');
const axios = require('axios');


module.exports = {
    friendlyName: 'boxPlot',
    description: 'to plot line graph in detail analytics',
    example: [
        `curl -X POST 0:1337/m2/graph/v2/customGraph -H "Content-Type: application/json" --data  {"startTime":"2021-09-22 08:45:48","endTime":"2021-09-22 09:45:48","siteId":"mgch","group":"","paramsX":"actuator","deviceIdX":"mgch_14","paramsY":"hum","deviceIdY":"2582"}`
    ],

    inputs: {
        deviceIdX: {
            type: 'string',
            example: 'ssh_2',
            description: 'device/component info array of objects',
            required: true
        },
        deviceIdY: {
            type: 'string',
            example: 'ssh_2',
            description: 'device/component info array of objects',
            required: true
        },
        startTime: {
            type: 'string',
            example: '2021-09-14T18: 30: 00',
            description: 'start Time to get data from',
            custom: globalHelper.isValidDateTime,
            required: true
        },
        endTime: {
            type: 'string',
            example: '2021-09-15T18:30:00',
            description: 'end Time to get data to',
            custom: globalHelper.isValidDateTime,
            required: true
        },
        paramsX: {
            type: 'string',
            exmaple: 'ahum',
            description: 'Id of a site'
        },
        paramsY: {
            type: 'string',
            exmaple: 'ahum',
            description: 'Id of a site'
        },
    },

    exits: {
        serverError: {
            responseType: 'serverError',
            description: '[datadevice > boxplotGraph] Server Error!',
        },
        badRequest: {
            responseType: 'badRequest',
            description: '[datadevice > boxplotGraph] Bad Request!',
        },
        forbidden: {
            responseType: 'forbidden',
            description: '[datadevice > boxplotGraph] forbidden Request!',
        },
    },

    fn: async function (inputs, exits) {
        try {
            let browserHeader = this.req.headers.authorization; // md5(browser hash)
            const {
                deviceIdX, deviceIdY, startTime, endTime, paramsX, paramsY
            } = inputs;
            const { JOULETRACK_API_ENDPOINT } = process.env
            const payload = {
                startTime,
                endTime,
                xAxis: {
                    deviceId: deviceIdX,
                    param: paramsX,
                },
                yAxis: {
                    deviceId: deviceIdY,
                    param: paramsY,
                },
            };
            const obj = {};
            try {
                const response = await axios.post(`${JOULETRACK_API_ENDPOINT}/analyzeCustomAxis`, payload, {
                    headers: { Authorization: `${browserHeader}` },
                });
                const { data } = response;
                const modified = data.data.map((el) => el.map((e) => parseFloat(e)));
                obj.data = modified;
            } catch (err) {
                sails.log.error(`${JOULETRACK_API_ENDPOINT}/analyzeCustomAxis`, err);
            }
            exits.success({ data: { boxData: obj } });
        } catch (error) {
            sails.log.error('[datadevice > heatmapGraph] Error!');
            sails.log.error(error);
            exits.serverError(error);
        }
    }
};
