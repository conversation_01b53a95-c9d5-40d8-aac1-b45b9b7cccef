/**
 * This is written to move graphQL to this repo.
 * A similar API exists and need testing./m2/analytic/v2/plot/:type
 * Delete this API once integrated.
 */

const axios = require('axios');
const globalHelper = require('../../utils/globalhelper');
const utils = require('../../utils/datadevice/utils');

module.exports = {
  friendlyName: 'lineGraph',
  description: 'to plot line graph in detail analytics',
  example: [
    `curl -X POST 0:1337/m2/graph/v2/line -H "Content-Type: application/json" --data  {"startTime":"2021-09-22 08:41:18","endTime":"2021-09-22 09:41:18","siteId":"mgch","group":"minute","devices":"[{"deviceId":"mgch_14","params":["ra"],"controlParams":[]}]}"}`
  ],

  inputs: {
    devices: {
      type: 'json',
      example: [{ deviceId: 'ssh_2', params: [] }],
      description: 'device/component info array of objects',
      required: true
    },
    startTime: {
      type: 'string',
      example: 'YYYY-MM-DD HH:mm',
      description: 'start Time to get data from',
      custom: globalHelper.isValidDateTime,
      required: true
    },
    endTime: {
      type: 'string',
      example: 'YYYY-MM-DD HH:mm',
      description: 'end Time to get data to',
      custom: globalHelper.isValidDateTime,
      required: true
    },
    group: {
      type: 'string',
      exmaple: 'hour',
      isIn: utils.GROUP_BY,
      description: 'group by parameter on min/hour/day'
    },
    siteId: {
      type: 'string',
      exmaple: 'mgch',
      description: 'Id of a site'
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[datadevice > lineGraph] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[datadevice > lineGraph] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[datadevice > lineGraph] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try {
      let browserHeader = this.req.headers.authorization; // md5(browser hash)
      const {
        group, startTime, endTime, devices,
      } = inputs;
      const { JOULETRACK_API_ENDPOINT } = process.env
      const promises = devices.map((device) => {
        const { deviceId, params } = device;
        const mparams = params.join(',');
        const promise = axios.post(
          `${JOULETRACK_API_ENDPOINT}/analytics/line`,
          {
            deviceId,
            group,
            params: mparams,
            startTime,
            endTime,
          },
          {
            headers: { Authorization: `${browserHeader}` },
          },
        );
        return promise;
      });
      let obj = [];
      try {
        const response = await Promise.all(promises);
        let index = 0;
        obj = devices.map((device) => {
          const { data } = response[index];
          const { deviceId, params } = device;
          const list = params.map((p) => {
            const dataForParam = []
            Object.entries(data[deviceId].plot).forEach((plot) => {
              const [plotParameter, plotValueInArray] = plot
              if (plotParameter.toLowerCase() === p.toLowerCase()) {
                dataForParam.push(...plotValueInArray)
              }
            })
            return { param: p, data: dataForParam, deviceId }
          });
          index += 1;
          return {
            plot: list,
          };
        });
      } catch (err) {
        sails.log.error(`${JOULETRACK_API_ENDPOINT}/analytics/line`, err);
      }
      exits.success({ data: { linesData: obj } });
    } catch (error) {
      sails.log.error('[datadevice > lineGraph] Error!');
      sails.log.error(error);
      return exits.serverError(error);
    }
  }
};
