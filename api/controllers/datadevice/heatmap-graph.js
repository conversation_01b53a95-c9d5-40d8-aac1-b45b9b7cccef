

/**
 * This is written to move graphQL to this repo.
 * A similar API exists and need testing./m2/analytic/v2/plot/:type
 * Delete this API once integrated.
 */


const datadeviceService = require('../../services/datadevice/datadevice.service');
const globalHelper = require('../../utils/globalhelper');
const selfUtils = require('../../utils/datadevice/plot-graph.util.js');
const utils = require('../../utils/datadevice/utils');
const axios = require('axios');


module.exports = {
    friendlyName: 'heatmapGraph',
    description: 'to plot line graph in detail analytics',
    example: [
        `curl -X POST 0:1337/m2/graph/v2/heatmap -H "Content-Type: application/json" --data  {"startTime":"2021-09-22 08:41:18","endTime":"2021-09-22 09:41:18","siteId":"mgch","params":"actuator","deviceId":"mgch_15"}`

    ],

    inputs: {
        deviceId: {
            type: 'string',
            example: 'ssh_2,ssh_5',
            description: 'device/component info array of objects',
            required: true
        },
        startTime: {
            type: 'string',
            example: '2021-09-14T18: 30: 00',
            description: 'start Time to get data from',
            custom: globalHelper.isValidDateTime,
            required: true
        },
        endTime: {
            type: 'string',
            example: '2021-09-15T18:30:00',
            description: 'end Time to get data to',
            custom: globalHelper.isValidDateTime,
            required: true
        },
        group: {
            type: 'string',
            exmaple: 'hour',
            isIn: utils.GROUP_BY,
            description: 'group by parameter on min/hour/day'
        },
        params: {
            type: 'string',
            exmaple: 'ahum',
            description: 'Id of a site'
        },
        siteId: {
            type: 'string',
            exmaple: 'mgch',
            description: 'Id of a site'
        },
    },

    exits: {
        serverError: {
            responseType: 'serverError',
            description: '[datadevice > heatmapGraph] Server Error!',
        },
        badRequest: {
            responseType: 'badRequest',
            description: '[datadevice > heatmapGraph] Bad Request!',
        },
        forbidden: {
            responseType: 'forbidden',
            description: '[datadevice > heatmapGraph] forbidden Request!',
        },
    },

    fn: async function (inputs, exits) {
        try {
            let browserHeader = this.req.headers.authorization; // md5(browser hash)
            const {
                group, startTime, endTime, deviceId, params
            } = inputs;
            const { JT_API_V2_API_ENDPOINT } = process.env;
            const paramsArray = params.constructor.name === 'Array' ? params : [params];
            const obj = {};
            try {
                const response = await axios.post(
                    `${JT_API_V2_API_ENDPOINT}/m2/analytic/v2/plot/heatmap`,
                    {
                        deviceId,
                        group,
                        params: paramsArray,
                        startTime,
                        endTime,
                    },
                    {
                        headers: { Authorization: `${browserHeader}` },
                    },
                );
                const { data } = response;
                obj.plot = data[deviceId].plot[params].plot;
                obj.user = data[deviceId].user;
                obj.maintenance = data[deviceId].maintenance;
                obj.minimum = data[deviceId].plot[params].minimum;
                obj.maximum = data[deviceId].plot[params].maximum;
            } catch (err) {
                sails.log(err);
            }
            exits.success({ data: { heatmapData: obj } });
        } catch (error) {
            sails.log.error('[datadevice > heatmapGraph] Error!');
            sails.log.error(error);
            exits.serverError(error);
        }
    }
};
