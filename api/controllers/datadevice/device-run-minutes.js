const datadeviceService = require('../../services/datadevice/datadevice.service');
const globalHelper = require('../../utils/globalhelper');
const selfUtils = require('../../utils/datadevice/device-run-minutes.util');
const utils = require('../../utils/datadevice/utils.js');

module.exports = {
  friendlyName: 'deviceRunHours',
  description: 'Find running hours of device. If device.param == kwh and kwh > 0.1, increment runminutes of device.',
  example: [
    'curl 0:1337/m2/data/v2/devicerunminutes -X POST -H "Content-Type: application/json" --data \'{"deviceIds":["4731"], "group":"hour", "startTime": "2021-03-01 08:00", "endTime": "2021-03-06 08:00"}\'',
  ],

  inputs: {
    _userMeta: {
      type: {},
      example: { id: 'userName', _site: 'ssh', _role: 'role' },
      description: 'User related info attached by isAuthorized-policy.',
      required: true,
    },
    deviceIds: {
      type: ['ref'],
      example: ['2291', '2222'],
      description: 'Device ids to get run minutes of',
      required: true,
    },
    startTime: {
      type: 'string',
      example: 'YYYY-MM-DD HH:mm',
      description: 'start Time to get data from',
      custom: globalHelper.isValidDateTime,
      required: true,
    },
    endTime: {
      type: 'string',
      example: 'YYYY-MM-DD HH:mm',
      description: 'end Time to get data to',
      custom: globalHelper.isValidDateTime,
      required: true,
    },
    group: {
      type: 'string',
      exmaple: 'hour',
      isIn: utils.GROUP_BY,
      required: true,
      description: 'group by parameter on min/hour/day',
    },

  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[datadevice > deviceRunHours] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[datadevice > deviceRunHours] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[datadevice > deviceRunHours] forbidden Request!',
    },
  },

  async fn(inputs, exits) {
    try {
      let {
        deviceIds,
        startTime,
        endTime,
        group,
      } = inputs;
      const { _site: siteId, unitPref: unitPreference } = inputs._userMeta;
      const params = ['kwh'];
      let $devicesData; let devicesData; let
        plotData;

      startTime = globalHelper.formatDateTime(startTime);
      endTime = globalHelper.formatDateTime(endTime);

      $devicesData = deviceIds.map((deviceId) => datadeviceService.getDeviceParamsDataBetween2TimestampInPreferredUnit(
        deviceId, params, siteId, startTime, endTime, unitPreference,
      ));

      devicesData = await Promise.all($devicesData);
      plotData = devicesData.reduce((acc, deviceData, i) => {
        const deviceId = deviceIds[i];
        const deviceRunMinute = utils.groupDevicedataOnParamBetweenStartAndEndTime(
          deviceData, group, params, startTime, endTime, selfUtils.countDeviceIsRunning,
        );
        acc[deviceId] = Object.keys(deviceRunMinute).reduce((acc1, deviceParam) => {
          // backward compatibility with frontend. # TODO fix this later
          acc1.runminutes = deviceRunMinute[deviceParam].reverse();
          return acc1;
        }, {});
        return acc;
      }, {});

      return exits.success(plotData);
    } catch (error) {
      sails.log.error('[datadevice > deviceRunHours] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  },
};
