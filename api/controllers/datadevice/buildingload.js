
const datadeviceService = require('../../services/datadevice/datadevice.service');
const dynamokeystoreService = require('../../services/dynamokeystore/dynamokeystore.public');
const globalHelper = require('../../utils/globalhelper');
const selfUtils = require('../../utils/datadevice/buildingload.util.js');
const utils = require('../../utils/datadevice/utils.js');

module.exports = {
  friendlyName: 'buildingload',
  description : 'Given weeks and days of those week, find the building energy load/consumption on those days',
  example: [
    `curl -X POST 0:1337/m2/analytic/v2/presets/buildingload -H "Content-Type: application/json" --data '{"weeks": [1]}'`,
  ],

  inputs: {
    _userMeta: {
      type: {},
      example: { 'id': 'userName' },
      required: true
    },
    weeks: {
      type: ['ref'],
      example: [0,1,2,3,4],
      description: 'List of all the weeks of whose building loading we need of',
      custom: selfUtils.isInputIntergerArray, // only numbers in weeks are allowed
    },
    days: {
      type: ['ref'],
      example: [0,1,2,3,4],
      description: 'List of all days in the week we need building load of',
      custom: selfUtils.isInputIntergerArray, // only numbers in days are allowed
    }

  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[datadevice > buildingload] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[datadevice > buildingload] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[datadevice > buildingload] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      let { weeks, days } = inputs;
      let { _site: siteId } = inputs._userMeta;
      let datesToCalulcateLoadOf, today = globalHelper.toMoment().startOf('day');
      let mainEnergyMeters, buildingConsumptionGraph;

      if (weeks === undefined || days === undefined) {
        let yesterday = today.clone().subtract(1, 'day').format();
        let dateAWeekBeforeYesterday = today.clone().subtract(8, 'day').format();
        datesToCalulcateLoadOf = [yesterday, dateAWeekBeforeYesterday];
      } else {
        datesToCalulcateLoadOf = selfUtils.getDatesFromWeekAndDays(weeks, days);
      }

      let mainEnergyMetersSet = await dynamokeystoreService.findSitesMainMeterSet(siteId);
      mainEnergyMeters = Array.from(mainEnergyMetersSet);

      // for each device in mainEnergyMeters, get consumption on datesToCalulcateLoadOf groupBy hour

      let $devicesConsumptionData = [];
      for(let emDeviceId of mainEnergyMeters) {
        for( let endTime of datesToCalulcateLoadOf) {
          let startTime = globalHelper.toMoment(endTime).subtract(1, 'day').format(); // if endTime = 2020-08-09 00:00,  startTime = 2020-08-08 00:00
          let endMinMinus1 = globalHelper.toMoment(endTime).subtract(1, 'minute').format(); // if endTime = 2020-08-09 12:00,  endMinMinus1 = 2020-08-08 23:59

          $devicesConsumptionData.push(
            datadeviceService.getDeviceParamsDataBetween2TimestampInPreferredUnit(
              emDeviceId,
              ['kw'],
              siteId,
              startTime,
              endMinMinus1
            )
          )
        }
      }

      let devicesConsumptionData = await Promise.all($devicesConsumptionData);
      let totalDevices = mainEnergyMeters.length;
      buildingConsumptionGraph = selfUtils.calculateBuildingConsumption(devicesConsumptionData, totalDevices);

      return exits.success(buildingConsumptionGraph);

    } catch(error) {
      sails.log.error('[datadevice > buildingload] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
