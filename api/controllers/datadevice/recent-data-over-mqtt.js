const flaverr = require('flaverr');
const datadeviceService = require('../../services/datadevice/datadevice.service');
const IotCoreService = require('../../services/iotCore/iotCore.public');

module.exports = {
  friendlyName: '',
  description: 'to fetch the recent data request by IoT',
  inputs: {
    uuid: {
      type: 'string',
      required: true,
    },
    componentId: {
      type: 'string',
      required: true,
      example: 'agdcc-kol_1',
    },
    siteId: {
      type: 'string',
      required: true,
      example: 'agdcc-kol',
    },
    paramList: {
      type: 'ref',
      required: true,
      example: ['runminutes'],
    },
    lastValidDays: {
      type: 'number',
      required: false,
    },
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[datadevice > recent-data-over-mqtt] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[datadevice > recent-data-over-mqtt] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[datadevice > recent-data-over-mqtt] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[datadevice > recent-data-over-mqtt] Not Found',
    },
    entityUnprocessable: {
      statusCode: 422
    },
  },
  async fn({
    siteId,
    componentId,
    paramList,
    uuid,
    lastValidDays
  }, exits) {
    try {
      if (!(Array.isArray(paramList) && paramList.every((element) => typeof element === 'string'))) {
        throw flaverr(
          { code: 'E_INPUT_VALIDATION' },
          new Error('Invalid input: \'paramList\' must be an array of strings'),
        );
      }
      const recentData = await datadeviceService.getComponentRecentDataByParams({
        siteId,
        componentId,
        paramList,
        lastValidDays,
      });
      const recentDataResponse = {
        ...recentData,
        statusCode: 1,
        uuid
      };
      /**publish to iot core*/
      await IotCoreService.publish(
        `${siteId}/response/${componentId}/recentData`,
        recentDataResponse,
      );
      return exits.success(recentDataResponse);
    } catch (error) {
      sails.log.error(error);
      sails.log.error('[datadevice > recent-data-over-mqtt]');
      if (error?.code?.includes('[AWS-IOT-CORE-SERVICE')) {
        return exits.unprocessableEntity({
          message: error?.message,
          code: error?.code
        });
      }
    }
  },
};
