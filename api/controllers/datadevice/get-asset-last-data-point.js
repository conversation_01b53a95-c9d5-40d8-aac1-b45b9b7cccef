const datadeviceService = require('../../services/datadevice/datadevice.service');

module.exports = {
  friendlyName: 'Get asset last data point',
  description: 'Query last data point for a device/component',

  inputs: {
    deviceId: {
      type: 'string',
    },
    siteId: {
      type: 'string'
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[datadevices > get-asset-last-data-point] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[datadevices > get-asset-last-data-point] Bad Request!',
      statusCode: 400,
    },
    notFound: {
      statusCode: 404,
      description: '[datadevices > get-asset-last-data-point] Not Found!',
    },
    noContent: {
      statusCode: 204,
      description: '[datadevices > get-asset-last-data-point] No Content!',
    }
  },

  async fn({ deviceId, siteId }, exits) {
    try {
      const isDevice = !isNaN(deviceId);
      let lastDataPoint;
      if (isDevice) {
        lastDataPoint = await datadeviceService.getDeviceLastDataPoint(siteId, deviceId);
      } else {
        lastDataPoint = await datadeviceService.getComponentLastDataPoint(siteId, deviceId);
      }
      if (!lastDataPoint) {
        return exits.noContent();
      }
      return exits.success(lastDataPoint);
    } catch (error) {
      sails.log.error('[datadevices > get-asset-last-data-point] Error!');
      sails.log.error(error);
      return exits.serverError('Server has encountered an issue. Please contact admin to resolve this issue.');
    }
  },
};
