const authService = require("../../services/auth/auth.service");
const userService = require("../../services/user/user.service");
const cacheService = require("../../services/cache/cache.service");
const { toJSON } = require("lodash/seq");

module.exports = {
  friendlyName: "refreshToken",
  description:
    "Takes in a secret browser hash from user which should pre-exist in redis at time of login and issues a new JWT token if the secret exists in cache.",
  example: [
    `curl '0:1337/auth/v2/refreshToken' -H 'Content-Type: application/json' --data '{"secret":"a55f559f0dd14c908149e156c11de2e8","siteId":"ssh","prevSiteId":null}'`,
  ],

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
    },
    siteId: {
      type: "string",
      required: true,
      example: "Id of site",
    },
    secret: {
      type: "string",
      required: true,
      example: "ccda1683d8c97f8f2dff2ea7d649b42c",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[auth > refreshToken] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[auth > refreshToken] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[auth > refreshToken] Forbidden Request!",
    },
    unauthorized: {
      statusCode: 401,
      description: "[auth > refreshToken] Session Expired!",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const {
        secret,
        siteId,
        _userMeta: { id: userId, _h_: clientHashToken },
      } = inputs;

      const [_, tokenSecret] = clientHashToken.split("_");

      if (tokenSecret !== secret) {
        return exits.forbidden({ err: "Unauthorized access. Please try again." });
      }

      const userMetaDetails = await userService.getUserAuthDetailsWithDefaultSite(userId, siteId);

      const { userInfo, userPreference, defaultSiteId, message } = userMetaDetails;
      const { name, passwordUpdatedAt } = userInfo;
      let unitPref;
      if (userPreference?.unitPreference) {
        unitPref =
          typeof userPreference?.unitPreference === "string"
            ? JSON.parse(userPreference?.unitPreference)
            : userPreference?.unitPreference;
      }
      const tokenPayload = {
        id: userId,
        _role: userPreference?.role || null,
        _site: defaultSiteId,
        name,
        _h_: `${defaultSiteId}_${secret}`,
        unitPref: unitPref || sails.config.custom.DEFAULT_UNIT_PREF,
        pwUpdatedTime: passwordUpdatedAt,
      };

      // Mystery code to query data from redis
      await authService.getUserInfoFromClientToken(clientHashToken);

      const token = await authService.issueJWTToken(tokenPayload);

      await cacheService.setUserAuthToken(userId, siteId, token);

      const response = { token };
      if (message) response.message = message;

      return exits.success(response);
    } catch (error) {
      sails.log.error("[auth > refreshToken] Error!");
      sails.log.error(error);
      switch (error.code) {
        case "E_UNAUTHORIZED_USER":
          return exits.unauthorized({ err: error.message });
        default:
          return exits.serverError(error);
      }
    }
  },
};
