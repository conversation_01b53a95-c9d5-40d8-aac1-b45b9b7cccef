const authService = require('../../services/auth/auth.service');
const cacheService = require("../../services/cache/cache.service");

module.exports = {
  friendlyName: 'logout',
  description: 'Logs out a user by blacklisting their token',
  inputs: {},
  exits: {
    success: {
      statusCode: 200,
      description: 'User logged out successfully',
    },
    badRequest: {
      statusCode: 400,
      description: 'Invalid token',
    },
    serverError: {
      statusCode: 500,
      description: 'Server error',
    },
  },
  fn: async function (inputs, exits) {
    try {
      const authorizationHeader = this.req.headers.authorization;
      if (!authorizationHeader) {
        return exits.badRequest({ message: 'Authorization header is missing' });
      }

      const [authBearer, token] = authorizationHeader.split(' ');
      if (!token || authBearer.toLowerCase() !== 'bearer') {
        return exits.badRequest({ message: 'Invalid authorization token format' });
      }

      let decodedToken;
      try {
        decodedToken = authService.verifyJWTToken(token);
      } catch (err) {
        return exits.badRequest({ message: 'Invalid or expired token' });
      }

      const { id: userId } = decodedToken;

      try {
        await cacheService.deleteAllUserTokens(userId);
      } catch (e) {
        sails.log.error('[auth > logout] Redis error during token deletion:', e);
      }

      return exits.success({ message: 'Logout successful.' });
    } catch (error) {
      sails.log.error('[auth > logout] Error logging out:', error);
      return exits.serverError(error);
    }
  },
};
