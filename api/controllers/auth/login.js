
const authService = require('../../services/auth/auth.service');
const SiteService = require('../../services/site/site.service');
const userService = require('../../services/user/user.public');
const cacheService = require("../../services/cache/cache.service");
const { getUserSiteIfDefaultSiteNotExist, decryptPassword } = require('../../services/user/user.service');
const { getSiteIdFromRedirectionURL, extractSiteIdFromRedirectURL } = require('../../utils/auth/login');

module.exports = {
  friendlyName: 'login',
  description: 'Log a user into site',
  example: [
    // valid user
    `curl -X POST -H 'Content-Type: application/json' --data '{"userId":"<EMAIL>", "password":"password"}' -H "Authorization: HASHCODE"  http://localhost:1337/auth/v2/login`,
    // invalid username
    `curl -X POST -H 'Content-Type: application/json' --data '{"userId":"<EMAIL>", "password":"password"}' -H "Authorization: HASHCODE"  http://localhost:1337/auth/v2/login`,
  ],
  oldroute: '/v1/auth/login',
  inputs: {
    userId: {
      type: 'string',
      example: '<EMAIL>',
      required: true
    },
    password: {
      type: 'string',
      example: 'password',
      required: true
    },
    currentSite: {
      type: 'string',
      example: 'acc-che',
      description: 'New site requested'
    },
    redirectLocation: {
      type: 'string',
      example: '/configuration',
      description: 'The url to be redirected'
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[auth > login] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[auth > login] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[auth > login] Forbidden request',
    },
    notFound: {
      statusCode: 404,
      description: '[auth > login] Not Found!',
    }
  },

  fn: async function (inputs, exits) {

    const { userId, password, redirectLocation } = inputs;
    let browserHash = this.req.headers.authorization; // md5(browser hash)
    if (!browserHash) {
      return exits.badRequest({ 'problems': ['authorization header is missing'] })
    }
    try {
      let userPreference,
        sessionRestoredSiteId = null,
        isRequestedSiteExist = null;

      const decryptedPassword = decryptPassword(password);

      const user = await userService.authenticate(userId, decryptedPassword);
      if (!user) return exits.forbidden({ 'problems': ['UserName/Password is incorrect'] });

      const { defaultSite, name } = user
      const userDefaultSite = await getUserSiteIfDefaultSiteNotExist(userId, defaultSite)
      if (!userDefaultSite) {
        return exits.forbidden({ 'problems': ['You do not have any site access. Please connect with admin team.'] });
      }

      if (redirectLocation) isRequestedSiteExist = await SiteService.siteExists(extractSiteIdFromRedirectURL(redirectLocation));
      if (redirectLocation && await authService.userHasSiteAccess(userId, extractSiteIdFromRedirectURL(redirectLocation))) sessionRestoredSiteId = extractSiteIdFromRedirectURL(redirectLocation)


      userPreference = await userService.getUserPreference(userId, sessionRestoredSiteId || userDefaultSite)

      if (!userPreference) {
        return exits.forbidden({ 'problems': ['You do not have any default site. Please connect with admin team.'] });
      }

      let { unitPreference, role, siteId } = userPreference;
      const pwUpdatedTime = user.passwordUpdatedAt;
      let tokenObj = {
        'id': userId,
        '_role': role,
        name,
        '_site': siteId,
        'unitPref': unitPreference || sails.config.custom.DEFAULT_UNIT_PREF,
        'pwUpdatedTime': pwUpdatedTime
      };
      tokenObj['_h_'] = siteId + '_' + browserHash
      let token = authService.issueJWTToken(tokenObj);

      await cacheService.setUserAuthToken(userId, siteId, token);

      let _responseObj = {
        token
      }
      if (
        redirectLocation && !sessionRestoredSiteId && !isRequestedSiteExist
      ) {
        _responseObj.message = `The site you are trying to reach ${extractSiteIdFromRedirectURL(redirectLocation)} does not exist. Redirecting to ${userDefaultSite}...`
      } else if (redirectLocation && !sessionRestoredSiteId && isRequestedSiteExist) {
        _responseObj.message = `Access denied for ${extractSiteIdFromRedirectURL(redirectLocation)}, Redirecting to ${userDefaultSite}`
      } else if (redirectLocation && sessionRestoredSiteId) {
        _responseObj.redirectLocation = redirectLocation
      }
      return exits.success(_responseObj);

    } catch (error) {
      if (error.code === 'E_USER_NOT_FOUND') {
        return exits.notFound({
          problems: ['User not found']
        })
      } else {
        sails.log.error('[auth > login] Error!');
        sails.log.error(error);
        return exits.serverError(error);
      }
    }
  }
};
