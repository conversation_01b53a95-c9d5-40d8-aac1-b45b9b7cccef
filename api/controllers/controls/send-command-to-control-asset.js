const JouleTrackCommandBuilder = require('../../services/controls/lib/JouleTrackCommandBuilder');
const CommandExecutionService = require('../../services/controls/lib/CommandExecutionService');

module.exports = {
  friendlyName: 'sendCommandToControlAsset',
  description: 'This api will send the command to control an asset like turning on/off or change the frequency',
  example: [''],
  inputs: {
    controlAbbr: {
      type: 'string',
      required: true,
      example: 'turnOnOffCoolingTower',
    },
    componentId: {
      type: 'string',
      required: true,
      example: 'mgch_1',
    },
    commandRequest: {
      type: 'json',
      required: true,
      example: {
        value: 1,
        parameter: 'stop'
      }
    },
    _userMeta: {
      type: 'json',
      required: true,
      example: {
        id: 'userId',
        _role: 'role',
        _site: 'siteId'
      },
      description: 'User meta information added by default to authenticated routes',
    },

  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[controls > sendCommandToControlAsset] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[controls > sendCommandToControlAsset] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[controls > sendCommandToControlAsset] unauthorized!',
    },
    duplicateCommand: {
      statusCode: 422
    },
    iotCommunicationLayerDown: {
      statusCode: 502
    },
    jouletrackMqttMicroserviceError: {
      statusCode: 502
    }
  },
  async fn(inputs, exits) {
    try {
      const {
        commandRequest,
        componentId,
        controlAbbr,
        _userMeta
      } = inputs;
      const {
        commandParam,
        commandValue
      } = commandRequest;

      //TODO: move this code service file of controls

      const jouleTrackCommandBuilder = await JouleTrackCommandBuilder.start(componentId);
      const jouleTrackCommand = jouleTrackCommandBuilder
        .selectAsset(componentId)
        .selectControl(controlAbbr)
        .setCommand({
          commandParam,
          commandValue
        })
        .setExecutorDetail({
          userId: _userMeta.id,
          socketId: _userMeta._h_
        })
        .build();
      if (!(await jouleTrackCommand.isControlInReadyState())) {
        return exits.duplicateCommand({
          error: 'E_ALREADY_COMMAND_IN_PROGRESS',
          message: 'Previous command is not completed yet. please wait for sometime for new command'
        });
      }

      await JouleTrackCommandBuilder.setCommandDeviceControllerId(jouleTrackCommand);
      const commandExecution = new CommandExecutionService(jouleTrackCommand);
      await commandExecution.execute();
      await jouleTrackCommand.saveCommand();

      return exits.success({
        commandUid: jouleTrackCommand.uniqId,
        deviceId: jouleTrackCommand.deviceId,
        device_abbr: jouleTrackCommand.device_abbr,
        componentId: jouleTrackCommand.componentId,
        siteId: jouleTrackCommand.siteId,
        value: jouleTrackCommand.commandValue,
      });

    } catch (error) {
      sails.log.error('CODE X', error.code);
      if (error.HTTP_STATUS_CODE == 400) {
        return exits.badRequest({
          message: error.message,
          code: error.code
        });
      }
      if (error?.code?.includes('[AWS-IOT-CORE-SERVICE')) {
        return exits.duplicateCommand({
          message: error?.message,
          code: error?.code
        });
      }
      if (error.HTTP_STATUS_CODE == 502) {
        return exits.iotCommunicationLayerDown({
          message: error.message,
          code: error.code
        });
      }
      if (error.HTTP_STATUS_CODE == 503) {
        return exits.jouletrackMqttMicroserviceError({
          message: error.message,
          code: error.code
        });
      }
      if (error.code === 'COMPONENT_ID_NOT_EXIST') {
        return exits.badRequest({
          err: error.message,
          code: error.code
        });
      }
      sails.log.error('[controls > sendCommandToControlAsset] Error!');
      sails.log.error(error);
      return exits.serverError(error);
    }
  },

};
