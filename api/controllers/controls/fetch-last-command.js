const { fetchComponentLastCommand } = require("../../services/controls/control.public");
const { validateFetchLastCommand } = require("../../utils/controls/requestValidator");
module.exports = {
  friendlyName: "get-last-command",
  description: "This api is fetching command history of a controls",
  example: [""],
  inputs: {
    siteId: {
      type: "string",
      example: "sjo-del",
    },
    componentId: {
      type: "string",
      example: "mgch_1",
    },
    controlAbbr: {
      type: "string",
      example: "status",
    },
  },
  exits: {
    serverError: {
      responseType: "serverError",
      description: "[controls > get-last-command] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[controls > get-last-command] Bad Request!",
    },
    unauthorized: {
      responseType: "unauthorized",
      description: "[controls > get-last-command] unauthorized!",
    },
    success: {
      statusCode: 200,
    },
  },
  async fn(inputs, exits) {
    try {
      const { siteId, componentId, controlAbbr } = inputs;
      validateFetchLastCommand({ siteId, componentId, controlAbbr  });
      const commandHistoryMetaInfo = await fetchComponentLastCommand(
        siteId,
        componentId,
        controlAbbr
      );
      const response = {
        'data': commandHistoryMetaInfo,
        'message': ''
      };
      if (_.isEmpty(commandHistoryMetaInfo)) response.message = 'No Data Available';
      return exits.success(response);
    } catch (e) {
      if (e.code === "E_COMPONENT_ID_NOT_FOUND") {
        return exits.badRequest({
          err: e.message,
        });
      } else if (e.code === "E_INPUT_VALIDATION") {
        return exits.badRequest({
          err: e.message,
        });
      } else if (e.code === 'E_INVALID_SITE_ID'){
        return exits.badRequest({
          err: e.message,
        });
      } else {
        sails.log.error(e.message, e);
        return exits.serverError({err:'Server has encountered an error.Please contact the administrator.'});
      }
    }
  },
};