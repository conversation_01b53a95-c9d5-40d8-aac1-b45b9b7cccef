const ControlService = require('../../services/controls/control.service')
module.exports = {
  friendlyName: 'getAllControlStateOfAllComponent',
  description: 'This api is fetching all the state of control for all component',
  example: [''],
  inputs: {
    componentIds: {
      type: 'json',
      required: true,
      example: ['mgch_1', 'mgch_2'],
    },
    _userMeta: {
      type: 'json',
      required: true,
      example: {
        id: 'userId',
        _role: 'role',
        _site: 'siteId'
      },
      description: 'User meta information added by default to authenticated routes',
    },

  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[controls > getAllControlStateOfAllComponent] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[controls > getAllControlStateOfAllComponent] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[controls > getAllControlStateOfAllComponent] unauthorized!',
    },
    success: {
      statusCode: 200
    }
  },
  async fn({_userMeta:{_site:siteId}, componentIds }, exits) {
    try {
      if (!Array.isArray(componentIds)) {
        return exits.badRequest({ message: "Component Id's must be in Array" });
      }
      componentIds = _.uniq(componentIds);
      return exits.success(await ControlService.fetchControlStateByComponents(siteId,componentIds));
    } catch (error) {
      if (error instanceof Error && error.HTTP_STATUS_CODE === 400) {
        return exits.badRequest({
          error: error.code,
          message: error.message,
        });
      }

      sails.log.error('[controls > getAllControlStateOfAllComponent] Error!');
      sails.log.error(error);
      return exits.serverError(error);
    }
  }
};

