const { getControlCommandHistoryMetaInfo } = require("../../services/controls/control.public");
module.exports = {
  friendlyName: "get-command-history-meta-info",
  description: "This api is fetching command history meta info",
  example: [""],
  inputs: {
    siteId: {
      type: "string",
      example: "sjo-del",
    },
    componentId: {
      type: "string",
      example: "mgch_1",
    },
  },
  exits: {
    serverError: {
      responseType: "serverError",
      description: "[controls > get-command-history-meta-info] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[controls > get-command-history-meta-info] Bad Request!",
    },
    unauthorized: {
      responseType: "unauthorized",
      description: "[controls > get-command-history-meta-info] unauthorized!",
    },
    success: {
      statusCode: 200,
    },
  },
  async fn({ siteId, componentId }, exits) {
    try {
      const commandHistoryMetaInfo = await getControlCommandHistoryMetaInfo(siteId, componentId);
      return exits.success(commandHistoryMetaInfo);
    } catch (e) {
      if (e.code === 'E_COMPONENT_ID_NOT_FOUND') {
        return exits.badRequest({
          err: e.message
        })
      } else {
        sails.log.error(e.message, e);
        return exits.serverError(e);
      }
    }
  },
};
