const CommandRepository = require('../../services/controls/lib/CommandRepository');
const FeedbackBuilder = require('../../services/controls/lib/JouleTrackCommandFeedbackBuilder');
const { notifyJouleTrackPublicRoom } = require('../../services/socket/socket.service');
const JouleTrackControlFeedbackService = require('../../services/controls/lib/JouleTrackControlFeedbackHandler');

module.exports = {
  friendlyName: 'handlerIoTFeedbackForJouletrackCommand',
  description: 'this webhook is handling the Jouletrack command feedback from IoT and managing them',
  example: [''],
  inputs: {},
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[controls > handlerIoTFeedbackForJouletrackCommand] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[controls > handlerIoTFeedbackForJouletrackCommand] Bad Request!',
    },
    success: {
      statusCode: 200
    }
  },
  async fn(inputs, exits) {
    try {
      const rawFeedbackMessage = this.req.body;
      const {
        status: IoTCommandStatus,
        uniqId: commandUid
      } = rawFeedbackMessage;
      const repo = new CommandRepository();
      const commandData = await repo.fetchJouleTrackCommandDetailByCommandId(commandUid);
      if (!commandData) {
        sails.log(`command does not exist or command expired. commandId:${commandUid}`);
        return exits.badRequest({
          error: `command does not exist or command expired. commandId:${commandUid}`,
          data: { ...rawFeedbackMessage } || {},
          errorCode: `COMMAND_UID_NOT_FOUND`
        });
      }
      const JTCommandFeedbackInstance = new JouleTrackControlFeedbackService();
      const updatedControlState = await JTCommandFeedbackInstance.changeControlState(IoTCommandStatus, commandUid, rawFeedbackMessage);
      if (!updatedControlState) {
        sails.log(`Command State Change Not required`);
        return exits.success({ message: 'Command State Change Not required' });
      }
      const feedbackBuilderInstance = new FeedbackBuilder();
      const feedbackPayload = feedbackBuilderInstance
        .setComponentId(updatedControlState.componentId)
        .setSiteId(updatedControlState.siteId)
        .setCommandDetail({
          value: updatedControlState.value,
          commandAbbr: updatedControlState.commandAbbr,
          controlAbbr: updatedControlState.controlAbbr,
          executionTimestamp: updatedControlState.commandExecutionTsInUnix,
          executedBy: updatedControlState.userId,
          commandUid: commandUid,
          userId: updatedControlState.userId
        })
        .setControlState(IoTCommandStatus)
        .setRawFeedback(rawFeedbackMessage)
        .build();
      await notifyJouleTrackPublicRoom(updatedControlState.siteId, 'JouleTrackCommandFeedback', { data: feedbackPayload });
      return exits.success(feedbackPayload);
    } catch (e) {
      if (e.HTTP_STATUS_CODE == 400) {
        return exits.badRequest({
          error: e.message,
          data: e.data || {},
          errorCode: e.code
        });
      }
      sails.log.error(e);
      return exits.serverError(e);
    }
  },
};
