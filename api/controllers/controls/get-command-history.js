const { fetchComponentCommandList } = require("../../services/controls/control.public");
const { validateCommandHistoryList } = require("../../utils/controls/requestValidator");
module.exports = {
  friendlyName: "get-command-history",
  description: "This api is fetching command history of a controls",
  example: [""],
  inputs: {
    siteId: {
      type: "string",
      example: "sjo-del",
    },
    componentId: {
      type: "string",
      example: "mgch_1",
    },
    controlAbbr: {
      type: "string",
      example: "status",
    },
    commandSource: {
      type: "string",
      example: "recipe",
    },
    rows: {
      type: "number",
      example: 8,
      defaultsTo: 8,
    },
    page: {
      type: "number",
      example: 1,
      defaultsTo: 1,
    },
  },
  exits: {
    serverError: {
      responseType: "serverError",
      description: "[controls > get-command-history] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[controls > get-command-history] Bad Request!",
    },
    unauthorized: {
      responseType: "unauthorized",
      description: "[controls > get-command-history] unauthorized!",
    },
    success: {
      statusCode: 200,
    },
  },
  async fn(inputs, exits) {
    try {
      const { siteId, componentId, rows, page, commandSource, controlAbbr } = inputs;
      const commandHistorySearchObj = {
        rows: rows,
        offset: (page-1) * rows,
        commandSource: commandSource,
        controlAbbr: controlAbbr,
      };
      validateCommandHistoryList({ rows, page, commandSource });
      const commandHistoryMetaInfo = await fetchComponentCommandList(
        siteId,
        componentId,
        commandHistorySearchObj
      );
      return exits.success(commandHistoryMetaInfo);
    } catch (e) {
      if (e.code === "E_COMPONENT_ID_NOT_FOUND") {
        return exits.badRequest({
          err: e.message,
        });
      } else if (e.code === "E_INPUT_VALIDATION") {
        return exits.badRequest({
          err: e.message,
        });
      } else {
        sails.log.error(e.message, e);
        return exits.serverError(e);
      }
    }
  },
};