const ControlService = require('../../services/controls/control.service')
module.exports = {
  friendlyName: 'getAllControlStateByComponent',
  description: 'This api is fetching all the state of control',
  example: [''],
  inputs: {
    componentId: {
      type: 'string',
      required: true,
      example: 'mgch_1',
    },
    _userMeta: {
      type: 'json',
      required: true,
      example: {
        id: 'userId',
        _role: 'role',
        _site: 'siteId'
      },
      description: 'User meta information added by default to authenticated routes',
    },

  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[controls > getAllControlStateByComponent] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[controls > getAllControlStateByComponent] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[controls > getAllControlStateByComponent] unauthorized!',
    },
    success: {
      statusCode: 200
    }
  },
  async fn(inputs, exits) {
    try {
      const { componentId } = inputs;

      const {controlsState}  = await ControlService.fetchControlStateByComponentId(componentId)
      return exits.success(controlsState)
    } catch (error) {
      if(error && error.hasOwnProperty('HTTP_STATUS_CODE') && error.HTTP_STATUS_CODE === 400){
        return exits.badRequest({
          error:error.code,
          message:error.message
        })
      }
      sails.log.error('[controls > getAllControlStateByComponent] Error!');
      sails.log.error(error);
      return exits.serverError(error);
    }
  },
};

