const { updateComponentValidateInputs } = require('../../utils/component/inputValidation');
const componentService= require('../../services/component/component.service');
const { notifyJouleTrackPublicRoom } = require('../../services/socket/socket.service');
const auditEventLogService = require('../../services/auditEventLog/auditEventLog.public');
const componentUtils = require('../../utils/component/utils');

module.exports = {
  friendlyName: 'edit-component',
  description: 'edit component',
  inputs: {
    component: {
      type: 'json',
      required: true
    },
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: '<EMAIL>', _role: 'role', _site: 'siteId' },
    },
  },
  exits: {
    serverError: {
      statusCode: 500,
      responseType: "serverError",
      description: "Server Error",
    },
    forbidden: {
      statusCode: 403,
      description: "Invalid User",
    },
    notFound: {
      statusCode: 404,
      description: "Not Found",
    },
    success: {
      statusCode: 200,
      description: "Record Updated Successfully",
    },
    badRequest: {
      statusCode: 400,
      description: "Bad Request",
    },
  },
  fn: async function(inputs, exits) {
    try {
      const { component, _userMeta:{ id:userId } } = inputs;
      if (!component) return exits.badRequest({err: 'Component data not found'});
      let {
        deviceId,
        siteId,
      } = updateComponentValidateInputs(component);
      let componentData = await  Components.findOne({
        deviceId,
      })
      if (!componentData) return exits.badRequest({err: 'No such component device found'});
      if (componentData.siteId != siteId) return exits.badRequest({err: 'Component for specific site does not exist'})
      componentData.data = componentData.data &&  JSON.parse(componentData.data) || [];
      componentData.controls = componentData.controls && JSON.parse(componentData.controls) || [];


      const {
        addedDevices,
        deletedDevices
      } = componentUtils.logChangedDevice(componentData, component)


      await componentService.updateComponentInfoInDevices({
        addedDevices,
        deletedDevices,
        deviceId,
        siteId
      })
      const updatedComponentData = await componentService.updateComponent(component, componentData);
      await componentService.removeCachedComponentCategories(siteId)
      let response = await componentService.getSiteDeviceComponent(componentData, updatedComponentData)
      if (_.isEmpty(response)) return exits.notFound({err: `No component found for device ${deviceId}`})

      await notifyJouleTrackPublicRoom(
        siteId,
        "components",
        {
          event: "update",
          data: response,
        }
      );

      const auditPayload = {
        event_name: "state_update_component",
        user_id: userId,
        site_id: siteId,
        asset_id: deviceId,
        req: this.req,
        prev_state: componentData,
        curr_state: updatedComponentData,
      };

      auditEventLogService.emit(auditPayload);
      return exits.success(response);
    } catch(e) {
      switch (e.code) {
        case 'E_COMPONENT_NOT_UPDATED': {
          sails.log.error('Component device not found');
          return exits.forbidden({err: e.message})
        }
        case 'E_DEVICES_NOT_FOUND': {
          sails.log.error('Device not found');
          return exits.badRequest({err: e.message})
        }
        case 'E_TRANSACTION_FAILED' : {
          return exits.forbidden({err: e.message})
        }
        case 'E_INPUT_VALIDATION': {
          return exits.badRequest({err: e.message})
        }
        case 'E_DEVICE_NOT_FOUND': {
          return exits.badRequest({err: e.message});
        }
        case 'E_CONTROLLER_CATEGORY_MISSING': {
          return exits.badRequest({err: e.message});
        }
        default:  {
          sails.log.error(e);
          emitter.emit('stashChange', this.req)
          return exits.serverError(e);
        }
      }
    }
  }
}
