const { validateUserConfigParam } = require('../../utils/component/inputValidation');
const componentService= require('../../services/component/component.public');
module.exports = {
  friendlyName: 'edit-component',
  description: 'edit component',
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    },
    componentId: {
      type: 'string',
    },
    abbr: {
      type: 'string',
    },
    expressionDataValue: {
      type: 'string',
    },
    siteId: {
      type: 'string',
    }
  },
  exits: {
    serverError: {
      statusCode: 500,
      responseType: "serverError",
      description: "Server Error",
    },
    forbidden: {
      statusCode: 403,
      description: "Invalid User",
    },
    notFound: {
      statusCode: 404,
      description: "Not Found",
    },
    success: {
      statusCode: 200,
      description: "Record Updated Successfully",
    },
    badRequest: {
      statusCode: 400,
      description: "Bad Request",
    },
    unprocessableEntity: {
      statusCode: 422,
    },
  },
  fn: async function(inputs, exits) {
    try {
      const { _userMeta: { id: userId }, siteId, abbr, expressionDataValue, componentId} = inputs
      validateUserConfigParam({siteId, abbr, expressionDataValue, componentId})
      await componentService.sendUpdateUserConfigDataParamRequestToIOT({siteId, componentId, abbr, expressionDataValue, userId});
      return exits.success({
        message: 'Request has been sent successfully'
      });
    } catch(e) {
      if (e.HTTP_STATUS_CODE == 400) {
          sails.log('Error > bad-request [send-request-to-update-user-config-param]')
          return exits.badRequest({err: e.message})
      } else if (e.HTTP_STATUS_CODE == 422) {
        sails.log('Error > unprocessed-entity [send-request-to-update-user-config-param]')
        return exits.unprocessableEntity({
          err: e.message,
          data: e.data
        })
      } else  {
          sails.log.error('error > send-request-to-update-user-config-param',e);
          return exits.serverError(e);
      }
    }
  }
}
