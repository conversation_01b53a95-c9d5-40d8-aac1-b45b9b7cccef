const componentService = require('../../services/component/component.service');

module.exports = {
  friendlyName: 'Get control relationship by component',
  description: '',
  inputs: {
    componentId: {
      type: 'string',
      required: true,
      example: 'mgch_1'
    },
  },
  exits: {
    serverError: {
      statusCode: 500,
      responseType: "serverError",
      description: "Server Error",
    },
    forbidden: {
      statusCode: 403,
      description: "Invalid User",
    },
    notFound: {
      statusCode: 404,
      description: "Not Found",
    },
    success: {
      statusCode: 200,
      description: "Fetched control relationship by componentId",
    },
    badRequest: {
      statusCode: 400,
      description: "Bad Request",
    },
  },
  fn: async function ({ componentId }, exits) {
    try {
      const controlRelationship = await componentService.fetchConfiguredControlsWithSyncStatus(componentId);
      return exits.success(controlRelationship);
    } catch (error) {
      if (error.code == 'COMPONENT_ID_NOT_EXIST' || error.code == 'DRIVER_DETAIL_NOT_EXIST' || error.code == 'E_INVALID_DEVICE') {
        return exits.badRequest({ error: error.message });
      }
      if (error.code == 'E_DEVICE_NOT_FOUND' || error.code == 'E_CONTROLLER_NOT_ATTACHED_TO_DEVICE' || error.code == 'E_CONTROLLER_NOT_FOUND') {
        return exits.notFound({ error: error.message });
      }
      sails.log.error("component > getControlRelationship", error);
      return exits.serverError(error);
    }
  }
}
