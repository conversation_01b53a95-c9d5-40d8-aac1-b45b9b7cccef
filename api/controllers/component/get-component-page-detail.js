const componentService = require("../../services/component/component.public");

module.exports = {
  friendlyName: "getComponentPageDetail",
  description: "Get the details to render on the component page",
  inputs: {
    siteId: {
      type: "string",
      required: true,
      example: "gob-coi",
    },
    componentId: {
      type: "string",
      required: true,
      example: "gob-coi_52",
    },
  },
  exits: {
    serverError: {
      statusCode: 500,
      responseType: "serverError",
      description: "Server Error",
    },
    forbidden: {
      statusCode: 403,
      description: "Invalid User",
    },
    notFound: {
      statusCode: 404,
      description: "Not Found",
    },
    success: {
      statusCode: 200,
      description: "Success",
    },
    badRequest: {
      statusCode: 400,
      description: "Bad Request",
    },
  },
  fn: async function ({ siteId, componentId }, exits) {
    try {
      const componentPageDetail = await componentService.getComponentPageDetail(
        siteId,
        componentId,
      );
      const componentPageResponseObject = {
        deviceId: componentPageDetail.deviceId,
        deviceType: componentPageDetail.deviceType,
        driverType: componentPageDetail.driverType,
        siteId: componentPageDetail.siteId,
        controllerId: componentPageDetail.controllerId,
        isInMaintenanceMode: componentPageDetail?.isInMaintenanceMode ?? "0",
        modeLabel: componentPageDetail.modeLabel,
        svgId: componentPageDetail.svgId,
        data: componentPageDetail.data,
        controls: componentPageDetail.controls,
        name: componentPageDetail?.name,
        controlRelationshipMap: componentPageDetail.controlRelationshipMap,
      };
      return exits.success(componentPageResponseObject);
    } catch (error) {
      switch (error.code) {
        case "COMPONENT_ID_NOT_EXIST":
          return exits.notFound({ error: error.message });
        case "DRIVER_DETAIL_NOT_EXIST":
          return exits.badRequest({ error: error.message });
        default:
          return exits.serverError(error);
      }
    }
  },
};
