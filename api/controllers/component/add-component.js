const ComponentService = require("../../services/component/component.service");
module.exports = {
  friendlyName: "add-component-informations",
  description: "carry informations of components of a particular site",
  example: [],
  inputs: {
    siteId: {
      type: "string",
      required: true,
      example: "mah-che",
    },
    name: {
      type: "string",
      required: true,
      example: "Cooling Tower 8 SJPL",
    },
    deviceType: {
      type: "string",
      required: true,
      example: "coolingTower",
    },
    controllerId: {
      type: "string",
      required: true,
      example: "8625",
    },
    regionId: {
      type: "string",
      required: true,
      example: "wphl",
    },
  },
  exits: {
    serverError: {
      statusCode: 500,
      responseType: "serverError",
      description: "Server Error",
    },
    forbidden: {
      statusCode: 403,
      description: "Invalid User",
    },
    notFound: {
      statusCode: 404,
      description: "Not Found",
    },
    created: {
      statusCode: 201,
      description: "Record Created",
    },
    badRequest: {
      statusCode: 400,
      description: "Bad Request",
    },
  },
  fn: async function (inputs, exits) {
    try {
      let { siteId, name, controllerId, regionId, deviceType } =
        inputs;
      let _expectedControllerId = await ComponentService.generateComponentId(
        siteId
      );
      if (_expectedControllerId === null)
        return exits.badRequest({ err: "Can not generate deviceId" });
      sails.log(_expectedControllerId);
      const NewComponent = {
        siteId,
        name,
        deviceId: _expectedControllerId,
        deviceType,
        controllerId,
        regionId,
        driverType: 0,
      };
      await Components.create(NewComponent);
      await Devices.update(
        { deviceId: controllerId, siteId: siteId },
        { componentId: _expectedControllerId }
      );
      let responseObject = {
        siteId: NewComponent.siteId,
        name: NewComponent.name,
        deviceId: NewComponent.deviceId,
        deviceType: NewComponent.deviceType,
        controllerId: NewComponent.controllerId,
        regionId: NewComponent.regionId,
        driverType: NewComponent.driverType,
        createdAt: NewComponent.createdAt,
        updatedAt: NewComponent.updatedAt,
      };
      return exits.created(responseObject);
    } catch (err) {
      sails.log("component > add-component");
      sails.log.error(err);
      return exits.serverError(err);
    }
  },
};
