const componentService = require('../../services/component/component.service');
module.exports = {
  friendlyName: 'Get all component config',
  description: '',
  inputs: {
    componentIds: {
      type: 'json',
      required: true,
      example: ['mgch_1', 'mgch_2'],
    },
  },
  exits: {
    serverError: {
      statusCode: 500,
      responseType: "serverError",
      description: "Server Error",
    },
    forbidden: {
      statusCode: 403,
      description: "Invalid User",
    },
    notFound: {
      statusCode: 404,
      description: "Not Found",
    },
    success: {
      statusCode: 200,
      description: "Fetched control relationships of all components",
    },
    badRequest: {
      statusCode: 400,
      description: "Bad Request",
    },
  },
  fn: async function ({ componentIds }, exits) {
    try {
      if (!Array.isArray(componentIds)) return exits.badRequest({ error: `Array of component Id(s) is required` });

      const componentRelationshipPromises = await Promise.allSettled(componentIds.map((componentId) => componentService.getComponentConfig(componentId)));
      const errors = [];
      const componentRelationship = componentRelationshipPromises.reduce((acc, curr) => {
        if (curr['status'] === 'fulfilled') {
          const controlsRelationshipResponse = {
            device_id: curr['value'].deviceId,
            controlRelationshipMap: curr['value'].controlRelationshipMap.controls
          };
          acc.push(controlsRelationshipResponse);
        } else {
          errors.push(curr);
        }
        return acc;
      }, []);

      if (!errors.length) {
        sails.log(errors);
      }

      return exits.success(componentRelationship);
    }catch (err) {
      if (err) {
        sails.log.error("modes > getComponentsMode",err);
        return exits.serverError(err);
      }
    }
  }

}
