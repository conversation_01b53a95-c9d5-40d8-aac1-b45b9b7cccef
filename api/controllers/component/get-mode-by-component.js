const componentService = require("../../services/component/component.service");
module.exports = {
  friendlyName: "getMode",
  description: "Fetching Modes for a particular Component",
  example: [],
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: {
        id: "userId",
        _role: "role",
        _site: "siteId",
      },
      description: "User meta information added by default to authenticated routes",
    },
    componentId: {
      type: "string",
      required: true,
      description: "The selected component Id",
      example: "mgch_1",
    },
    systemId: {
      type: "number",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[modes > getMode] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[modes > getMode] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[modes > getMode] forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[modes > getMode] Not Found",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { componentId, systemId } = inputs;
      const filter = {};
      if (systemId) {
        filter.systemId = systemId;
      }
      const componentModeDetails = await componentService.getLastKnownModeByComponentId(
        componentId,
        filter
      );
      return exits.success(componentModeDetails);
    }
    catch (err) {
      switch (err.code) {
        case "E_SITE_NOT_FOUND": {
          return exits.notFound({
            err: err.message,
          });
        }
        case "E_COMPONENT_NOT_FOUND": {
          return exits.notFound({
            err: err.message,
          });
        }
        case "E_NO_CONTROL_RELATIONSHIP_EXISTS": {
          return exits.badRequest({
            err: err.message,
          });
        }
        case "E_CONTROL_NOT_FOUND": {
          return exits.badRequest({
            err: err.message,
          });
        }
        case "E_SYSTEM_NOT_FOUND": {
          return exits.badRequest({
            err: err.message,
          });
        }
        case 'COMPONENT_ID_NOT_EXIST': {
          return exits.badRequest({
            err: err.message,
          })
        }
        default: {
          sails.log("modes > getMode");
          sails.log.error(err);
          return exits.serverError(err);
        }
      }
    }
  },
};
