
const componentService = require('../../services/component/component.service');
module.exports = {
  friendlyName: 'getComponent',
  description: 'Fetching components info for one site..',
  example: [
    'curl -X GET "http://localhost:1337/site/v2/site/:key/:value',
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    siteId:{
      type:'string',
      example:'rpml-bij',
      required:true
    }
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[recipe > getComponent] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[recipe > getComponent] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[recipe > getComponent] forbidden Request!',
    },
  },
  async fn(inputs, exits) {
    try {
        const { siteId } = inputs;
       if(!siteId){
            return res.badRequest({ "err": "Invalid request format" });
       }
        const components = await componentService.getComponents(siteId);
        return exits.success(components);
      } catch (error) {
        sails.log.error('[site > getComponent] Error!');
        sails.log.error(error);
        exits.serverError(error);
      }
  },
};