const { isValidFileContent } = require('../../utils/globalhelper');
const fs = require("fs").promises;
const s3Service = require("../../services/s3/s3.service");
module.exports = {
    friendlyName: "uploadComponentFiles",
    description: "Upload files to S3 for a specific component",
    example: [],
    inputs: {
        _userMeta: {
            type: "json",
            required: true,
            example: {
                id: "userId",
                _role: "role",
                _site: "siteId",
            },
            description: "User meta information added by default to authenticated routes",
        },
        bucket: {
            type: "string",
            required: true,
            description: "The name of the S3 bucket",
        },
        path: {
            type: "string",
            required: true,
            description: "The S3 path where files will be uploaded",
        },
    },

    exits: {
        success: {
            description: "Files uploaded successfully.",
        },
        serverError: {
            responseType: "serverError",
            description: "[component > uploadComponentFiles] Server Error!",
        },
        badRequest: {
            responseType: "badRequest",
            description: "[component > uploadComponentFiles] Bad Request!",
        },
        forbidden: {
            responseType: "forbidden",
            description: "[component > uploadComponentFiles] forbidden Request!",
        }
    },

    fn: async function (inputs, exits) {
        try {
            const { bucket, path } = inputs;

            const uploadedFiles = await new Promise((resolve, reject) => {
                this.req.file("files").upload(async (err, uploadedFiles) => {
                    if (err) return reject(err);
                    resolve(uploadedFiles);
                });
            });

            if (!uploadedFiles || uploadedFiles.length === 0) {
                return exits.badRequest({ message: "No files provided for upload." });
            }

            const uploadResults = [];

            for (const file of uploadedFiles) {
                const { fd: tempFilePath, filename, type: fileType } = file;
                let jsonContent;

                const fileContentBuffer = await fs.readFile(tempFilePath);
                if (fileType === "application/json") {
                    jsonContent = JSON.parse(fileContentBuffer.toString("utf8"));

                    if (!isValidFileContent(Buffer.from(JSON.stringify(jsonContent)), fileType)) {
                        return exits.badRequest({ error: "Invalid JSON content." });
                    }
                } else {
                    // Validate other file types
                    if (!isValidFileContent(fileContentBuffer, fileType)) {
                        return exits.badRequest({ error: "File content invalid." });
                    }
                }

                const uniqueFileName = `${path}/${filename}`;
                const result = await s3Service.upload(fileType === "application/json" ? JSON.stringify(jsonContent) : tempFilePath, uniqueFileName, fileType, bucket);
                uploadResults.push({
                    fileName: filename,
                    s3Path: result.Location,
                });
            }
            
            return exits.success({
                message: "Files uploaded successfully.",
                uploadedFiles: uploadResults,
            });
        }
        catch (err) {
            sails.log.error("Error uploading files to S3:", err);
            return exits.serverError({err});
        }
    },
};
