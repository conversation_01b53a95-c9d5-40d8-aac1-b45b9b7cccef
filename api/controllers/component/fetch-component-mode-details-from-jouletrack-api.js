const componentService = require("../../services/component/component.service");
module.exports = {
  friendlyName: "Fetch mode details from jouletrack api",
  description: "",

  inputs: {
    componentIds: {
      type: 'json',
      required: true,
      example: ['mgch_1', 'mgch_2'],
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[component > fetch-mode-details-from-jouletrack] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[component > fetch-mode-details-from-jouletrack] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[component > fetch-mode-details-from-jouletrack] forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[component > fetch-mode-details-from-jouletrack] Not Found",
    },
  },

  fn: async function ({ componentIds }, exits) {
    try {
      if (!Array.isArray(componentIds)) return exits.badRequest({ error: `Array of component Id(s) is required` });
      componentIds = _.uniq(componentIds);
      const getLastKnownModesPromises = await Promise.allSettled(componentIds.map(componentId=>componentService.getLastKnownModeByComponentId(componentId)));
      const errors = [];
      const getLastKnownModes = getLastKnownModesPromises.reduce((acc,curr)=>{
        if(curr.status === 'fulfilled'){
          acc.push(curr['value']);
        }else{
          errors.push(curr);
        }
        return acc;
      },[]);


      if(errors && !errors.length){
        sails.log(errors);
      }

      return exits.success(getLastKnownModes);
    } catch (err) {
      if (err) {
          sails.log.error("fetch-mode-details-from-jouletrack > getComponentsMode",err);
          return exits.serverError(err);
      }
    }
  },
};
