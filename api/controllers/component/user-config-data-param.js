const { validateUpdateUserConfigDataParamFeedback } = require('../../utils/component/inputValidation');
const componentService= require('../../services/component/component.public');
module.exports = {
  friendlyName: 'edit-component',
  description: 'edit component',
  inputs: {
    componentId: {
      type: 'string',
    },
    siteId: {
      type: 'string',
    },
    requestId: {
      type: 'string',
    },
    status: {
      type: 'string',
    },
    error: {
      type: 'string',
    }
  },
  exits: {
    serverError: {
      statusCode: 500,
      responseType: "serverError",
      description: "Server Error",
    },
    forbidden: {
      statusCode: 403,
      description: "Invalid User",
    },
    notFound: {
      statusCode: 404,
      description: "Not Found",
    },
    success: {
      statusCode: 200,
      description: "Record Updated Successfully",
    },
    badRequest: {
      statusCode: 400,
      description: "Bad Request",
    },
  },
  fn: async function(inputs, exits) {
    try {
      const { siteId, componentId, requestId, status, error} = inputs
      validateUpdateUserConfigDataParamFeedback({ siteId, componentId, requestId, status})
      await componentService.updateCommandUCDParam({siteId, componentId, requestId, status, error});
      return exits.success({
        message: 'Feedback updated successfully',
        data: { siteId, componentId, requestId, status}
      });
    } catch(e) {
      if (e.HTTP_STATUS_CODE == 400) {
          sails.log('Error > user-config-data-param [bad-request]', e);
          return exits.badRequest({err: e.message, inputs})
      } else  {
          sails.log.error('error > user-config-data-param',e);
          return exits.serverError(e);
      }
    }
  }
}
