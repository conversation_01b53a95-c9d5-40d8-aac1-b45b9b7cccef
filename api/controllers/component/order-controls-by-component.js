const flaverr = require('flaverr');
const componentService = require('../../services/component/component.service');
const { validateOrderControlsRequest } = require('../../utils/component/inputValidation');
module.exports = {
  friendlyName: '',
  description: '',
  inputs: {
    componentId: {
      type: 'string',
    },
    controls: {
      type: 'json',
    }
  },
  exits: {
    serverError: {
      statusCode: 500,
      responseType: "serverError",
      description: "Server Error",
    },
    forbidden: {
      statusCode: 403,
      description: "Invalid User",
    },
    notFound: {
      statusCode: 404,
      description: "Not Found",
    },
    success: {
      statusCode: 200,
      description: "",
    },
    badRequest: {
      statusCode: 400,
      description: "Bad Request",
    },
  },
  fn: async function (inputs, exits) {
    try {
      const payload = validateOrderControlsRequest(inputs);
      const { componentId, controls } = payload;
      const orders = controls.map(control => control.order);
      const uniqueOrders = new Set(orders);
      if(uniqueOrders.size !== orders.length)
        throw flaverr('E_INPUT_VALIDATION', new Error('Order numbers should be unique'));
      await componentService.orderControlsByComponent({ componentId, controls });
      return exits.success({message: 'Controls order updated successfully'});
    } catch (error) {
      switch (error.code) {
        case 'E_INPUT_VALIDATION':
          return exits.badRequest({ error: error.message });
        case 'COMPONENT_ID_NOT_EXIST':
          return exits.notFound({ error: error.message });
        default:
          return exits.serverError(error);
      }
    }
  }
}
