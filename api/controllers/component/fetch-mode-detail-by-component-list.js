const componentService = require("../../services/component/component.service");
module.exports = {
  friendlyName: "Fetch mode details",
  description: "",

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
    },
    componentIds: {
      type: "json",
      required: true,
      example: ["mgch_1", "mgch_2"],
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[component > fetch-mode-details] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[component > fetch-mode-details] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[component > fetch-mode-details] forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[component > fetch-mode-details] Not Found",
    },
  },

  fn: async function ({ componentIds, _userMeta: { _site: siteId } }, exits) {
    try {
      if (!Array.isArray(componentIds))
        return exits.badRequest({ error: `Array of component Id(s) is required` });
      componentIds = _.uniq(componentIds);
      const lastKnownMode = await componentService.getLastKnownModeByComponentIdV2(
        siteId,
        componentIds
      );

      return exits.success(Object.values(lastKnownMode));
    } catch (err) {
      sails.log.error("modes > getComponentsMode", err);
      return exits.serverError(err);
    }
  },
};
