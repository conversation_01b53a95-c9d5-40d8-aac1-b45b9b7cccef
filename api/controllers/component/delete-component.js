const { validateDeleteComponentInputs } = require('../../utils/component/inputValidation');
const componentService = require('../../services/component/component.service');
const { notifyJouleTrackPublicRoom } = require('../../services/socket/socket.service');
const {
  DriverControlConfigMapping,
} = require("../../services/controlRelationshipConfig/controlsRelationshipConfig.service");
const siteService = require('../../services/site/site.service');

module.exports = {
  friendlyName: 'delete-component',
  description: 'Delete a component',
  inputs: {
    deviceId: {
      type: 'string',
      required: true,
      description: 'The unique identifier for the component to be deleted'
    },
    siteId: {
      type: 'string'
    }
  },
  exits: {
    serverError: {
      statusCode: 500,
      responseType: "serverError",
      description: "Server Error",
    },
    forbidden: {
      statusCode: 403,
      description: "Invalid User",
    },
    notFound: {
      statusCode: 404,
      description: "Component Not Found",
    },
    success: {
      statusCode: 200,
      description: "Component Deleted Successfully",
    },
    badRequest: {
      statusCode: 400,
      description: "Bad Request",
    },
  },
  fn: async function (inputs, exits) {
    try {
      const { deviceId, siteId } = inputs;

      // Validate inputs
      if (!deviceId) return exits.badRequest({ err: 'Device ID is missing' });

      const component = await componentService.fetchComponentById(inputs);
      if (!component) return exits.notFound({ err: 'Component not found' });

      const linkedConfPages = await componentService.getLinkedConfiguratorPageByComponentId(deviceId);
      if (linkedConfPages.length) {
        return exits.badRequest({
          err: `Unable to delete component because it is linked to configurator pages: ${linkedConfPages.map(it => `"${it.title}"`).join(', ')}.`,
        });
      }

      // Remove site cache and update devices
      await componentService.removeCachedComponentCategories(siteId);
      const devices = await componentService.getDevicesOnComponent(siteId, deviceId);
      const updatedDevices = await componentService.updateDevices(devices);

      // Notify about updated devices
      const updateEventData = { event: 'update', data: updatedDevices };
      await notifyJouleTrackPublicRoom(siteId, "devices", updateEventData);

      // Delete component and notify about the deletion
      await componentService.deleteComponent(deviceId, siteId);
      const deleteEventData = { event: 'delete', data: [{ deviceId }] };
      await notifyJouleTrackPublicRoom(siteId, "components", deleteEventData);

      // Handle driver-control mapping updates
      await DriverControlConfigMapping.syncControlRelationship([deviceId], component.deviceType, component.driverType);

      return exits.success({ message: `Component ${deviceId} delete successfully` });
    } catch (e) {
      sails.log.error("[component >> delete-component]", e);
      switch (e.code) {
        case 'E_COMPONENT_NOT_FOUND':
          return exits.notFound({ err: e.message });
        case 'E_INVALID_PARAMS':
          return exits.badRequest({ err: e.message });
        case 'E_LINKED_SYSTEM':
          return exits.forbidden({ err: e.message });
        default:
          sails.log.error(e);
          return exits.serverError(e);
      }
    }
  }
};
