const componentService = require('../../services/component/component.service');
module.exports = {
  friendlyName: 'getComponentConfig',
  description: 'get component config',
  inputs: {
    componentId: {
      type: 'string',
      required: true,

    },
  },
  exits: {
    serverError: {
      statusCode: 500,
      responseType: "serverError",
      description: "Server Error",
    },
    forbidden: {
      statusCode: 403,
      description: "Invalid User",
    },
    notFound: {
      statusCode: 404,
      description: "Not Found",
    },
    success: {
      statusCode: 200,
      description: "",
    },
    badRequest: {
      statusCode: 400,
      description: "Bad Request",
    },
  },
  fn: async function ({ componentId }, exits) {
    try {
      const componentConfig = await componentService.getComponentConfig(componentId);
      if (!componentConfig) {
        return exits.badRequest({ message: `componentId ${componentId} does not exist` })
      }
      const responseObject = {
        controls: componentConfig.controls,
        updatedAt: componentConfig.updatedAt,
        siteId: componentConfig.siteId,
        regionId: componentConfig.regionId,
        name: componentConfig.name,
        driverType: componentConfig.driverType,
        deviceType: componentConfig.deviceType,
        deviceId: componentConfig.deviceId,
        data: componentConfig.data,
        createdAt: componentConfig.createdAt,
        controllerId: componentConfig.controllerId,
        controlRelationshipMap: componentConfig.controlRelationshipMap,
        svgId: componentConfig.svgId,
        isInMaintenanceMode: componentConfig?.isInMaintenanceMode ?? '0'
      }
      return exits.success(responseObject)
    } catch (error) {
      switch (error.code) {
        case 'COMPONENT_ID_NOT_EXIST':
          return exits.notFound({ error: error.message });
        case 'DRIVER_DETAIL_NOT_EXIST':
          return exits.badRequest({ error: error.message });
        default:
          return exits.serverError(error);
      }
    }
  }
}
