
const componentService = require('../../services/component/component.service');
const deviceTypeService = require('../../services/devicetype/devicetype.service.js');
// const globalHelper = require('../../utils/globalhelper');
const selfUtils = require('../../utils/component/bulk-component-generation.util.js');
const SheetService = require("../../services/google/bulk-configuration.service");
const deviceService = require('../../services/device/device.service');
const routeService = require("../../services/device/bulk-component-generation.service.js");

module.exports = {
  friendlyName: 'bulkComponentGeneration',
  description : '',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    sheetId: {
      type: 'string',
      required: false,
      example: '1nJT92Hv1ee6JGDgGSEDMHEGX2OW3D7sigxA37PdHi20',
      description: 'sheetId of the Google Sheet used to fetch parameter values.',
    },
    siteId: {
      type: 'string',
      required: false,
      example: 'sjo-del',
      description: 'SiteId if mentioned explicitly, overwrites the siteId inherited from the token.',
    },
    deviceType: {
      type: 'string',
      required: false,
      example: 'fireAlarmSensor',
      description: 'deviceType if mentioned explicitly, overwrites the deviceType set as default, ie, fireAlarmSensor.',
    },
    deviceId: {
      type: 'string',
      required: true,
      example: '100573',
      description: 'deviceId of the device whose parmeters need to be generated.',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[component > bulkComponentGeneration] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[component > bulkComponentGeneration] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[component > bulkComponentGeneration] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      const {
        _userMeta: { _site }, sheetId: sheetIdInBodyParams, siteId: siteIdInBodyParams, deviceType: deviceTypeInBodyParams, deviceId
      } = inputs;
      const deviceType = deviceTypeInBodyParams ? deviceTypeInBodyParams : "fireAlarmSensor";

      const $deviceConfiguration =  deviceService.findOne({ deviceId });
      const deviceConfiguration = await $deviceConfiguration;
      if (!deviceConfiguration){
        return exits.badRequest({
          code: "E_DEVICE_NOT_FOUND",
          problems: ["Device not found"]
        });
      }
      // Initialising sheetId
      let sheetId;
      if (sheetIdInBodyParams) sheetId = sheetIdInBodyParams;
      else if(deviceConfiguration.sheetId) sheetId = deviceConfiguration.sheetId;
      else {
        return exits.badRequest({
          code: "E_SHEET_INVALID",
          problems: ["SheetId not found in device configuration."]
        });
      }

      // Reading values from the google sheet.
      let sheet = new SheetService(sheetId);
      await sheet.init();
      const $rows = sheet.getAllRows();
      const $layerInfo = sheet.fetchLayerInfoFromSheet();

      // Input checks
      const $componentDriver = deviceTypeService.findOne({ deviceType, driverType: 0 });
      const rows = await $rows;
      const layerNameToIdMap = await $layerInfo;
      let inputCheck = selfUtils.checkInput(rows, layerNameToIdMap);
      if(!inputCheck.status) return exits.badRequest({
        code: "E_INPUT_CHECK_FAILURE",
        problems: inputCheck.errors
      });
      const { assetIdGrouping, assetIdsOfComponentsToBeCreated, componentIdToComponentNameMapping } = inputCheck;

      const { controllerId } = deviceConfiguration;
      // Generating component configuration packets
      const siteId = deviceConfiguration.siteId;
      const $generateComponentId = componentService.generateComponentId(siteId);
      const componentDriver = await $componentDriver;
      const generatedComponentId = await $generateComponentId;
      const basicComponentConfiguration = selfUtils.generateComponentCreationBodyParams(assetIdGrouping, siteId, generatedComponentId, controllerId, assetIdsOfComponentsToBeCreated);
      const {
        completeComponentConfiguration:componentConfigWithDataParams,
        componentIdToDeviceParameterAbbrMap
      } = selfUtils.appendComponentDataParams(basicComponentConfiguration, componentDriver, assetIdGrouping, deviceId);

      //  Checking if components already exist for the associated device parameters.
      const componentParameterErrors = await routeService.checkComponentsAlreadyExistForParameter(componentIdToDeviceParameterAbbrMap, siteId);
      if(!componentParameterErrors.status){
        return exits.badRequest({
          code: "E_CONFLICTING_COMPONENTS_FOUND",
          problems: componentParameterErrors.errors
        })
      }

      // Updating component names of existing components
      const $updateQueries = routeService.updateComponentNames(componentIdToComponentNameMapping);

      // Saving Component Configurations and updating Parameter configurations if successful.
      let createQueries = await routeService.createComponentsAndModifyParameterConfigurations(componentConfigWithDataParams, componentIdToDeviceParameterAbbrMap, siteId);
      if(!createQueries.status)
        return exits.badRequest({
        "status": false,
        "code": "E_CREATE_COMPONENT_ERROR",
        "problems": createQueries.problems
      });

      const updateQueries = await $updateQueries;
      if(!updateQueries.status){
        return exits.badRequest({
          status:false,
          code: "E_UPDATE_COMPONENT_ERROR",
          problems: updateQueries.problems
        })
      }


      return exits. success({
        "status": true,
        componentConfigWithDataParams
      });
    } catch(error) {
      sails.log.error('[component > bulkComponentGeneration] Error!');
      sails.log.error(error);
      return exits.serverError(error);
    }
  }
};
