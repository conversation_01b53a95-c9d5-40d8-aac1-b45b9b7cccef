const componentService = require('../../services/component/component.service');
const deviceTypeService = require('../../services/devicetype/devicetype.public');
module.exports = {
  friendlyName: 'Get all component config',
  description: '',
  inputs: {
    deviceType: {
      type: 'json',
      required: true,
      example: ['mgch_1', 'mgch_2'],
    },
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    },
  },
  exits: {
    serverError: {
      statusCode: 500,
      responseType: "serverError",
      description: "Server Error",
    },
    forbidden: {
      statusCode: 403,
      description: "Invalid User",
    },
    notFound: {
      statusCode: 404,
      description: "Not Found",
    },
    success: {
      statusCode: 200,
      description: "Fetched control relationships of all components",
    },
    badRequest: {
      statusCode: 400,
      description: "Bad Request",
    },
  },
  fn: async function ({ deviceType,  _userMeta: { _site: siteId } }, exits) {
    try {
      const deviceTypesRawData = await deviceTypeService.find({
        deviceType
      })
      if (_.isEmpty(deviceTypesRawData)) {
        return exits.badRequest({ err: `DeviceType: ${deviceType} does not exist` });
      }

      const componentIds = await componentService.getDriverComponentIds(siteId, deviceType);
      if (!Array.isArray(componentIds)) return exits.badRequest({ err: `Array of component Id(s) is required` });

      const componentRelationshipPromises = await Promise.allSettled(componentIds.map((componentId) => componentService.getComponentConfig(componentId)));
      const errors = [];
      const componentRelationship = componentRelationshipPromises.reduce((acc, curr) => {
        if (curr['status'] === 'fulfilled') {
          const controlsRelationshipResponse = {
            device_id: curr['value'].deviceId,
            controlRelationshipMap: curr['value'].controlRelationshipMap.controls
          };
          acc.push(controlsRelationshipResponse);
        } else {
          errors.push(curr);
        }
        return acc;
      }, []);

      if (!errors.length) {
        sails.log(errors);
      }

      return exits.success(componentRelationship);
    }catch (err) {
      if (err) {
        sails.log.error("modes > getComponentsMode",err);
        return exits.serverError(err);
      }
    }
  }

}
