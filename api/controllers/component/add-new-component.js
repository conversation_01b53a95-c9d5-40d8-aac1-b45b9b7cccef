const { validateAddComponentInputs } = require('../../utils/component/inputValidation');
const componentService= require('../../services/component/component.service');
const { notifyJouleTrackPublicRoom } = require('../../services/socket/socket.service');
const auditEventLogService = require('../../services/auditEventLog/auditEventLog.public');
const {
  DriverControlConfigMapping,
} = require("../../services/controlRelationshipConfig/controlsRelationshipConfig.service");

module.exports = {
  friendlyName: 'add-new-component',
  description: 'add new component',
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: '<EMAIL>', _role: 'role', _site: 'siteId' },
    },
    component: {
      type: 'json',
      required: true
    },
  },
  exits: {
    serverError: {
      statusCode: 500,
      responseType: "serverError",
      description: "Server Error",
    },
    forbidden: {
      statusCode: 403,
      description: "Invalid User",
    },
    notFound: {
      statusCode: 404,
      description: "Not Found",
    },
    success: {
      statusCode: 201,
      description: "Record Updated Successfully",
    },
    badRequest: {
      statusCode: 400,
      description: "Bad Request",
    },
  },
  fn: async function(inputs, exits) {
    try {
    const { component, _userMeta:{ id:userId } } = inputs;
      if (!component) return exits.badRequest({ err: 'Component data is missing' });

      const componentDetails = validateAddComponentInputs(component);
      const { siteId, controllerId, deviceType, driverType } = componentDetails;

      componentDetails.driverType = driverType ? String(driverType) : "0";
      componentDetails.isVirtualComponent = componentDetails.hasOwnProperty("isVirtualComponent") ? componentDetails.isVirtualComponent : "0";

      const driverDetail = await componentService.validateDriverDetails(componentDetails.driverType, deviceType);
      if (!driverDetail) return exits.badRequest({ err: 'Invalid driver or device type' });

      const controllerRecord = await componentService.getControllerDeviceRecord(controllerId, siteId);
      if (!controllerRecord) return exits.badRequest({ err: `Controller ID: ${controllerId} does not exist` });

      const newComponentId = await componentService.generateComponentId(siteId);
      componentDetails.deviceId = newComponentId;

      //added bogus value for deviceParameterList, since it cannot be null in a db transaction
      componentDetails.deviceParameterList = ['']

      await componentService.create(componentDetails)

      const createdComponent = await componentService.fetchComponentById({ siteId, deviceId: newComponentId });

      const createEventData = {
        "event": "create",
        "data": [createdComponent],
      };

      await notifyJouleTrackPublicRoom(
        siteId,
        "components",
        createEventData
      );

      await componentService.linkComponentToController(controllerRecord, createdComponent.deviceId, siteId);

      const updatedControllerRecord = await componentService.getControllerDeviceRecord(controllerId, siteId);
      if (!updatedControllerRecord) return exits.badRequest({ err: `Controller ID: ${controllerId} does not exist` });

      const updateEventData = {
        "event": "create",
        "data": updatedControllerRecord,
      };

      await notifyJouleTrackPublicRoom(
        siteId,
        "devices",
        updateEventData
      );

      await DriverControlConfigMapping.syncControlRelationship(
        [newComponentId],
        createdComponent.deviceType,
        createdComponent.driverType
      );

      // Remove the extra fields
      delete createdComponent.deviceParameterList;
      delete createdComponent.controls;
      delete createdComponent.data;

      // Convert Unix timestamp to ISO format
      const createdAtISODate = new Date(createdComponent.createdAt).toISOString();
      createdComponent.createdAt = createdAtISODate;
      createdComponent.updatedAt = createdAtISODate;

      const auditPayload = {
        event_name: "state_create_component",
        user_id: userId,
        site_id: siteId,
        asset_id: newComponentId,
        req: this.req,
        prev_state: null,
        curr_state: createdComponent,
      };

      auditEventLogService.emit(auditPayload);

      return exits.success(createdComponent);
    } catch(e) {
      sails.log.error("[component >> add-component]", e);
      switch (e.code) {
        case 'E_COMPONENT_NOT_UPDATED': {
          return exits.forbidden({err: e.message, code: e.code})
        }
        case 'E_DEVICES_NOT_FOUND': {
          return exits.badRequest({err: e.message, code: e.code})
        }
        case 'E_TRANSACTION_FAILED' : {
          return exits.forbidden({err: e.message, code: e.code})
        }
        case 'E_INPUT_VALIDATION': {
          return exits.badRequest({err: e.message, code: e.code})
        }
        default:  {
          return exits.serverError(e);
        }
      }
    }
  }
}
