const { loadCPAAssetDataSource } = require("../../services/CPAAssetConfigSync/CPAAssetConfigSync.service");

module.exports = {
  friendlyName: 'sync-cpa-config-data-source',
  description:'',
  example: [`curl -X GET "http://localhost:1337/`],
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    },
    siteId: {
      type: 'string',
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[cpa > sync-cpa-config-data-source] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[cpa > sync-cpa-config-data-source] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[cpa > sync-cpa-config-data-source] forbidden Request!',
    },
  },

  fn: async function ({siteId, _userMeta}, exits) {
    try {
      if (!siteId.trim().length) return exits.badRequest({
        err: 'Please provide valid site'
      })
      const userId = _userMeta.id
      const transactionId = this.req.headers['x-transaction-id']
      await loadCPAAssetDataSource(siteId,userId, transactionId)
      return exits.success({
        message: `${siteId} CPA config has been successfully synced.`
      });
    } catch (error) {
      if (error.HTTP_STATUS_CODE == 400) {
        sails.log('[badRequest > sync-cpa-config-data] Invalid siteID: ', siteId)
        return exits.badRequest({
          message: error.message
        })
      } else {
        sails.log.error('[maintenance-mode-log > sync-cpa-config-data] Error!');
        sails.log.error(error);
        return exits.serverError(error);
      }
    }
  },
};
