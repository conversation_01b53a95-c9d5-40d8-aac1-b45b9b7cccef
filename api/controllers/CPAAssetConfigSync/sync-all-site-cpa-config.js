const { loadCPAAssetDataSource } = require("../../services/CPAAssetConfigSync/CPAAssetConfigSync.service");
const { fetchAllSites } = require("../../services/site/site.service");


module.exports = {
  friendlyName: 'build-cpa-config-data-source',
  description:'',
  example: [`curl -X GET "http://localhost:1337/`],
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[cpa > build-cpa-config-data-source] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[cpa > build-cpa-config-data-source] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[cpa > build-cpa-config-data-source] forbidden Request!',
    },
  },

  fn: async function ({siteId,  _userMeta}, exits) {
    try {
      const userId =  _userMeta.id
      const sites = await fetchAllSites();
      const transactionId = this.req.headers['x-transaction-id']
      const $buildDataSource = sites.map(({siteId}) =>  loadCPAAssetDataSource(siteId, userId, transactionId))
      const syncedSite = await Promise.allSettled($buildDataSource);
      const response = {
        success: [],
        failure: []
      }
      for(const site of syncedSite) {
         if (site.status == 'rejected') {
          response.failure.push({
            status: 'rejected',
            message: site.reason.message
          })
         } else {
          response.success.push(site.value.siteId)
         }



      }
      return exits.success(response);
    } catch (error) {
      if (error.HTTP_STATUS_CODE == 400) {
        sails.log('[badRequest > build-cpa-config-data-source] Invalid siteID: ', siteId)
        return exits.badRequest({
          message: error.message
        })
      } else {
        sails.log.error('[maintenance-mode-log > build-cpa-config-data-source] Error!');
        sails.log.error(error);
        return exits.serverError(error);
      }
    }
  },
};
