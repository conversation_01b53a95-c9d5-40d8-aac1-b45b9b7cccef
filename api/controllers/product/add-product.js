const  ProductService = require('../../services/product/product.service')
module.exports = {
  friendlyName: "addProduct",
  description: "to add new product",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
      description:
        "User meta information added by default to authenticated routes",
    },
    product_name:{
      type:"string",
      description:"",
      required:true
    },
    unit:{
      type:"string",
      description:"",
      required:true
    },
  },
  exits: {
    serverError: {
      responseType: "serverError",
      description:
        "[shift > addShift] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description:
        "[shift > addShift] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description:
        "[shift > addShift] forbidden Request!",
    },
    duplicateProductName:{
      statusCode:400,
      description: ""
    }
  },
  fn: async function creatShift(inputs, exits) {
    try {
      const { _userMeta, product_name, unit  } = inputs
      const _shift = await ProductService.create({
        siteId: _userMeta._site,
        product_name,
        unit,
        created_by:_userMeta.id
      })
      if(_shift) return exits.success(_shift);
      else return exits.duplicateProductName({problems:[`DUPLUCATE_PRODUCT_NAME`]})
    } catch (err) {
      return exits.serverError(err);
    }
  },
};
