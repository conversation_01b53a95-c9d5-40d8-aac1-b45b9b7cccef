
const plantVisualisationsService = require('../../services/plantvisualisations/plantvisualisations.service');
// const globalHelper = require('../../utils/globalhelper');
// const selfUtils = require('../../utils/plantVisualisations/save-plant-config.util.js');

module.exports = {
  friendlyName: 'savePlantConfig',
  description : 'Stores JSON based configuration containing hierarchy information as per low side requirement.',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    config: {
      type: 'json',
      required: true,
      description: 'JSON configuration containing hierarchy information.',
    },
    plantType: {
      type: 'string',
      required: true,
      description: 'Industry/plant type for storing configuration',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[plantVisualisations > savePlantConfig] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[plantVisualisations > savePlantConfig] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[plantVisualisations > savePlantConfig] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      const {
        _userMeta: { _site: siteId }, config, plantType
      } = inputs;

      const saveObject = {
        siteId,
        plantType,
        config
      };

      await plantVisualisationsService.create(saveObject);

      return exits.success([saveObject]);
    } catch(error) {
      sails.log.error('[plantVisualisations > savePlantConfig] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
