
const plantVisualisationsService = require('../../services/plantvisualisations/plantvisualisations.service');
// const globalHelper = require('../../utils/globalhelper');
// const selfUtils = require('../../utils/plantVisualisations/find-plant-config.util.js');

module.exports = {
  friendlyName: 'findPlantConfig',
  description : 'Fetches JSON based configuration containing hierarchy information as per low side requirement. If no plantType is mentioned, it fetches all the plantTypes of that siteId.',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    plantType: {
      type: 'string',
      description: 'Industry/plant type for storing configuration',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[plantVisualisations > findPlantConfig] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[plantVisualisations > findPlantConfig] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[plantVisualisations > findPlantConfig] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      const {
        _userMeta: { _site: siteId }, plantType
      } = inputs;
      const searchObject = {
        siteId,
        plantType
      };

      let plantConfig = await plantVisualisationsService.find(searchObject);
      return exits.success(plantConfig);
    } catch(error) {
      sails.log.error('[plantVisualisations > findPlantConfig] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
