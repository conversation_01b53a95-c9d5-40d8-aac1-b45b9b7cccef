module.exports = {
  friendlyName: 'adapter',
  description : 'Route to add support of the new Dynamo adapter for sails 0.12 repo running simultaneously.',
  example: [
    `curl -X POST "http://localhost:1337/database/v1`,
  ],

  inputs: {
    tableName: {
      type: 'string',
      example: 'Name of the table. All small case. Ex: "sites"',
      required: true
    },
    query: {
      type: 'json',
      example: {
        primaryKey: 'foo',
        secondaryKey: 'bar'
      },
    },
    updateObject: {
      type: 'json',
      example: {
        primaryKey: 'foo',
        secondaryKey: 'bar',
        dataField1: 'fooo'
      },
    },
    operation: {
      type: 'string',
      example: 'find',
      required: true
    }
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[database > adapter] Server Error!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      let { query, tableName, operation, updateObject } = inputs;
      let queryResponseData;
      switch(operation){
        case 'create':
          queryResponseData = await sails.models[tableName].create(query);
          break;
        case 'find':
          queryResponseData = await sails.models[tableName].find(query);
          break;
        case 'findOne':
          queryResponseData = await sails.models[tableName].findOne(query);
          break;
        case 'update':
          queryResponseData = await sails.models[tableName].update(query, updateObject);
          break;
        case 'delete':
          queryResponseData = await sails.models[tableName].destroy(query);
          break;
      }
      exits.success(queryResponseData);
    } catch(error) {
      sails.log.error('[JouleTrack v2: database > adapter] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
