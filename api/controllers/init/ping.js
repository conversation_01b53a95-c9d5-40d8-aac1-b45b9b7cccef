
const sheetService = require("../../services/google/bulk-configuration.service.js");
const globalHelper = require('../../utils/globalhelper');

module.exports = {
  friendlyName: 'Ping',
  description: 'ping for other services to check if the monolith is live',
  example: [
    `curl -X GET "http://localhost:1337/ping`,
  ],

  inputs: {

  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[systems > fetchSystemDetails] Server Error!',
    },
  },

  fn: async function (inputs, exits) {
    try {
      return exits.success({
        message: "System is up and running!",
        status: "success",
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      sails.log.error("Error in /m2/ping!", error);
      return exits.serverError(error);
    }

  }
};
