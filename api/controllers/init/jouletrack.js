
const roleService = require('../../services/role/role.public');
const deviceService = require('../../services/device/device.public');
const userSiteMapService = require('../../services/userSiteMap/userSiteMap.public');
const siteService = require('../../services/site/site.public');
const componentService = require('../../services/component/component.public');
const processService = require('../../services/process/process.public');
const parameterService = require('../../services/parameter/parameter.public');

const globalHelper = require('../../utils/globalhelper');
const util = require('../../utils/init/jouletrack.util.js');

module.exports = {
  friendlyName: 'jouletrack',
  description : '',
  example: [
    `curl -X GET "http://localhost:1337/init/v2/jouletrack/`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      description: 'Auto attached by isAuthorized policy.',
      required: true
    },

  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[init > jouletrack] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[init > addRegion] Bad Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      let { _userMeta } = inputs;
      let errors = util.checkInput(_userMeta);
      if(errors !== false) return exits.badRequest('Invalid Token keys!');
      let siteId = _userMeta._site, role = _userMeta._role, userId = _userMeta.id;

      let policy = await roleService.findOne({roleName: role});
      if(globalHelper.isNullish(policy)) return exits.badRequest('Invalid Policy for user role.');

      let $databaseQueries = Promise.all([
        getSiteData(userId, siteId),
        componentService.find({siteId}),
        getPlantData(siteId),
        getDevicesData(siteId)
      ]);

      let [siteData, componentList, plants, devices] = await $databaseQueries;

      return exits.success({
        currentSite: siteData.currentSite,
        sitesList: siteData.sitesList,
        componentList,
        plants,
        policy,
        devices
      });
      // ===  Helper functions ===
      /**
       * @description Fetches complete details of current site along with list of sites that the user has access to.
       * @param {string} userId userId that came with the request.
       * @param {string} siteId siteId that came with request token.
       */
      async function getSiteData(userId, siteId) {
        let userSiteAccessList = await userSiteMapService.find({ userId });
        let $siteList = userSiteAccessList.map(userOnSiteData => {
          return siteService.findOne({siteId: userOnSiteData.siteId});
        });
        let siteList = await Promise.all($siteList);
        return util.filterSiteData(siteList, siteId);
      }

      /**
       * @description Fetches plant details and filters and gets keys: name, processId, plantCategories
       * @param {string} siteId Request token siteId
       */
      async function getPlantData(siteId){
        let plantList = await processService.find({where: { siteId, type: 'plant' }});
        return util.filterPlantList(plantList);
      }

      /**
       * @description Fetches all devices of a site and appends the parameter object in the 'param' key
       * @param {string} siteId Request token siteId
       */
      async function getDevicesData(siteId){
        let [ deviceList, parameterList ] = await Promise.all([
          deviceService.find({ siteId }),
          parameterService.find({siteId}),
        ]);
        return util.addParameterKeysToDevices(deviceList, parameterList);
      }

    } catch(error) {
      sails.log.error('[init > jouletrack] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
