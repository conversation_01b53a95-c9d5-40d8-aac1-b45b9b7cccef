const requestValidator = require('../../utils/maintenanceCard/requestValidator');
const maintenanceTicketService = require('../../services/maintenanceCard/maintenanceTicket.service');
const FreshDesk = require('../../services/maintenanceCard/freshdesk');

module.exports = {
  friendlyName: 'Create Maintenance Ticket',

  description: 'Create maintenance ticket',

  inputs: {
    priority: {
      type: 'number',
      required: true,
    },
    description: {
      type: 'string',
      required: true,
    },
    subject: {
      type: 'string',
      required: true,
    },
    comment: {
      type: 'string',
      required: false,
    },
    email: {
      type: 'string',
      required: true,
    },
    siteId: {
      type: 'string',
      required: true,
    },
    status: {
      type: 'number',
      required: true,
    },
    ccEmails: {
      type: 'string',
      required: false,
    },
    pageUrl: {
      type: 'string',
      required: true,
    },
    maintenanceType: {
      type: 'string',
      required: true,
    },
    devices: {
      type: 'string',
      required: false,
    },
    attachments: {
      type: 'ref',
      required: false,
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[maintenanceCard > create-maintenance-ticket] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[maintenanceCard > create-maintenance-ticket] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[maintenanceCard > create-maintenance-ticket] Forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[maintenanceCard > create-maintenance-ticket] Not Found',
    },
    success: {
      statusCode: 201,
      description: '[maintenanceCard > create-maintenance-ticket] Ticket create successfully',
    },
  },

  fn: async function (inputs, exits) {
    try {
      await requestValidator.validateCardPayload(inputs);
      const attachments = await new Promise((resolve, reject) => {
        this.req.file('attachments')
          .upload(
            {
              dirname: require('path')
                .resolve(sails.config.appPath, '.tmp/uploads'),
            },
            (err, files) => {
              if (err) return reject(err);
              resolve(files);
            }
          );
      });
      const formData = await maintenanceTicketService.createTicketFormData(inputs, attachments);
      const freshDesk = new FreshDesk();
      const response = await freshDesk.createTicket(formData);
      return exits.success({ cardId: response.data.id });
    } catch (err) {
      sails.log.error('maintenanceCard > create-maintenance-ticket');
      sails.log.error(err);
      switch (err.code || err.response?.status) {
        case 'E_FRESHDESK_API_FAILURE':
          return exits.badRequest({ err: err.message });
        case 403:
          return exits.badRequest({ err: err?.response?.data?.message });
        case 'E_INPUT_VALIDATION':
          return exits.badRequest({ err: err.message });
        default:
          return exits.serverError(err);
      }
    }
  },
};
