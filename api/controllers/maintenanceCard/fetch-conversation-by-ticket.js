const FreshDesk = require('../../services/maintenanceCard/freshdesk.js');
const responseBuilder = require("../../utils/maintenanceCard/responseBuilder");

module.exports = {
  friendlyName: "Fetch Conversation by ticket",

  description: "Fetch Conversation by ticket",

  inputs: {
    id: {
      type: "string",
      required: true,
    }
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[assetMaintenance > fetch-conversation-by-ticket] Server Error!",
    },
    badRequest: {
      statusCode: 400,
      responseType: "badRequest",
      description: "[assetMaintenance > fetch-conversation-by-ticket] Bad Request!",
    },
    forbidden: {
      statusCode: 403,
      responseType: "forbidden",
      description: "[assetMaintenance > fetch-conversation-by-ticket] Forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[assetMaintenance > fetch-conversation-by-ticket] Not Found",
    },
    success: {
      statusCode: 200,
      description:
        "[assetMaintenance > fetch-conversation-by-ticket] Maintenance ticket conversation fetched successfully",
    },
  },

  fn: async function ({ id }, exits) {
    try {
      const ticketId = Number(id);
      if (!Number.isInteger(ticketId) || ticketId <= 0) {
        return exits.badRequest({ err: "The provided ticketId is not a valid number." });
      }
      const freshDesk = new FreshDesk();
      const response = await freshDesk.fetchConversationByTicketId(ticketId);
      const formattedConvo = response ? responseBuilder.getFormattedConversation(response.data) : [];
      return exits.success(formattedConvo);
    } catch (err) {
      sails.log.error("assetMaintenance > fetch-conversation-by-ticket");
      sails.log.error(err);
      switch (err.code || err.response?.status) {
        case "E_FRESHDESK_API_FAILURE":
          return exits.notFound({ err: err.message });
        case "E_INPUT_VALIDATION":
        case 403:
          return exits.badRequest({ err: err.message });
        default:
          return exits.serverError(err);
      }
    }
  },
};
