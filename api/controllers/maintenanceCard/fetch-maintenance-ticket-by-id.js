const FreshDesk = require("../../services/maintenanceCard/freshdesk");

module.exports = {
  friendlyName: "Fetch Maintenance Ticket",

  description: "Fetch maintenance ticket",

  inputs: {
    id: {
      type: "string",
      required: true,
    }
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[maintenanceCard > fetch-maintenance-ticket] Server Error!",
    },
    badRequest: {
      statusCode: 400,
      responseType: "badRequest",
      description: "[maintenanceCard > fetch-maintenance-ticket] Bad Request!",
    },
    forbidden: {
      statusCode: 403,
      responseType: "forbidden",
      description: "[maintenanceCard > fetch-maintenance-ticket] Forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[maintenanceCard > fetch-maintenance-ticket] Not Found",
    },
    success: {
      statusCode: 200,
      description:
        "[maintenanceCard > fetch-maintenance-ticket] Maintenance ticket fetched successfully",
    },
  },

  fn: async function ({ id }, exits) {
    try {
      const ticketId = Number(id);
      if (!Number.isInteger(ticketId) || ticketId <= 0) {
        return exits.badRequest({ err: "The provided ticketId is not a valid number." });
      }
      const freshDesk = new FreshDesk();
      const response = await freshDesk.getTicketById(ticketId);
      return exits.success(response ? response.data : null);
    } catch (err) {
      sails.log.error("maintenanceCard > fetch-maintenance-ticket");
      sails.log.error(err);
      switch (err.code || err.response?.status) {
        case "E_FRESHDESK_API_FAILURE":
          return exits.notFound({ err: err.message });
        case "E_INPUT_VALIDATION":
        case 403:
          return exits.badRequest({ err: err.message });
        default:
          return exits.serverError(err);
      }
    }
  },
};
