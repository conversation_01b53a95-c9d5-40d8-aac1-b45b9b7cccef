const FreshDesk = require("../../services/maintenanceCard/freshdesk");

module.exports = {
  friendlyName: "Add Comment to Conversation",

  description: "Add Comment to Conversation",

  inputs: {
    id: {
      type: "string",
      required: true,
    },
    comment: {
      type: "string",
      required: true,
    },
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[maintenanceCard > add-comment-to-conversation] Server Error!",
    },
    badRequest: {
      statusCode: 400,
      responseType: "badRequest",
      description: "[maintenanceCard > add-comment-to-conversation] Bad Request!",
    },
    forbidden: {
      statusCode: 403,
      responseType: "forbidden",
      description: "[maintenanceCard > add-comment-to-conversation] Forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[maintenanceCard > add-comment-to-conversation] Not Found",
    },
    success: {
      statusCode: 200,
      description:
        "[maintenanceCard > add-comment-to-conversation] Maintenance ticket fetched successfully",
    },
  },

  fn: async function ({ id, comment, _userMeta: { id: userId } }, exits) {
    try {
      const ticketId = Number(id);
      if (!Number.isInteger(ticketId) || ticketId <= 0) {
        return exits.badRequest({ err: "The provided ticketId is not a valid number." });
      }
      const freshDesk = new FreshDesk();
      await freshDesk.commentToConversation(ticketId, comment, userId);
      return exits.success({
        message: `Comment added successfully to ticket(${ticketId}) by user(${userId})`,
      });
    } catch (err) {
      sails.log.error("maintenanceCard > add-comment-to-conversation");
      sails.log.error(err);
      switch (err.code || err.response?.status) {
        case "E_FRESHDESK_API_FAILURE":
          return exits.notFound({ err: err.message });
        case "E_INPUT_VALIDATION":
        case 403:
          return exits.badRequest({ err: err.message });
        default:
          return exits.serverError(err);
      }
    }
  },
};
