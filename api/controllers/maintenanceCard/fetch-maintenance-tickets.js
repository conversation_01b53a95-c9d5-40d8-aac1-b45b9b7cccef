const moment = require("moment");

const authService = require('../../services/auth/auth.service');
const requestValidator = require("../../utils/maintenanceCard/requestValidator");
const responseBuilder = require("../../utils/maintenanceCard/responseBuilder");
const FreshDesk = require("../../services/maintenanceCard/freshdesk");

module.exports = {
  friendlyName: "Fetch Maintenance Tickets",

  description: "Fetch maintenance tickets",

  inputs: {
    startDate: {
      type: "string",
      required: true,
    },
    endDate: {
      type: "string",
      required: true,
    },
    siteId: {
      type: "string",
      required: true,
    },
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[assetMaintenance > fetch-maintenance-tickets] Server Error!",
    },
    badRequest: {
      statusCode: 400,
      responseType: "badRequest",
      description: "[assetMaintenance > fetch-maintenance-tickets] Bad Request!",
    },
    forbidden: {
      statusCode: 403,
      responseType: "forbidden",
      description: "[assetMaintenance > fetch-maintenance-tickets] Forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[assetMaintenance > fetch-maintenance-tickets] Not Found",
    },
    success: {
      statusCode: 200,
      description:
        "[assetMaintenance > fetch-maintenance-tickets] Maintenance tickets fetched successfully",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { _userMeta: { id: userId } } = inputs;
      if (!await authService.userHasSiteAccess(userId, inputs.siteId)) {
        return exits.badRequest({
          error: "The user does not have access to this site.",
        });
      }
      requestValidator.validateFetchMaintenanceTickets(inputs);
      const startDate = moment(inputs.startDate).subtract(1, "days").format("YYYY-MM-DD");
      const endDate = moment(inputs.endDate).add(1, "days").format("YYYY-MM-DD");
      const filter = `custom_string:'${inputs.siteId}' AND created_at:>'${startDate}' AND created_at:<'${endDate}'`;
      const freshDesk = new FreshDesk();
      const response = await freshDesk.fetchTickets(`"${filter}"`);
      const maintenanceList = await responseBuilder.formatMaintenanceData(response.data.results, inputs.siteId);
      return exits.success(maintenanceList);
    } catch (err) {
      sails.log.error("assetMaintenance > fetch-maintenance-tickets");
      sails.log.error(err);
      switch (err.code || err.response?.status) {
        case "E_FRESHDESK_API_FAILURE":
          return exits.forbidden({ err: err.message });
        case 403:
          return exits.badRequest({ err: err?.response?.data?.message });
        case "E_INPUT_VALIDATION":
          return exits.badRequest({ err: err.message });
        default:
          return exits.serverError(err);
      }
    }
  },
};
