const requestValidator = require('../../utils/maintenanceCard/requestValidator');
const maintenanceTicketService = require('../../services/maintenanceCard/maintenanceTicket.service');
const FreshDesk = require('../../services/maintenanceCard/freshdesk');

module.exports = {
  friendlyName: 'Update Maintenance Ticket',

  description: 'Update maintenance ticket',

  inputs: {
    id: {
      type: 'string',
      required: true,
    },
    priority: {
      type: 'number',
      required: true,
    },
    description: {
      type: 'string',
      required: true,
    },
    subject: {
      type: 'string',
      required: true,
    },
    comment: {
      type: 'string',
      required: false,
    },
    email: {
      type: 'string',
      required: true,
    },
    siteId: {
      type: 'string',
      required: true,
    },
    status: {
      type: 'number',
      required: true,
    },
    ccEmails: {
      type: 'string',
      required: false,
    },
    pageUrl: {
      type: 'string',
      required: true,
    },
    maintenanceType: {
      type: 'string',
      required: true,
    },
    devices: {
      type: 'string',
      required: false,
    },
    attachments: {
      type: 'ref',
      required: false,
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[maintenanceCard > update-maintenance-ticket] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[maintenanceCard > update-maintenance-ticket] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[maintenanceCard > update-maintenance-ticket] Forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[maintenanceCard > update-maintenance-ticket] Not Found',
    },
    success: {
      statusCode: 200,
      description: '[maintenanceCard > update-maintenance-ticket] Ticket create successfully',
    },
  },

  fn: async function (inputs, exits) {
    try {
      const ticketId = +inputs.id;
      if (!_.isFinite(ticketId)) {
        return exits.badRequest({ err: 'The provided ticketId is not a valid number.' });
      }
      await requestValidator.validateCardPayload(inputs);
      const attachment = await new Promise((resolve, reject) => {
        this.req.file('attachments')
          .upload(
            {
              dirname: require('path')
                .resolve(sails.config.appPath, '.tmp/uploads'),
            },
            (err, files) => {
              if (err) return reject(err);
              resolve(files[0]);
            }
          );
      });
      const formData = await maintenanceTicketService.createTicketFormData(inputs, attachment);
      const freshDesk = new FreshDesk();
      const response = await freshDesk.updateTicket(ticketId, formData);
      return exits.success({ message: `Ticket ${ticketId} successfully updated` });
    } catch (err) {
      sails.log.error('maintenanceCard > update-maintenance-ticket');
      sails.log.error(err);
      switch (err.code || err.response?.status) {
        case 'E_FRESHDESK_API_FAILURE':
          return exits.badRequest({ err: err.message });
        case 'E_INPUT_VALIDATION':
        case 403:
          return exits.badRequest({ err: err.message });
        default:
          return exits.serverError(err);
      }
    }
  },
};
