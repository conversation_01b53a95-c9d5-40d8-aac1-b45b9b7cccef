const FreshDesk = require('../../services/maintenanceCard/freshdesk.js');

module.exports = {
  friendlyName: "Delete Maintenance Ticket",

  description: "Delete maintenance ticket",

  inputs: {
    id: {
      type: "string",
      required: true,
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[assetMaintenance > delete-maintenance-ticket] Server Error!",
    },
    badRequest: {
      statusCode: 400,
      responseType: "badRequest",
      description: "[assetMaintenance > delete-maintenance-ticket] Bad Request!",
    },
    forbidden: {
      statusCode: 403,
      responseType: "forbidden",
      description: "[assetMaintenance > delete-maintenance-ticket] Forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[assetMaintenance > delete-maintenance-ticket] Not Found",
    },
    success: {
      statusCode: 200,
      description:
        "[assetMaintenance > fetch-maintenance-ticket] Maintenance ticket fetched successfully",
    },
  },

  fn: async function ({ id }, exits) {
    try {
      const ticketId = Number(id);
      if (!Number.isInteger(ticketId) || ticketId <= 0) {
        return exits.badRequest({ err: "The provided ticketId is not a valid number." });
      }
      const freshDesk = new FreshDesk();
      await freshDesk.deleteTicket(ticketId);
      return exits.success({ message: `Maintenance ticket(${ticketId}) deleted successfully` });
    } catch (err) {
      sails.log.error("assetMaintenance > delete-maintenance-ticket");
      sails.log.error(err);
      switch (err.code || err.response?.status) {
        case "E_FRESHDESK_API_FAILURE":
          return exits.notFound({ err: err.message });
        case "E_INPUT_VALIDATION":
        case 403:
          return exits.badRequest({ err: err.message });
        default:
          return exits.serverError(err);
      }
    }
  },
};
