const moment = require("moment");
const service = require("../../services/ibms/ibm.service");
const globalHelper = require('../../utils/globalhelper')
const utils = require('../../utils/datadevice/utils');
module.exports = {
  description: 'Joule-cool line graph',
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: {
        id: 'userId',
        _role: "role",
        _site: "siteId",
      },
      description: 'User meta information added by default to authenticated routes',
    },
    startTime: {
        type: 'string',
        example: 'YYYY-MM-DD HH:mm',
        description: 'start Time to get data from',
        custom: globalHelper.isValidDateTime,
        required: true
    },
    endTime: {
        type: 'string',
        example: 'YYYY-MM-DD HH:mm',
        description: 'end Time to get data to',
        custom: globalHelper.isValidDateTime,
        required: true
    },
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[Jcool > line graph] Server error'
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[Jcool > line graph] Bad request'
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[Jcool > line graph] Forbidden'
    }
  },
  fn: async function({
   _userMeta,
    startTime,
    endTime,
  }, exits) {
    try {
      const {_site:siteId} = _userMeta;
      const offset = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'Z'})
      const requestObj =  {
        startTime: moment(startTime).utcOffset(offset).format("YYYY-MM-DDTHH:mm:00Z"),
        endTime: moment(endTime).utcOffset(offset).format("YYYY-MM-DDTHH:mm:00Z"),
      }
      const deviceList = [4573, 4576, 4577, 4574, 4575]
      const getJCoolLineGraph = await service.getJCoolLineGraph(deviceList, requestObj);
      const response = getJCoolLineGraph.reduce((acc,jCoolData) => {
        const value = Number(jCoolData.kWh_TRh);
        if (value) {
          acc.push({
            siteId: siteId,
            timestamp: moment(jCoolData._time).format('YYYY-MM-DD'),
            kWh_TRh:value
           })
        }
        return acc;
      }, []);
      return exits.success(response);
    } catch (e) {
      sails.log.error('[Jcool > line graph] Error: ' + e.message)
      sails.log.error(e)
      return exits.serverError(e);
    }
  }
}
