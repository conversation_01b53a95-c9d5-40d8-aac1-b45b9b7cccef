const service = require("../../services/ibms/ibm.service");
module.exports = {
  friendlyName: "fetchFireAlarmSystemSummaryTable",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
      description:
        "User meta information added by default to authenticated routes",
    },
    FAS_system_id: {
      required: true,
      type: "number",
    },
    building_id: {
      required: true,
      type: "number",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[systems > fetchSystemDetails] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[systems > fetchSystemDetails] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[systems > fetchSystemDetails] forbidden Request!",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const {
        _userMeta: { _site: siteId },
        building_id,
        FAS_system_id,
      } = inputs;
      let floorList = await service.getFASFloorList(
        siteId,
        FAS_system_id,
        building_id
      );
      if (_.isEmpty(floorList)) {
        return exits.success([]);
      }
      let floorDeviceCountData = await service.getFloorWiseDeviceCount(
        floorList.map((it) => it.id)
      );
      let floorDeviceStateCountData = await service.getFireDevicesStatusCount(
        floorList.map((it) => it.id),
        siteId
      );

      const floorMap = floorList.reduce((acm, curr) => {
        acm[curr.id] = curr;
        acm[curr.id].normal_mode_device_count = floorDeviceStateCountData.hasOwnProperty(curr.id) && floorDeviceStateCountData[curr.id].NORMAl_MODE || 0;
        acm[curr.id].maintance_mode_device_count = floorDeviceStateCountData.hasOwnProperty(curr.id) && floorDeviceStateCountData[curr.id].MAINTENANCE_MODE || 0;
        acm[curr.id].fire_detected_device_count = floorDeviceStateCountData.hasOwnProperty(curr.id) && floorDeviceStateCountData[curr.id].FIRE_DETECTED_MODE || 0;
        acm[curr.id].total_device_count = floorDeviceCountData.hasOwnProperty(
          curr.id
        )
          ? floorDeviceCountData[curr.id]
          : 0;
        return acm;
      }, {});
      return exits.success(Object.values(floorMap));
    } catch (error) {
      sails.log.error("[systems > fetchSystemDetails] Error!");
      sails.log.error(error);
      return exits.serverError(error);
    }
  },
};
