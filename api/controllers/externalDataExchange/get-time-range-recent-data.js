const moment = require("moment-timezone");
const datadeviceService = require("../../services/datadevice/datadevice.public.js");
const selfUtils = require("../../utils/externalDataExchange/get-time-range-recent-data.util.js");

module.exports = {
  friendlyName: 'Get time range recent data for a device/component',
  description: 'Query recent data for a device/component between a given time range',

  inputs: {
    deviceId: {
      type: 'string',
    },
    siteId: {
      type: 'string'
    },
    from: {
      type: 'string',
      example: 'YYYY-MM-DDTHH:mm:ss',
      description: 'Start Time to get data from',
    },
    to: {
      type: 'string',
      example: 'YYYY-MM-DDTHH:mm:ss',
      description: 'End Time to get data to',
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[externalDataExchange > get-time-range-recent-data] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[externalDataExchange > get-time-range-recent-data] Bad Request!',
      statusCode: 400,
    },
    notFound: {
      statusCode: 404,
      description: '[externalDataExchange > get-time-range-recent-data] Not Found!',
    },
    noContent: {
      statusCode: 204,
      description: '[externalDataExchange > get-time-range-recent-data] No Content!',
    },
    unprocessableEntity: {
      statusCode: 422,
      responseType: 'badRequest',
      description: '[externalDataExchange > get-time-range-recent-data] Unprocessable Entity: Invalid siteId',
    },
    success: {
      statusCode: 200,
      description: '[externalDataExchange > get-time-range-recent-data] Successfully retrieved asset last data point',
    },
  },

  async fn(inputs, exits) {
    try {
      const isValidRequest = selfUtils.requestValidator(inputs);
      const { deviceId, siteId, from, to } = inputs;
      if(siteId != 'smyras-yog') {
        return exits.unprocessableEntity({ error: "Invalid siteId. Only 'smyras-yog' is allowed." });
      }
      if (!isValidRequest) return exits.badRequest({ error: "Invalid request" });
      const timezone = await sails.helpers.getSiteTimezone.with({ siteId, timezoneFormat: 'tz' });
      const now = moment().tz(timezone);
      const startTimestamp = from ? moment(from).tz(timezone).format('YYYY-MM-DDTHH:mm:ssZ') : now.clone().subtract(10, 'minutes').format('YYYY-MM-DDTHH:mm:ssZ');
      const endTimestamp = to ? moment(to).tz(timezone).format('YYYY-MM-DDTHH:mm:ssZ') : now.format('YYYY-MM-DDTHH:mm:ssZ');
      const recentData = await datadeviceService.getAssetRecentData({ siteId, deviceId, startTimestamp, endTimestamp });
      return exits.success(recentData);
    } catch (error) {
      sails.log.error('[externalDataExchange > get-time-range-recent-data] Error!');
      sails.log.error(error);
      return exits.serverError('Server has encountered an issue. Please contact admin to resolve this issue.');
    }
  },
};
