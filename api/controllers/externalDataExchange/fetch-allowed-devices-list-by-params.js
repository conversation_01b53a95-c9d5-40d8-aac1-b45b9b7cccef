const DataExchangeService = require("../../services/externalDataExchange/dataExchange.service");

module.exports = {
  friendlyName: "fetch-devices-list-by-params",
  description: "fetch devices list by params",
  inputs: {
    siteId: {
      type: "string",
      example: "mgch",
      required: true,
      isIn: ["aeh-mad"],
    },
    paramName: {
      type: "string",
      example: "kwh",
      required: true,
      isIn: ["kwh", "chleff", "pertotcap"],
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[ExternalDataExchange > DeviceList] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[ExternalDataExchange > DeviceList] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[ExternalDataExchange > DeviceList] Forbidden request",
    },
    notFound: {
      statusCode: 404,
      description: "[ExternalDataExchange > DeviceList] Not Found!",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { siteId, paramName } = inputs;
      const parameterDeviceDriverMap = {
        kwh: {
          deviceType: "em",
          deviceClass: "device",
          unit: "",
        },
        chleff: {
          deviceType: "chiller",
          deviceClass: "component",
          unit: "",
        },
        pertotcap: {
          deviceType: "chiller",
          deviceClass: "component",
          unit: "",
        },
      };
      if (!parameterDeviceDriverMap.hasOwnProperty(paramName)) {
        return badRequest(`parameterDeviceDriverMap does not contain the key :${paramName}`);
      }

      const { deviceType, deviceClass } = parameterDeviceDriverMap[paramName];
      const assetsList = await DataExchangeService.fetchAssetsList({
        siteId,
        deviceType,
        param: paramName,
        deviceClass,
      });
      return exits.success(assetsList);
    } catch (error) {
      sails.log.error("[ExternalDataExchange > DeviceList] Error!", error);
      return exits.serverError(error);
    }
  },
};
