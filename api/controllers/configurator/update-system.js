const systemService = require("../../services/ConfiguratorSystem/configuratorSystem.service");
const { validateUpdateSystem } = require("../../utils/configuratorSystem/requestValidator.util");
module.exports = {
  friendlyName: "updateSystem",
  description: "Updating a new system",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: {
        id: "userId",
        _role: "role",
        _site: "siteId",
      },
      description: "User meta information added by default to authenticated routes",
    },
    siteId: {
      type: "string",
    },
    systemId: {
      type: "number",
    },
    systemName: {
      type: "string",
      example: "laundry page",
      description: "Name of the system",
    },
    systemCategoryId: {
      type: "number",
    },
    description: {
      type: "string",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[systems > updateSystem] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[systems > updateSystem] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[systems > updateSystem] forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[systems > updateSystem] Not Found",
    },
    invalidSiteId: {
      statusCode: 422,
      description: "[systems > updateSystem] siteId does not exist",
    },
    invalidSystemCategoryId: {
      statusCode: 422,
      description: "[systems > updateSystem] system category does not exist",
    },
    success: {
      statusCode: 200,
      description: "[systems > updateSystem] success",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const {
        systemName,
        systemId,
        systemCategoryId,
        description,
        siteId,
        _userMeta: { id: userId },
      } = inputs;
      validateUpdateSystem(inputs);

      this.req.file("icon").upload(
        {
          maxBytes: sails.config.custom.ICON_IMAGE_LIMIT,
        },
        async function whenDone(err, uploadedSVG) {
          if (err && err.code === "E_EXCEEDS_UPLOAD_LIMIT") {
            return exits.badRequest({
              err: `Icon file size limit exceeded. Max limit ${sails.config.custom.ICON_IMAGE_LIMIT / 1024}Kb`,
            });
          }

          if (err) {
            return exits.serverError(err);
          }

          if (uploadedSVG.length > 1) {
            return exits.badRequest({
              err: `Maximum number of file upload limit exceeded`,
            });
          }
          const updateSystemObj = {
            systemName,
            systemId,
            systemCategoryId,
            description,
            siteId,
            userId,
          };
          if (!_.isEmpty(uploadedSVG)) {
            updateSystemObj.iconFile = uploadedSVG[0];
          }
          try {
            const isUpdated = await systemService.updateSystem(updateSystemObj);
            if (!isUpdated) {
              return exits.forbidden({
                err: "System could not updated",
              });
            }
            return exits.success({
              msg: "System updated successfully",
            });
          } catch (error) {
            switch (error.code) {
              case "E_SITE_NOT_FOUND": {
                return exits.notFound({ err: error.message });
              }
              case "E_SYSTEM_CATEGORY_NOT_FOUND": {
                return exits.notFound({ err: error.message });
              }
              case "E_SYSTEM_NOT_FOUND": {
                return exits.notFound({ message: error.message });
              }
              default: {
                sails.log.error("[systems > updateSystem] Error!");
                sails.log.error(error);
                exits.serverError(error);
              }
            }
          }
        },
      );
    } catch (e) {
      if (e.code === "INPUT_VALIDATION_ERROR") {
        return exits.badRequest({ message: e.message });
      } else {
        sails.log.info("[configurator > updateConfigurator] validation unknown error", e);
        return exits.serverError(e);
      }
    }
  },
};
