const systemService = require('../../services/ConfiguratorSystem/configuratorSystem.service');
const siteService = require('../../services/site/site.service');
module.exports = {
  friendlyName: "fetch dynamic navigation",
  description: "fetch dynamic navigation",
  example: [],
  inputs: {
   siteId: {
    type: 'string',
   }
  },
  exits: {
    serverError: {
      statusCode: 500,
      responseType: "serverError",
      description: "Server Error",
    },
    forbidden: {
      statusCode: 403,
      description: "Invalid User",
    },
    notFound: {
      statusCode: 404,
      description: "Not Found",
    },
    success: {
      statusCode: 200,
      description: "Successfully",
    },
    badRequest: {
      statusCode: 400,
      description: "Bad Request",
    },
  },
  fn: async function (inputs, exits) {
    try {
      const {siteId} = inputs;
      if (!siteId || _.isEmpty(siteId.trim())) {
        return exits.badRequest({
          err: 'Site id is not valid'
        })
      }
      const systems = await systemService.fetchDynamicNavigation(siteId)
      return exits.success(systems);
    } catch (err) {
      switch(err.code) {
        case 'E_SITE_NOT_FOUND': {
          return exits.notFound({
            err: err.message
          })
        }

        default: {
          sails.log("configurator > fetch-dynamic-system-navigation");
          sails.log.error(err);
          return exits.serverError(err);
        }
      }
    }
  },
};
