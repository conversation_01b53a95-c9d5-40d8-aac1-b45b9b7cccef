const systemService = require('../../services/ConfiguratorSystem/configuratorSystem.service');
const {validateFetchSystem} = require('../../utils/configuratorSystem/requestValidator.util')
module.exports = {
  friendlyName: "fetchSystem",
  description: "fetch system",
  example: [],
  inputs: {
    siteId: {
      type: 'string'
    },
    systemId: {
      type: 'string'
    },
    pageId: {
      type: 'string'
    }
  },
  exits: {
    serverError: {
      statusCode: 500,
      responseType: "serverError",
      description: "[configurator > fetchSystem] Server Error",
    },
    forbidden: {
      statusCode: 403,
      description: "[configurator > fetchSystem] Invalid User",
    },
    notFound: {
      statusCode: 404,
      description: "[configurator > fetchSystem] Not Found",
    },
    success: {
      statusCode: 200,
      description: "[configurator > fetchSystem] Successfully",
    },
    badRequest: {
      statusCode: 400,
      description: "[configurator > fetchSystem] Bad Request",
    },
  },
  fn: async function (inputs, exits) {
    try {
      const {siteId, systemId, pageId} = inputs;
      validateFetchSystem({siteId, systemId, pageId});
      const systemDetail = await systemService.fetchSystem(siteId, systemId, pageId)
      return exits.success(systemDetail);
    } catch (err) {
      switch(err.code) {
        case 'INPUT_VALIDATION_ERROR': {
          return exits.badRequest({
            err: err.message
          })
        }

        case 'E_INVALID_PAGE_TYPE': {
          return exits.badRequest({
            err: err.message
          })
        }

        case 'E_SYSTEM_NOT_FOUND': {
          return exits.notFound({
            err: err.message
          })
        }
        default: {
          sails.log("configurator > fetch-system");
          sails.log.error(err);
          return exits.serverError(err);

        }
      }

    }
  },
};
