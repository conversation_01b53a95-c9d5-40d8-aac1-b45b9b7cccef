
const systemService = require('../../services/ConfiguratorSystem/configuratorSystem.service');
const { validateDeleteSystemRequest } = require('../../utils/configuratorSystem/requestValidator.util');

module.exports = {
  friendlyName: 'deleteSystem',
  description : 'delete system',
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    siteId: {
      type: 'string',
      example: "acc-che",
      description: 'Name of the site',
    },
    systemId: {
      type: 'string',
      description: 'System id',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[configurator > deleteSystem] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[configurator > deleteSystem] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[configurator > deleteSystem] forbidden Request!',
    },
    success: {
      statusCode: 204,
      description: '[configurator > deleteSystem] Success'
    },
    notFound: {
      statusCode: 404,
      description: '[configurator > deleteSystem] Not found'
    },
    invalidSystemState: {
      statusCode: 422,
      description: '[systems > createSystem] system is not in edit mode'
    }
  },

  fn: async function (inputs, exits) {
    try {
      const {
        siteId,
        systemId,
      } = inputs;

      validateDeleteSystemRequest(inputs)


      await systemService.deleteSystem(siteId, systemId);
      return exits.success({
        message: 'System deleted Successfully',
      });
    } catch(err) {
      switch(err.code) {
        case 'E_SITE_NOT_FOUND': {
          return exits.notFound({
            err: err.message
          })
        }
        case 'E_SYSTEM_NOT_FOUND': {
          return exits.notFound({
            err: err.message
          })
        }
        case 'E_SYSTEM_NOT_DELETED': {
          return exits.notFound({
            err: err.message
          })
        }
        case 'E_SYSTEM_NOT_IN_EDITABLE_MODE': {
          return exits.invalidSystemState({
            err: err.message
          })
        }
        case 'INPUT_VALIDATION_ERROR': {
          return exits.badRequest({
            err: err.message
          })
        }

        default: {
          sails.log("configurator > unpublished system");
          sails.log.error(err);
          return exits.serverError(err);
        }
      }
    }

  }
};
