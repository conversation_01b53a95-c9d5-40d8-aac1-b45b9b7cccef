const systemCategoryService = require("../../services/configuratorSystemCategory/configuratorSystemCategory.service");
const globalhelper = require("../../utils/globalhelper");
module.exports = {
  friendlyName: "createSystemCategory",
  description: "Used to initialize a systemId and a system in the systems table",
  example: [
    `curl --location --request POST 'localhost:1337/m2/configurator/v1/system/category' \
    --header 'Content-Type: application/x-www-form-urlencoded' \
    --data-urlencode 'name=fire system'`,
  ],

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: {
        id: "userId",
        _role: "role",
        _site: "siteId",
      },
      description: "User meta information added by default to authenticated routes",
    },
    name: {
      type: "string",
      example: "Fire system",
      description: "Name of the system",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[systems > createSystem] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[systems > createSystem] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[systems > createSystem] forbidden Request!",
    },
    success: {
      statusCode: 201,
      description: "[systems > createdSystem] Success",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const {
        _userMeta: { id: userId },
      } = inputs;
      let name = inputs.hasOwnProperty("name") && inputs.name.trim();
      if (_.isEmpty(name)) {
        return exits.badRequest({ err: "System Category name is required" });
      }
      if (_.isEmpty(name) || globalhelper.isAllowedString(name)) {
        return exits.badRequest({
          err: "System Category name can have only A-Z,a-z,0-9,hyphen(-) and underscores(_)",
        });
      }
      const record = await systemCategoryService.registerNewSystemCategory(name, userId);
      return exits.success({
        id: record.id,
        name: record.name,
      });
    } catch (error) {
      sails.log.error("[systems > createSystem] Error!");
      sails.log.error(error);
      exits.serverError(error);
    }
  },
};
