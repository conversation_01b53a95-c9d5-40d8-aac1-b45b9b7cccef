const systemCategoryService = require("../../services/configuratorSystemCategory/configuratorSystemCategory.service");
const {
  validateDeleteSystemConfigurator,
} = require("../../utils/configuratorSystem/requestValidator.util");
module.exports = {
  friendlyName: "deleteSystemCategory",
  description: "delete system category",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: {
        id: "userId",
        _role: "role",
        _site: "siteId",
      },
      description: "User meta information added by default to authenticated routes",
    },
    systemId: {
      type: "string",
      description: "System id",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[configurator > deleteSystemCategory] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[configurator > deleteSystemCategory] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[configurator > deleteSystemCategory] forbidden Request!",
    },
    success: {
      statusCode: 200,
      description: "[configurator > deleteSystemCategory] Success",
    },
    notFound: {
      statusCode: 404,
      description: "[configurator > deleteSystemCategory] Not found",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const {
        systemId,
        _userMeta: { id: userId },
      } = inputs;

      validateDeleteSystemConfigurator(systemId);

      await systemCategoryService.deleteSystemCategory(systemId, userId);
      return exits.success({
        message: "System deleted Successfully",
      });
    } catch (err) {
      switch (err.code) {
        case "E_SYSTEM_CATEGORY_NOT_FOUND": {
          return exits.notFound({
            err: err.message,
          });
        }
        case "E_SYSTEM_CATEGORY_DELETION_CONDITION": {
          return exits.notFound({
            err: err.message,
          });
        }

        case "INPUT_VALIDATION_ERROR": {
          return exits.badRequest({
            err: err.message,
          });
        }

        default: {
          sails.log("configurator > delete system category");
          sails.log.error(err);
          return exits.serverError(err);
        }
      }
    }
  },
};
