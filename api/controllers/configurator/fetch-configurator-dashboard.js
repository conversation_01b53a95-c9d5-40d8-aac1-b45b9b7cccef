const configuratorService = require('../../services/ConfiguratorSystem/configuratorSystem.service');
const { dashboardResponseGenerator } = require('../../utils/configuratorPage/responseBuilder');

module.exports = {
  friendlyName: 'Fetch configurator dashboard data',
  description: '',
  inputs: {
    siteId: {
      type: 'string',
      required: true,
    }
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[configurator > fetch-configurator-dashboard-data] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[configurator > fetch-configurator-dashboard-data] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[configurator > fetch-configurator-dashboard-data] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[configurator > fetch-configurator-dashboard-data] Not Found'
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { siteId } = inputs;
      const results = await configuratorService.fetchDashboardBySiteId(siteId);
      const response = await dashboardResponseGenerator(results);
      return exits.success(response);
    } catch (err) {
      if (err.code == 'E_SITE_NOT_FOUND') {
        return exits.notFound({
          error: err.message
        })
      }
      sails.log.error(err);
      return exits.serverError(err);
    }
  }
};
