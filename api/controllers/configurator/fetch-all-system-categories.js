const systemCategoryService = require('../../services/configuratorSystemCategory/configuratorSystemCategory.service');
module.exports = {
  friendlyName: 'fetchAllSystemCategory',
  description: 'fetch all system categories',
  example: [],
  inputs: {},
  exits: {
    serverError: {
      statusCode: 500,
      responseType: "serverError",
      description: '[systemCategory >fetchAllSystemCategory] Server error ',
    },
    forbidden: {
      statusCode: 403,
      description: '[systemCategory >fetchAllSystemCategory] Unauthorized Access',
    },
    notFound: {
      statusCode: 404,
      description: '[systemCategory >fetchAllSystemCategory] not found',
    },
    success: {
      statusCode: 200,
      description: '[systemCategory >fetchAllSystemCategory] Successfully fetched all system',
    },
    badRequest: {
      statusCode: 400,
      description: '[systemCategory >fetchAllSystemCategory] badRequest',
    },
  },
  fn: async function (inputs, exits) {
    try {
      const systemCategories = await systemCategoryService.fetchSystemCategories();
      const response = systemCategories.map((systemCategory) => {
        return {
          id: systemCategory.id,
          name: systemCategory.name
        };
      });
      return exits.success(response);
    } catch (err) {
      sails.log('[configurator > fetchAllSystemCategory]');
      sails.log.error(err);
      return exits.serverError(err);
    }
  },
};
