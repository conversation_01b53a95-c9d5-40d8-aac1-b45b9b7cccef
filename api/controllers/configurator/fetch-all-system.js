const systemService = require('../../services/ConfiguratorSystem/configuratorSystem.service');
const siteService = require('../../services/site/site.service');
module.exports = {
  friendlyName: "fetch all systems",
  description: "fetch all systems",
  example: [],
  inputs: {
    siteId: {
      type: 'string'
    }
  },
  exits: {
    serverError: {
      statusCode: 500,
      responseType: "serverError",
      description: "Server Error",
    },
    forbidden: {
      statusCode: 403,
      description: "Invalid User",
    },
    notFound: {
      statusCode: 404,
      description: "Not Found",
    },
    success: {
      statusCode: 200,
      description: "Successfully",
    },
    badRequest: {
      statusCode: 400,
      description: "Bad Request",
    },
  },
  fn: async function (inputs, exits) {
    try {
      const {siteId} = inputs;
      if (!siteId || _.isEmpty(siteId.trim())) {
        return exits.badRequest({
          err: 'Site id is not valid'
        })
      }
      const systems = await systemService.fetchAllSystems(siteId)
      return exits.success(systems);
    } catch (err) {

      switch(err.code) {
        case 'E_SITE_NOT_FOUND': {
          return exits.notFound({
            err: err.message
          })
        }
        default: {
          sails.log("configurator > fetch-all-system-categories");
          sails.log.error(err);
          return exits.serverError(err);

        }
      }

    }
  },
};
