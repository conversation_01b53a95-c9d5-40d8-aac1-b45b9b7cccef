const ElectricalPower = require("../../services/energyConsumption/src/ElectricalPower.js");
const moment = require("moment");

module.exports = {
  friendlyName: "Fetch data",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
    },
    startTime: {
      type: "string",
      required: true,
    },
    endTime: {
      type: "string",
      required: true,
    },
    deviceList: {
      type: "json",
      required: true,
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[configurator > fetch-device-data] Server Error!",
    },
    badRequest: {
      statusCode: 400,
      responseType: "badRequest",
      description: "[configurator > fetch-device-data] Bad Request!",
    },
    forbidden: {
      statusCode: 403,
      responseType: "forbidden",
      description: "[configurator > fetch-device-data] forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[configurator > fetch-device-data] Not Found",
    },
    success: {
      statusCode: 200,
      description: "[configurator > fetch-device-data] Data fetched successfully",
    },
  },

  fn: async function ({ startTime, endTime, deviceList, _userMeta: { _site: siteId } }, exits) {
    try {
      const timezoneOffset = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'Z'})
      const formattedStartTime = moment(startTime)
        .utcOffset(timezoneOffset)
        .format("YYYY-MM-DDTHH:mm:ss.SSSZ");
      const formattedEndTime = moment(endTime)
        .utcOffset(timezoneOffset)
        .format("YYYY-MM-DDTHH:mm:ss.SSSZ");
      const deviceNodes = deviceList.map(({ deviceId, class: deviceClass, abbr }) => {
        return {
          deviceId,
          deviceClass,
          abbr: [abbr],
          siteId,
          startTime: formattedStartTime,
          endTime: formattedEndTime,
        };
      });
      const res = await ElectricalPower.fetchConsumption(deviceNodes);
      return exits.success(res);
    } catch (error) {
      switch (error.statusCode) {
        case 400:
          return exits.badRequest({ error: error.message });
        default:
          sails.log.error(error);
          return exits.serverError(error);
      }
    }
  },
};
