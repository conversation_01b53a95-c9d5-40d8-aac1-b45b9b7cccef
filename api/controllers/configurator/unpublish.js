
const systemService = require('../../services/ConfiguratorSystem/configuratorSystem.service');

module.exports = {
  friendlyName: 'unpublishSystem',
  description : 'unpublish system',
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    siteId: {
      type: 'string',
      example: "acc-che",
      description: 'Name of the site',
    },
    systemId: {
      type: 'string',
      description: 'System id',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[configurator > unpublishSystem] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[configurator > unpublishSystem] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[configurator > unpublishSystem] forbidden Request!',
    },
    success: {
      statusCode: 200,
      description: '[configurator > unpublishSystem] Success'
    },
    notFound: {
      statusCode: 404,
      description: '[configurator > unpublishSystem] Not found'
    }
  },

  fn: async function (inputs, exits) {
    try {
      const {
        siteId,
        systemId,
      } = inputs;

      if (!siteId || _.isEmpty(siteId)) {
        return exits.badRequest({
          err: 'Please enter a valid site id',
        })
      }

      if(!systemId || _.isEmpty(systemId) || !Number(systemId)) {
        return exits.badRequest({
          err: 'Please enter a valid system id',
        })
      }

      await systemService.unpublishSystem(siteId, systemId);
      return exits.success({
        message: 'Successfully unpublished system',
      });
    } catch(err) {
      switch(err.code) {
        case 'E_SITE_NOT_FOUND': {
          return exits.notFound({
            err: err.message
          })
        }
        case 'E_SYSTEM_NOT_FOUND': {
          return exits.notFound({
            err: err.message
          })
        }
        case 'E_SYSTEM_NOT_UNPUBLISHED': {
          return exits.notFound({
            err: err.message
          })
        }

        case 'E_SYSTEM_NOT_PUBLISHED': {
          return exits.badRequest({
            err: err.message
          })
        }

        default: {
          sails.log("configurator > unpublished system");
          sails.log.error(err);
          return exits.serverError(err);
        }
      }
    }

  }
};
