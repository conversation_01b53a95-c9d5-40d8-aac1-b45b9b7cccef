const configuratorPageService = require("../../services/configuratorPage/configuratorPage.public");
const {
  validateFetchAllPageRequest,
} = require("../../utils/configuratorPage/requestValidator.util");

module.exports = {
  friendlyName: "Fetch all configurator pages",

  description: "",

  inputs: {
    siteId: {
      type: "string",
    },
    subsystemId: {
      type: "string",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[configurator > fetch-all-configurator-page] Server Error!",
    },
    badRequest: {
      statusCode: 400,
      responseType: "badRequest",
      description: "[configurator > fetch-all-configurator-page] Bad Request!",
    },
    forbidden: {
      statusCode: 403,
      responseType: "forbidden",
      description: "[configurator > fetch-all-configurator-page] forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[configurator > fetch-all-configurator-page] Not Found",
    },
  },
  fn: async function (inputs, exits) {
    try {
      validateFetchAllPageRequest(inputs);
      const { subsystemId } = inputs;
      const pages = await configuratorPageService.fetchAllPageDetails(subsystemId);
      return exits.success({ pages });
    } catch (err) {
      sails.log.error("[configurator > fetch-all-configurator-page]");
      sails.log.error(err);
      if (err.code == "E_INPUT_VALIDATION" || err.code == "E_PAGE_ALREADY_EXISTS") {
        return exits.badRequest({ error: err.message });
      }
      if (err.code == "E_SYSTEM_NOT_FOUND") {
        return exits.notFound({ error: err.message });
      }
      return exits.serverError(err);
    }
  },
};
