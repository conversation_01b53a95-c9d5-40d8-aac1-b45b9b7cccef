const configuratorPage = require("../../services/configuratorPage/configuratorPage.service");
const { validateSaveSvgRequest } = require("../../utils/configuratorSystem/requestValidator.util");

module.exports = {
  friendlyName: "saveSystemSVG",
  description: "Save system svg",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: {
        id: "userId",
        _role: "role",
        _site: "siteId",
      },
      description: "User meta information added by default to authenticated routes",
    },
    siteId: {
      type: "string",
      example: "acc-che",
      description: "Name of the site",
    },
    systemId: {
      type: "string",
      description: "System id",
    },
    resetDataTaggerDetail: {
      type: "boolean",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[configurator > saveSystemSVG] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[configurator > saveSystemSVG] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[configurator > saveSystemSVG] forbidden Request!",
    },
    success: {
      statusCode: 201,
      description: "[configurator > createdSystem] Success",
    },
    notFound: {
      statusCode: 404,
      description: "[configurator > saveSystemSVG] Not found",
    },
    invalidSystemState: {
      statusCode: 422,
      description: "[systems > createSystem] system is not in edit mode",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const {
        siteId,
        pageId,
        _userMeta: { id: userId },
      } = inputs;
      validateSaveSvgRequest({
        siteId,
        pageId,
      });
      this.req.file("systemSVG").upload(
        {
          maxBytes: sails.config.custom.SVG_STORAGE_LIMIT,
        },
        // eslint-disable-next-line consistent-return
        async (err, svgUploadedData) => {
          if (svgUploadedData.length === 0) {
            return exits.badRequest({
              err: "Subsystem svg file is missing",
            });
          }

          if (err && err.code === "E_EXCEEDS_UPLOAD_LIMIT") {
            return exits.badRequest({
              err: `File size limit exceeded`,
            });
          }
          if (err) {
            return exits.serverError(err);
          }

          if (svgUploadedData.length > 1) {
            return exits.badRequest({
              err: `Multiple file upload is not allowed`,
            });
          }

          try {
            const response = await configuratorPage.saveSystemSVG({
              siteId,
              pageId,
              svgFilePath: svgUploadedData[0],
              userId,
            });
            return exits.success({
              message: "Successfully svg uploaded",
              link: response.svgURL,
            });
          } catch (err) {
            switch (err.code) {
              case "E_SITE_NOT_FOUND": {
                return exits.notFound({
                  err: "site not found",
                });
              }
              case "E_SYSTEM_NOT_FOUND": {
                return exits.notFound({
                  err: "system not found",
                });
              }
              case "E_FILE_NOT_UPLOADED": {
                return exits.notFound({
                  err: "file not found",
                });
              }
              case "E_SVG_NOT_UPDATED": {
                return exits.forbidden({
                  err: "svg not updated please try again later",
                });
              }
              case "E_SYSTEM_NOT_IN_EDITABLE_MODE": {
                return exits.invalidSystemState({
                  err: err.message,
                });
              }
              case "E_INVALID_FILE_FORMAT" || "E_INVALID_FILE_CONTENT": {
                return exits.invalidSystemState({
                  err: err.message,
                });
              }
              default: {
                sails.log.error("[systems > saveSystemSVG] Error!");
                sails.log.error(err);
                exits.serverError(err);
              }
            }
          }
        },
      );
    } catch (e) {
      if (e.code === "INPUT_VALIDATION_ERROR") {
        return exits.badRequest({ message: e.message });
      }
      return exits.serverError(e);
    }
  },
};
