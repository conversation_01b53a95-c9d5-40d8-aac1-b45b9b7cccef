const systemService = require("../../services/ConfiguratorSystem/configuratorSystem.service");
const {
  validateCreateNewSystemObject,
} = require("../../utils/configuratorSystem/requestValidator.util");
const globalHelper = require("../../utils/globalhelper");
module.exports = {
  friendlyName: "createSystem",
  description: "Creating a new system",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: {
        id: "userId",
        _role: "role",
        _site: "siteId",
      },
      description: "User meta information added by default to authenticated routes",
    },
    siteId: {
      type: "string",
    },
    systemName: {
      type: "string",
      example: "laundry page",
      description: "Name of the system",
    },
    systemCategoryId: {
      type: "number",
    },
    description: {
      type: "string",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[systems > createSystem] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[systems > createSystem] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[systems > createSystem] forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[systems > createSystem] Not Found",
    },
    invalidSiteId: {
      statusCode: 422,
      description: "[systems > createSystem] siteId does not exist",
    },
    invalidSystemCategoryId: {
      statusCode: 422,
      description: "[systems > createSystem] system category does not exist",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const {
        systemName,
        systemCategoryId,
        description,
        siteId,
        _userMeta: { id: userId },
      } = inputs;
      validateCreateNewSystemObject(inputs);

      this.req.file("icon").upload(
        {
          maxBytes: sails.config.custom.ICON_IMAGE_LIMIT,
        },
        async function whenDone(err, uploadedFiles) {
          if (err && err.code === "E_EXCEEDS_UPLOAD_LIMIT") {
            return exits.badRequest({
              err: `Icon file size limit exceeded. Max limit ${sails.config.custom.ICON_IMAGE_LIMIT / 1024}Kb`,
            });
          }
          if (err) {
            return exits.serverError(err);
          }

          if (uploadedFiles.length > 1) {
            return exits.badRequest({
              err: `Maximum number of file upload limit exceeded`,
            });
          }
          const createSystemObj = {
            systemName,
            siteId,
            description,
            systemCategoryId,
          };
          if (!_.isEmpty(uploadedFiles)) {
            createSystemObj.iconFile = uploadedFiles[0];
          }

          try {
            const result = await systemService.registerSystem(createSystemObj, userId);
            return exits.success(result);
          } catch (error) {
            switch (error.code) {
              case "E_SITE_NOT_FOUND": {
                return exits.invalidSiteId({ err: error.message });
              }
              case "E_SYSTEM_CATEGORY_NOT_FOUND": {
                return exits.invalidSystemCategoryId({ err: error.message });
              }
              case "E_INVALID_FILE" || "E_INVALID_FILE_CONTENT": {
                return exits.badRequest({ message: error.message });
              }
              default: {
                sails.log.error("[systems > createSystem] Error!");
                sails.log.error(error);
                exits.serverError(error);
              }
            }
          }
        },
      );
    } catch (e) {
      if (e.code === "INPUT_VALIDATION_ERROR") {
        return exits.badRequest({ message: e.message });
      } else {
        return exits.serverError(e);
      }
    }
  },
};
