const configuratorSystemService = require("../../services/ConfiguratorSystem/configuratorSystem.service.js");
const auditEventLogService = require("../../services/auditEventLog/auditEventLog.public");

module.exports = {
  friendlyName: "save-system-order",
  description: "Save the configurator system order",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
      description: "User meta information added by default to authenticated routes",
    },
    siteId: {
      type: "string",
      required: true,
      example: "gob-coi",
    },
    systemIds: {
      type: "json",
      required: true,
      example: [42, 62, 34],
    },
  },
  exits: {
    serverError: {
      responseType: "serverError",
      description: "[configuratorSystem -> save-system-order] Server Error!",
    },
    badRequest: {
      statusCode: 400,
      responseType: "badRequest",
      description: "[configuratorSystem -> save-system-order] Bad Request!",
    },
    forbidden: {
      statusCode: 403,
      responseType: "forbidden",
      description: "[configuratorSystem -> save-system-order] forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[configuratorSystem -> save-system-order] Not Found",
    },
    success: {
      statusCode: 200,
      description: "[configuratorSystem -> save-system-order] Page created successfully",
    },
  },
  fn: async function (inputs, exits) {
    try {
      const {
        siteId,
        systemIds,
        _userMeta: { id: userId },
      } = inputs;
      if (!siteId || _.isEmpty(systemIds)) {
        return exits.badRequest({ err: "Invalid site id or systemIds" });
      }
      const auditPayload = {
        event_name: "state_update_configurator_system_order",
        user_id: userId,
        site_id: siteId,
        asset_id: "null",
        req: this.req,
        prev_state: null,
        curr_state: Object.assign({}, systemIds),
      };

      auditEventLogService.emit(auditPayload);

      await configuratorSystemService.setSystemOrder(systemIds);
      return exits.success({ message: "System order has been successfully updated." });
    } catch (error) {
      sails.log.error("[configuratorSystem -> save-system-order]", error);
      if (error.code === "E_INPUT_VALIDATION") {
        return exits.badRequest({ err: error.message });
      }
      return exits.serverError(error);
    }
  },
};
