const driverService = require('../../services/devicetype/devicetype.service');

module.exports = {
  friendlyName: 'createDriver',
  description: '',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    driver: {
      type: 'json',
      example: { deviceType: 'chillerController', driverType: '1', parameters: [], communicationCategory: "MB" },
      required: true
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[driver > createDriver] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[driver > createDriver] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[driver > createDriver] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try {
      let { driver } = inputs;
      const { svgIds } = driver;

      /** Initialising svgIds if not present */
      if (!svgIds) driver.svgIds = ["null"];

      /** Check for duplicate `abbr` in parameters */
      const abbrSet = new Set();
      const duplicates = [];

      driver.parameters.forEach(param => {
        if (abbrSet.has(param.abbr)) {
          duplicates.push(param.abbr);
        } else {
          abbrSet.add(param.abbr);
        }
      });

      if (!_.isEmpty(duplicates)) {
        return exits.badRequest({
          err: `Duplicate 'abbr' values detected in parameters: [${duplicates.join(', ')}]. Please ensure all 'abbr' values are unique.`
        });

      }

      /** Stringify parameters */
      driver.parameters = driver.parameters.map(param => JSON.stringify(param));

      await driverService.create(driver);
      return exits.success(`Driver Created Successfully with deviceType: ${driver.deviceType} and driverType: ${driver.driverType}`);
    } catch (error) {
      sails.log.error('[driver > createDriver] Error!');
      sails.log.error(error);
      return exits.serverError(error);
    }
  }
};
