
// const driverService = require('../../services/driver/driver.service');
// const globalHelper = require('../../utils/globalhelper');
// const utils = require('../../utils/driver/utils.js');

const deviceService = require("../../services/device/device.service");
const parameterService = require("../../services/parameter/parameter.service");
const deviceTypeService = require("../../services/devicetype/devicetype.public");


module.exports = {
  friendlyName: 'refreshParameters',
  description : 'Execution flow: Query all device configurations based on deviceId to find appropriate siteId. Delete all existing parameters in the parameters table. Then recreate parameters based on the driver identified by deviceType and driverType.',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    deviceIdList: {
      type: ['ref'],
      required: true,
      example: [ "2237", "65", "3130", "3055" ],
      description: 'List of deviceIds for which the driver parameters need to be refreshed.',
    },
    deviceType: {
      type: 'string',
      required: true,
      example: "chillerController",
      description: 'deviceType of the driver',
    },
    driverType: {
      type: 'string',
      required: true,
      example: "0",
      description: 'driverType of the driver.',
    },
    verifyDriverConfig: {
      type: 'boolean',
      example: false,
      description: 'If this key is specified with the value false, it will not check if the driver details match the sent deviceIds.',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[driver > refreshParameters] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[driver > refreshParameters] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[driver > refreshParameters] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      const {
        deviceIdList, driverType, deviceType
      } = inputs;
      let { verifyDriverConfig } = inputs;
      if(verifyDriverConfig == undefined){
        verifyDriverConfig = true;
      }
      let driver = await deviceTypeService.findOne({ deviceType, driverType });
			if (!driver) {
				sails.log.error("[ refresh-parameters ] Device type does not exist in DB. Aborting execution");
        return exits.badRequest(`Driver with the combination of deviceType: ${deviceType} and driverType: ${driverType} does not exist.`);
			}

      // JSON object containing keys to check and update
      const keysToUpdate = {
        functionType: 'functionType',
        mbBatchReadEnable: 'mbBatchReadEnable'
      };

      let $deviceConfigurations = deviceIdList.map(deviceId => {
        return deviceService.findOne({ deviceId })
      });
      let deviceConfigurations = await Promise.all($deviceConfigurations);
      // deviceConfigurations = deviceConfigurations.filter(Boolean);
      let deviceReconfigQueries = deviceConfigurations.map(async deviceConfig => {
        if(!deviceConfig) throw new Error("Device config not found");
        if(verifyDriverConfig !== false){
          if(deviceType != deviceConfig.deviceType || driverType!= deviceConfig.driverType)
            throw new Error('Either deviceType or driverType mismatching.');
        }

        // Check and set keys that need to be updated.
        let updateFields = {};
        for (const [key, value] of Object.entries(keysToUpdate)) {
          if (driver.hasOwnProperty(key) && deviceConfig[key] != driver[key]) {
            updateFields[key] = driver[key];
          }
        }

        // Update the device configuration if there are fields to update.
        if (Object.keys(updateFields).length > 0) {
          await deviceService.update({
            deviceId: deviceConfig.deviceId,
            siteId: deviceConfig.siteId
          }, updateFields);
        }

        let siteId = deviceConfig.siteId;
        let deviceId = deviceConfig.deviceId;
        await parameterService.destroyWithoutAbbr(siteId, deviceId);
        await parameterService.addDeviceParameters(deviceType, driverType, deviceId, siteId, driver);
      })

      let deviceReconfigResults = await Promise.allSettled(deviceReconfigQueries);
      let responseObject = {
        "successfullReconfigs": [],
        "failedReconfigs": {}
      };
      deviceReconfigResults.forEach((result, index) => {
        let deviceId = deviceIdList[index];
        if(result.status == "fulfilled")
          responseObject["successfullReconfigs"].push(deviceId);
        else responseObject["failedReconfigs"][deviceId] = result.reason.message;
      })
      exits.success(responseObject);
    } catch(error) {
      sails.log.error('[driver > refreshParameters] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
