const dynamokeystoreservice = require("../../services/dynamokeystore/dynamokeystore.public");

module.exports = {
  friendlyName: "getDriverBasedOnFlowParameter",
  description: "Fetch the driver details wrt flow based parameters",
  example: [],
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: {
        id: "userId",
        _role: "role",
        _site: "siteId",
      },
      description: "User meta information added by default to authenticated routes",
    },
  },
  exits: {
    serverError: {
      statusCode: 500,
      responseType: "serverError",
      description: "Server Error",
    },
    forbidden: {
      statusCode: 403,
      description: "Invalid User",
    },
    notFound: {
      statusCode: 404,
      description: "Not Found",
    },
    success: {
      statusCode: 200,
      description: "Successfully",
    },
    badRequest: {
      statusCode: 400,
      description: "Bad Request",
    },
  },
  fn: async function (inputs, exits) {
    try {
      const flowParamsRecord = await dynamokeystoreservice.findOne("sankey_flow_params_config");
      const flowParamsConfig = JSON.parse(flowParamsRecord?.value) || {};
      return exits.success(flowParamsConfig);
    } catch (err) {
      switch (err.code) {
        default: {
          sails.log("configurator > get-driver-based-on-flow-parameter");
          sails.log.error(err);
          return exits.serverError(err);
        }
      }
    }
  },
};
