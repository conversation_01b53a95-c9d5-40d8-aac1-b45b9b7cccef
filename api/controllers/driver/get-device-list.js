
const driverService = require('../../services/devicetype/devicetype.service');
const deviceService = require("../../services/device/device.service");

module.exports = {
  friendlyName: 'getDeviceList',
  description : 'API returns a list of devices which match the same criteria.',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    additionalFilters: {
      type: 'json',
      required: false,
      example: { siteId: "mgch" },
      description: 'Any additional filters if required.',
    },
    deviceType: {
      type: 'string',
      required: true,
      example: "chillerController",
      description: 'deviceType of the driver',
    },
    driverType: {
      type: 'string',
      required: true,
      example: "0",
      description: 'driverType of the driver.',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[driver > getDeviceList] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[driver > getDeviceList] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[driver > getDeviceList] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      const {
        additionalFilters, deviceType, driverType
      } = inputs;

      let driver = await driverService.findOne({
        deviceType,
        driverType
      });

      if(!driver){
        return exits.badRequest({
          problems: [`No driver with combination of deviceType: ${deviceType} and driverType: ${driverType} found in database`]
        });
      }

      let deviceConfigurations = await deviceService.find({
        ...additionalFilters,
        deviceType,
        driverType
      });

      let deviceIdList = deviceConfigurations.map(device => device.deviceId);

      return exits.success({
        deviceIdList,
        deviceConfigurations
      });

    } catch(error) {
      sails.log.error('[driver > getDeviceList] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
