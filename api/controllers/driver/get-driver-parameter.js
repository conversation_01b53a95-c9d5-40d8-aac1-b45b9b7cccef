const driverService = require('../../services/devicetype/devicetype.public');


module.exports = {


  friendlyName: 'Fetch all driver parameters',


  description: '',


  inputs: {
    deviceType: {
      type: 'string',
    },
    deviceClass: {
      type: 'string',
    }
  },


  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[driver > Fetch all driver parameters] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[driver > Fetch all driver parameters] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[driver > Fetch all driver parameters] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[driver > Fetch all driver parameters] Not Found'
    },
    success: {
      statusCode: 200,
      description: '[driver > Fetch all driver parameters] driver parameters fetched successfully'
    }
  },


  fn: async function (inputs, exits) {
    try {
      const { deviceType, deviceClass } = inputs;
      if (
        !deviceClass
        || !['device', 'component'].includes(deviceClass)) {
          return exits.badRequest({
            err: 'Invalid deviceClass: only [device, component] allowed'
          })
      }
      const response = await driverService.getDriverParameters(deviceType, deviceClass);
      return exits.success(response);
    } catch (error) {
        if (error.code == 'E_DRIVER_CONFIG_NOT_FOUND') {
            return exits.badRequest({
                err: error.message
            })
        } else {
            return exits.serverError(error)
        }
    }
  }


};
