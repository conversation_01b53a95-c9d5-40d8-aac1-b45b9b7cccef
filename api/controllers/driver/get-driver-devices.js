const DeviceTypeService = require('../../services/devicetype/devicetype.service');
const DriverDeviceUtils = require('../../utils/driver/get-driver-device.utils');

module.exports = {
  friendlyName: 'findDriverDevices',
  description: 'to get drivers data of devices in csv',
  example: [
    'curl --location --request GET \'localhost:1337/m2/driver/v2/devices --header \'Authorization: Bearer token\'',
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      description: 'Auto attached by isAuthorized policy.',
      required: true,
    },
  },
  exits: {
    serverError: {
      description: 'Failed to fetched drivers',
      responseType: 'serverError',
      statusCode: 500,
    },
  },
  async fn(inputs, exits) {
    try {
      const driver = await DeviceTypeService.find({ class: 'devices' });
      if (driver.length === 0) {
        return exits.badRequest({ problems: ['Failed to fetched drivers'] });
      }
      const filteredData = DriverDeviceUtils.filterDriverDevices(driver);
      const generatedExcelData = await DriverDeviceUtils.generateExcel(filteredData);
      this.res.setHeader('Content-disposition', 'attachment;');
      this.res.contentType('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      return this.res.send(generatedExcelData);
    } catch (error) {
      sails.log.error('[device > findDriver] Error!');
      sails.log.error(error);
      return exits.serverError(error);
    }
  },
};
