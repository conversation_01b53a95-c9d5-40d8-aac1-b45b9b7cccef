const moment = require('moment-timezone');
const BACnetErrorHandler = require('../../utils/bacnet/error-handler.util');

module.exports = {
  friendlyName: 'BACnet Discovery Result',
  description: 'Receive BACnet device discovery results from IoT Core',

  inputs: {
    siteId: {
      type: 'string',
      required: true,
      description: 'The site ID where the BACnet controller is located'
    },
    controllerId: {
      type: 'string',
      required: true,
      description: 'The BACnet slave controller device ID'
    },
    jobId: {
      type: 'string',
      required: true,
      description: 'The discovery job ID'
    },
    points: {
      type: 'ref',
      required: true,
      description: 'The discovered BACnet points data'
    }
  },

  exits: {
    success: {
      description: 'Discovery result received and queued for processing'
    },
    badRequest: {
      statusCode: 400,
      description: 'Invalid request parameters'
    },
    notFound: {
      statusCode: 404,
      description: 'Job not found or invalid'
    },
    serverError: {
      statusCode: 500,
      description: 'Internal server error'
    }
  },

  fn: async function (inputs, exits) {
    const { siteId, controllerId, jobId, points } = inputs;

    try {
      // Validate inputs
      BACnetErrorHandler.validateDiscoveryResult(siteId, controllerId, jobId, points);

      sails.log.info(`BACnet discovery result received for site: ${siteId}, controller: ${controllerId}, job: ${jobId}`);

      // Validate active job
      const activeJobKey = `activeJob:BACnetDiscovery:${siteId}:${controllerId}`;
      const activeJobId = await CacheService.get(activeJobKey);

      if (!activeJobId || activeJobId !== jobId) {
        sails.log.warn(`Invalid or expired job ID: ${jobId} for site: ${siteId}, controller: ${controllerId}`);
        return exits.notFound({
          error: 'Invalid job',
          message: 'Job not found or has expired'
        });
      }

      // Validate job exists and is in pending state
      const jobKey = `BACnetDiscovery:job:${jobId}`;
      const jobData = await CacheService.hgetall(jobKey);

      if (!jobData || !jobData.jobId) {
        return exits.notFound({
          error: 'Job not found',
          message: `Discovery job ${jobId} not found`
        });
      }

      if (jobData.status !== 'pending') {
        return exits.badRequest({
          error: 'Invalid job state',
          message: `Job ${jobId} is not in pending state. Current state: ${jobData.status}`
        });
      }

      // Validate points data structure
      if (!points || !points.devices || !Array.isArray(points.devices)) {
        return exits.badRequest({
          error: 'Invalid points data',
          message: 'Points data must contain a devices array'
        });
      }

      // Update job status to processing
      const timestamp = moment.tz('UTC').toISOString();
      await CacheService.hmset(jobKey, {
        status: 'processing',
        updatedAt: timestamp,
        receivedAt: timestamp
      });

      // Add job to BullMQ queue for processing
      const queueData = {
        jobId,
        siteId,
        controllerId,
        points,
        timestamp
      };

      const bacnetQueue = require('../../services/queue/bacnet-discovery-queue.service');
      await bacnetQueue.addJob(queueData, {
        priority: 5
      });

      sails.log.info(`BACnet discovery result queued for processing. Job: ${jobId}, devices count: ${points.devices.length}`);

      return exits.success({
        message: 'Discovery result received and queued for processing',
        jobId,
        status: 'processing',
        devicesCount: points.devices.length,
        timestamp
      });

    } catch (error) {
      const errorResponse = BACnetErrorHandler.handleDiscoveryResultError(error, siteId, controllerId, jobId);
      return exits.serverError(errorResponse);
    }
  }
};
