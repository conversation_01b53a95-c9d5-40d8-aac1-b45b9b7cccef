const moment = require('moment-timezone');
const { v4: uuidv4 } = require('uuid');
const BACnetErrorHandler = require('../../utils/bacnet/error-handler.util');

module.exports = {
  friendlyName: 'BACnet Discovery Request',
  description: 'Trigger BACnet device discovery for a specific slave controller',

  inputs: {
    siteId: {
      type: 'string',
      required: true,
      description: 'The site ID where the BACnet controller is located'
    },
    controllerId: {
      type: 'string',
      required: true,
      description: 'The BACnet slave controller device ID'
    }
  },

  exits: {
    success: {
      description: 'Discovery request triggered successfully'
    },
    badRequest: {
      statusCode: 400,
      description: 'Invalid request parameters'
    },
    notFound: {
      statusCode: 404,
      description: 'Site or controller not found'
    },
    conflict: {
      statusCode: 409,
      description: 'Discovery already in progress'
    },
    serverError: {
      statusCode: 500,
      description: 'Internal server error'
    }
  },

  fn: async function (inputs, exits) {
    const { siteId, controllerId } = inputs;

    try {
      // Validate inputs
      BACnetErrorHandler.validateDiscoveryRequest(siteId, controllerId);

      sails.log.info(`BACnet discovery request initiated for site: ${siteId}, controller: ${controllerId}`);

      // Validate site exists
      const site = await sails.models.sites.findOne({ id: siteId });
      if (!site) {
        return exits.notFound({
          error: 'Site not found',
          message: `Site with ID ${siteId} does not exist`
        });
      }

      // Validate controller exists and is a BACnet slave controller
      const controller = await sails.models.devices.findOne({
        deviceId: controllerId,
        siteId: siteId,
        isSlaveController: 1
      });

      if (!controller) {
        return exits.notFound({
          error: 'BACnet controller not found',
          message: `BACnet slave controller with ID ${controllerId} not found in site ${siteId}`
        });
      }

      // Check if discovery is already in progress
      const activeJobKey = `activeJob:BACnetDiscovery:${siteId}:${controllerId}`;
      const existingJob = await CacheService.get(activeJobKey);

      if (existingJob) {
        sails.log.warn(`Discovery already in progress for site: ${siteId}, controller: ${controllerId}`);
        return exits.conflict({
          error: 'Discovery in progress',
          message: 'Please wait, syncing data. Discovery is already in progress for this controller.',
          jobId: existingJob
        });
      }

      // Generate new job ID
      const jobId = uuidv4();
      const timestamp = moment.tz('UTC').toISOString();

      // Set active job in Redis with 2 hour TTL
      await CacheService.setex(activeJobKey, 7200, jobId);

      // Create job tracking record
      const jobKey = `BACnetDiscovery:job:${jobId}`;
      const jobData = {
        jobId,
        siteId,
        controllerId,
        status: 'pending',
        createdAt: timestamp,
        updatedAt: timestamp
      };

      await CacheService.hmset(jobKey, jobData);
      await CacheService.expire(jobKey, 7200); // 2 hour TTL

      // Find the primary JouleBox for this site to get the correct topic
      const primaryJouleBox = await sails.models.devices.findOne({
        siteId: siteId,
        deviceType: 'joulebox',
        type: 'Primary'
      });

      if (!primaryJouleBox) {
        // Clean up Redis entries
        await CacheService.del(activeJobKey);
        await CacheService.del(jobKey);

        return exits.notFound({
          error: 'Primary JouleBox not found',
          message: `No primary JouleBox found for site ${siteId}`
        });
      }

      // Publish discovery request to IoT Core
      const topic = `${siteId}/config/${controllerId}/discovery-request`;
      const payload = {
        jobId,
        slaveControllerId: controllerId,
        timestamp,
        requestedBy: this.req.user?.id || 'system'
      };

      await IotCoreService.publish(topic, payload);

      sails.log.info(`BACnet discovery request published to topic: ${topic}, jobId: ${jobId}`);

      return exits.success({
        message: 'Discovery request triggered successfully',
        jobId,
        status: 'pending',
        timestamp
      });

    } catch (error) {
      const errorResponse = BACnetErrorHandler.handleDiscoveryRequestError(error, siteId, controllerId);

      // Return appropriate exit based on status code
      switch (errorResponse.statusCode) {
        case 400:
          return exits.badRequest(errorResponse);
        case 404:
          return exits.notFound(errorResponse);
        case 409:
          return exits.conflict(errorResponse);
        default:
          return exits.serverError(errorResponse);
      }
    }
  }
};
