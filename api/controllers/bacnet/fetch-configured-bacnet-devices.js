const deviceService = require("../../services/device/device.public");

module.exports = {
  friendlyName: "Fetch bacnet devices",
  description: "fetch bacnet devices as third party master controllers.",

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
    },
    siteId: {
      type: "string",
      required: true,
      description: "Site id to fetch the bacnet devices",
    },
    secondaryControllerId: {
      type: "number",
      required: true,
      description: "controller id of the bacnet slave controller",
    },
  },

  exits: {
    success: {
      description: "Success",
    },
    serverError: {
      statusCode: 500,
      description: "Server error occurred",
    },
    notFound: {
      statusCode: 404,
      description: "Devices not found.",
    },
    badRequest: {
      statusCode: 400,
      description: "Bad request",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { siteId, secondaryControllerId } = inputs;

      //query devices table from dynamo using site id and secondary controller id
      const devices = await deviceService.find({
        siteId,
        secondaryControllerId,
      });

      // no devices are found
      if (!devices || devices.length === 0) {
        return exits.notFound({
          err: `No BACnet devices found for the given site and slave controller.`,
        });
      }

      return exits.success({ devices });
    } catch (error) {
      sails.log.error(`Error in fetchBacnetDevice: ${error}`);

      switch (error.code) {
        case "E_NOT_FOUND": {
          return exits.notFound({ error: error.message });
        }
        default: {
          return exits.serverError(error);
        }
      }
    }
  },
};
