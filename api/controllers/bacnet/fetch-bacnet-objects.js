module.exports = {
  friendlyName: "Fetch bacnet objects",
  description: "fetch bacnet objects related to third party controllers.",

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
    },
    siteId: {
      type: "string",
      required: true,
      description: "Site id to fetch the bacnet devices",
    },
    id: {
      type: "number",
      required: true,
      description: "controller id of the bacnet slave controller.",
    },
  },

  exits: {
    success: {
      description: "Success",
    },
    serverError: {
      statusCode: 500,
      description: "Server error occurred",
    },
    notFound: {
      statusCode: 404,
      description: "Devices not found.",
    },
    badRequest: {
      statusCode: 400,
      description: "Bad request",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { siteId, id } = inputs;
      const thirdPartyMasterControllerId = id;

      // - thirdPartyMasterControllerId = dynamo primary key for the third party master controller.
      // - use deviceId to query the third_party_bms_devices table
      // - use the third_party_bms_devices.id to get the related objects from third_party_bms_device_objects.device_ref_id
      // - use third_party_bms_device_objects.id to get the properties from third_party_bms_device_objects.object_ref_id

      // The input `id` corresponds to the `bms_connector_ref_id` in the ThirdPartyBmsDevices table.
      const devices = await ThirdPartyBmsDevices.find({
        refDeviceId: thirdPartyMasterControllerId,
      });

      if (!devices || devices.length === 0) {
        throw {
          code: "E_NOT_FOUND",
          message: "No devices found for this controller.",
        };
      }

      const deviceIds = devices.map((d) => d.id);

      // Fetch all objects for the found devices in a single query.
      const allObjects = await ThirdPartyBmsDeviceObjects.find({
        deviceRefId: { in: deviceIds },
      });

      // If objects are found, fetch their properties.
      if (allObjects.length > 0) {
        const objectIds = allObjects.map((o) => o.id);

        // Fetch all properties for the found objects in a single query.
        const allProperties = await ThirdPartyBmsObjectProperties.find({
          objectRefId: { in: objectIds },
        });

        // Group properties by their parent object's ID for efficient lookup.
        const propertiesByObjectId = allProperties.reduce((acc, prop) => {
          (acc[prop.objectRefId] = acc[prop.objectRefId] || []).push(prop);
          return acc;
        }, {});

        // Attach properties to their respective objects.
        allObjects.forEach((obj) => {
          obj.properties = propertiesByObjectId[obj.id] || [];
        });
      }

      // Group objects by their parent device's ID for efficient lookup.
      const objectsByDeviceId = allObjects.reduce((acc, obj) => {
        (acc[obj.deviceRefId] = acc[obj.deviceRefId] || []).push(obj);
        return acc;
      }, {});

      // Attach objects (now containing their properties) to their respective devices.
      devices.forEach((device) => {
        device.objects = objectsByDeviceId[device.id] || [];
      });

      return exits.success({ devices });
    } catch (error) {
      sails.log.error(`Error in fetchBacnetDevice: ${error}`);

      switch (error.code) {
        case "E_NOT_FOUND": {
          return exits.notFound({ error: error.message });
        }
        default: {
          return exits.serverError(error);
        }
      }
    }
  },
};
