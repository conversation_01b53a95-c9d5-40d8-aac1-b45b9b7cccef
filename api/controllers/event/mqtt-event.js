const eventService = require("../../services/event/event.service");
const selfUtils = require("../../utils/event/utils");

module.exports = {
  friendlyName: "MQTT event trigger",
  description: "This action is responsible to receive the mqtt events",
  example: [`curl -X GET "http://localhost:1337/m2/event/v2/mqttEvent`],

  inputs: {
    secret: {
      type: "string",
      example: "12345",
      required: true,
      isIn: [selfUtils.MQTT_SECRET],
    },
    topic: {
      type: "string",
      example: "sjo-del/feedback/1234/recipeConfig",
      required: true,
    },
    message: {
      type: "json",
      example: "any stringified json",
      required: true,
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[event > mqtt-event] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[event > mqtt-event] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[event > mqtt-event] forbidden!",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { topic, message } = inputs;
      eventService.notifyRespectiveService(topic, message);
      return exits.success();
    } catch (error) {
      sails.log.error("[event > mqtt-event] Error!");
      sails.log.error(error);
      exits.serverError(error);
    }
  },
};
