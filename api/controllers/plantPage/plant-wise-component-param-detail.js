const DynamoKeyStore = require('../../services/dynamokeystore/dynamokeystore.public')
module.exports = {
  friendlyName: 'getPlantWiseComponentParamDetail',
  description: 'getPlantWiseComponentParamDetail',
  example: [
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    plantName: {
      type: 'string',
      required:true
    },
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[plantPage > getPlantWiseComponentParamDetail] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[plantPage > getPlantWiseComponentParamDetail] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[plantPage > getPlantWiseComponentParamDetail] forbidden Request!',
    },
  },

  async fn(inputs, exits) {
    try {
      const {
        _userMeta: { _site: siteId },
      } = inputs;
      const { plantName } = inputs;
      let query = {};

      let plantPageDetail = await DynamoKeyStore.find({ where: { key: plantName },select:['key','value'] })
      if(_.isEmpty(plantPageDetail)){
        return exits.badRequest({message:`plant page detail not found`})
      }
      let value = JSON.parse(plantPageDetail[0].value);
      return exits.success(value);
    } catch (error) {
      sails.log.error('[plantPage > getPlantWiseComponentParamDetail] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  },
};
