const { DriverControlConfigMapping } = require('../../services/controlRelationshipConfig/controlsRelationshipConfig.service');


module.exports = {
  friendlyName: 'download-driver-controls-relationship',
  description: '',
  inputs: {
    format: {
      isIne: ['excel', 'json'],
      type:'string'
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[controlsRelationshipConfig > download-driver-controls-relationship] Server Error!',
    },
    noContent: {
      responseType: 'noContent',
      statusCode: 204,
      description: '[controlsRelationshipConfig > download-driver-controls-relationship] No Content!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[controlsRelationshipConfig > download-driver-controls-relationship] forbidden Request!',
    },
    badRequest: {
      responseType: 'badRequest',
      statusCode: 400,
      description: '[controlsRelationshipConfig > download-driver-controls-relationship] Bad Request!',
    }
  },

  fn: async function ({format}, exits) {
    try {
      const allowedFormats = ['excel', 'json']
        if (!format || !_.includes(allowedFormats, format)) {
          return exits.badRequest({
            err: 'Invalid format. format mus be either in [excel, json]'
        })
      }
      let fetchedControlRelationships = await DriverControlConfigMapping.getDriverControlsRelationship()
      if (format == 'json') {
        return exits.success(fetchedControlRelationships)
      }
      const generatedExcelSheet = await DriverControlConfigMapping.generateExcel(fetchedControlRelationships)
      this.res.setHeader('Content-disposition', 'attachment;');
      this.res.contentType('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      return this.res.send(generatedExcelSheet);
    } catch(e) {
      if (e.code == 'E_DRIVER_NOT_EXIST') {
        return  this.res.status(204).send({
          err: e.message
        });
      } else {
        return exits.serverError(e);
      }
    }
  }
};
