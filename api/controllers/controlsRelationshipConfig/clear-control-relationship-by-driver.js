const controlRelationshipService = require('../../services/controlRelationshipConfig/controlsRelationshipConfig.service');
const driverTypeService = require('../../services/devicetype/devicetype.public');
const { ERROR_CODE } = require('../../utils/controlsConfig/constants')
const deviceTypeService = require('../../services/devicetype/devicetype.public');
module.exports = {
  friendlyName: 'clearControlsRelationship',
  description: '',
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: {
        id: 'userId',
        _role: 'role',
        _site: 'siteId'
      },
      description: 'User meta information added by default to authenticated routes',
    },
    deviceType: {
      type: 'string',
      required: true,
    },
    driverType: {
      type: 'string',
      required: true,
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[controlsRelationshipConfig > clearControlsRelationship] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[controlsRelationshipConfig > clearControlsRelationship] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[controlsRelationshipConfig > clearControlsRelationship] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    const {
      _userMeta,
      deviceType,
      driverType,
    } = inputs;
    let driverDetails = await deviceTypeService.findOne({
      deviceType,
      driverType,
      class:'components'
    });
    if (_.isEmpty(driverDetails)) {
      return exits.badRequest({
        ...ERROR_CODE['INVALID_DRIVER_ID'],
        data: {
          driverType,
          deviceType
        }
      });
    }
    let clearedControlRelationships = await controlRelationshipService.DriverControlConfigMapping.clearControlRelationship({deviceType,driverType})
    return exits.success({message:`You have successfully cleared the control relationships for driver '${driverType} and deviceType '${deviceType}'`,clearedControlRelationships})
  }
};
