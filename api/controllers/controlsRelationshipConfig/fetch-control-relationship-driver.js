const devicetypeService = require('../../services/devicetype/devicetype.public');
const { DriverControlConfigMapping } = require('../../services/controlRelationshipConfig/controlsRelationshipConfig.service');
const { ERROR_CODE } = require('../../utils/controlsConfig/constants');

module.exports = {
  friendlyName: 'fetchControlsRelationshipDriver',
  description: '',
  inputs: {
    deviceType: {
      type: 'string',
      required: true,
    },
    driverType: {
      type: 'string',
      required: true,
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[controlsRelationshipConfig > fetchControlsRelationshipDriver] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[controlsRelationshipConfig > fetchControlsRelationshipDriver] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[controlsRelationshipConfig > fetchControlsRelationshipDriver] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    const {
      deviceType,
      driverType,
    } = inputs;
    let driverDetails = await devicetypeService.findOne({
      deviceType,
      driverType,
      class: 'components'
    });
    if (_.isEmpty(driverDetails)) {
      return exits.badRequest({
        // eslint-disable-next-line dot-notation
        ...ERROR_CODE['INVALID_DRIVER_ID'],
        data: {
          driverType,
          deviceType
        }
      });
    }
    let fetchedControlRelationships = await DriverControlConfigMapping.fetchControlRelationshipDriver({
      deviceType,
      driverType
    });
    return exits.success(fetchedControlRelationships);
  }
};
