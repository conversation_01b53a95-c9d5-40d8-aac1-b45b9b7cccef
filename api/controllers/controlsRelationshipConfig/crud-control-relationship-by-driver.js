const controlsRelationshipConfigService = require("../../services/controlRelationshipConfig/controlsRelationshipConfig.service");
module.exports = {
  friendlyName: "crud-controls-relationship",
  description: "CRUD operation on control relationship",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: {
        id: "userId",
        _role: "role",
        _site: "siteId",
      },
      description: "User meta information added by default to authenticated routes",
    },
    deviceType: {
      type: "string",
      // JOi validation is going for handling specific err message in-respective of sails
    },
    driverType: {
      type: "string",
    },
    operation: {
      type: "string",
    },
    data: {
      type: "json",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[controlRelationshipByDriver > crudControlRelationship] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[controlRelationshipByDriver > crudeControlRelationship] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[controlRelationshipByDriver > crudControlRelationship] forbidden Request!",
    },
    success: {
      statusCode: 201,
      description: "successfully created control relationship",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const {
        _userMeta: { id: userId },
        deviceType,
        driverType,
        data,
        operation,
      } = inputs;
      let response;
      if (operation === "append") {
        response = await controlsRelationshipConfigService.DriverControlConfigMapping.appendNewItem(
          {
            driverType,
            deviceType,
            newNode: data,
          },
          userId,
        );
      } else if (operation === "update") {
        response = await controlsRelationshipConfigService.DriverControlConfigMapping.updateItem(
          {
            driverType,
            deviceType,
            updatedNode: data,
          },
          userId,
        );
      } else if (operation === "delete") {
        response = await controlsRelationshipConfigService.DriverControlConfigMapping.deleteItem(
          {
            driverType,
            deviceType,
            deleteControlsList: data,
          },
          userId,
        );
      } else {
        return exits.badRequest({
          err: "Only [append, update, delete] operation are allowed",
        });
      }
      return exits.success(response);
    } catch (e) {
      if (e.statusCode == 400) {
        return exits.badRequest(e.data);
      } else {
        return exits.serverError(e);
      }
    }
  },
};
