const flaverr = require("flaverr");
module.exports = {
  friendlyName: "updateNewControlClass",
  description: "",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: {
        id: "userId",
        _role: "role",
        _site: "siteId",
      },
      description: "User meta information added by default to authenticated routes",
    },
    driverConfigs: {
      type: "ref",
      required: true,
      custom: (value) => {
        return (
          Array.isArray(value) &&
          value.every(
            (item) =>
              Object.prototype.hasOwnProperty.call(item, "deviceType") &&
              typeof item.deviceType === "string" &&
              item.deviceType.trim().length > 0 &&
              Object.prototype.hasOwnProperty.call(item, "driverType") &&
              typeof item.driverType === "string" &&
              item.driverType.trim().length > 0 &&
              Object.prototype.hasOwnProperty.call(item, "controlAbbr") &&
              typeof item.controlAbbr === "string" &&
              item.controlAbbr.trim().length > 0 &&
              Object.prototype.hasOwnProperty.call(item, "controlClass") &&
              typeof item.controlClass === "string" &&
              item.controlClass.trim().length > 0,
          )
        );
      },
      description:
        "Array of driver configurations, each including deviceType, driverType, controlAbbr, and controlClass",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[controlsRelationshipConfig > updateNewControlClass] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[controlsRelationshipConfig > updateNewControlClass] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[controlsRelationshipConfig > updateNewControlClass] forbidden Request!",
    },
  },

  fn: async function ({ driverConfigs, _userMeta: { id: userId } }, exits) {
    try {
      for (const { deviceType, driverType, controlAbbr, controlClass } of driverConfigs) {
        await sails.getDatastore("postgres").transaction(async (db) => {
          const updatedDriver = await DriverControlsRelationshipConfig.update(
            {
              driverType,
              deviceType,
              controlAbbr,
              createdBy: userId,
            },
            { controlClass },
          )
            .fetch()
            .usingConnection(db);
          if (!updatedDriver.length) {
            throw flaverr(
              "E_INPUT_VALIDATION",
              new Error(
                `Invalid driver type(${driverType}) or device type(${deviceType}) or control abbr(${controlAbbr})`,
              ),
            );
          }
          for (const it of updatedDriver) {
            const { id } = it;
            await DeviceControlRelationshipConfig.update(
              { driverRefId: id },
              {
                controlClass,
                createdBy: userId,
              },
            ).usingConnection(db);
          }
        });
      }
      return exits.success({
        message: `You have successfully updated the control class`,
      });
    } catch (error) {
      if (error.code == "E_INPUT_VALIDATION") {
        return exits.badRequest({ message: error.message });
      }
      sails.log.error(error);
      return exits.serverError(error);
    }
  },
};
