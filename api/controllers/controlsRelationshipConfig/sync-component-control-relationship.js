const flaverr = require("flaverr");
const {
  DriverControlConfigMapping,
} = require("../../services/controlRelationshipConfig/controlsRelationshipConfig.service");
const ComponentService = require("../../services/component/component.service");
const SitesService = require("../../services/site/site.public");
module.exports = {
  friendlyName: "syncComponentControlRelationship",
  description: "",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: {
        id: "userId",
        _role: "role",
        _site: "siteId",
      },
      description: "User meta information added by default to authenticated routes",
    },
    componentId: {
      type: "json",
      required: true,
      example: ["sjo-del_177"],
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[controlsRelationshipConfig  > syncComponentControlRelationship] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[controlsRelationshipConfig > syncComponentControlRelationship] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description:
        "[controlsRelationshipConfig > syncComponentControlRelationship] forbidden Request!",
    },
    unprocessableEntity: {
      statusCode: 422,
      description:
        "[controlsRelationshipConfig > syncComponentControlRelationship] Unprocessable Entity",
    },
  },

  fn: async function ({ componentId }, exits) {
    try {
      if (!Array.isArray(componentId) || componentId.length == 0 || componentId.length > 1)
        throw flaverr("E_INVALID_INPUT", new Error(`componentId-${componentId} is not valid`));

      const componentDetails = await ComponentService.findOne({ where: componentId[0] });
      if (!componentDetails)
        throw flaverr(
          "E_INVALID_COMPONENT_ID",
          new Error(`componentId-${componentId[0]} does not exists in the database`)
        );

      const { deviceType, driverType } = componentDetails;

      const syncedControls = await DriverControlConfigMapping.syncControlRelationship(
        componentId,
        deviceType,
        driverType
      );
      const res = {
        sync_control_count: syncedControls.length,
        message: "sync completed",
      };
      return exits.success(res);
    } catch (error) {
      if (
        error.code == "E_INVALID_INPUT" ||
        error.code == "E_INVALID_COMPONENT_ID" ||
        error.code == "NO_CONTROL_RELATIONSHIP_CONFIG_EXISTS"
      )
        return exits.badRequest({ error: error.message });
      if (error.code == "E_DB") return exits.unprocessableEntity({ error: error.message });
      return exits.serverError(error);
    }
  },
};
