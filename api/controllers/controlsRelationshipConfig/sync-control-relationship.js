const flaverr = require('flaverr');
const {
  DriverControlConfigMapping,
} = require("../../services/controlRelationshipConfig/controlsRelationshipConfig.service");
const ComponentService = require("../../services/component/component.service");

module.exports = {
  friendlyName: "syncControlRelationship",
  description: "",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: {
        id: "userId",
        _role: "role",
        _site: "siteId",
      },
      description: "User meta information added by default to authenticated routes",
    },
    deviceType: {
      type: "string",
      required: true,
      example: "chiller",
    },
    driverType: {
      type: "string",
      required: true,
      example: "0",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[controlsRelationshipConfig  > syncControlRelationship] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[controlsRelationshipConfig > syncControlRelationship] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[controlsRelationshipConfig > syncControlRelationship] forbidden Request!",
    },
    unprocessableEntity: {
      statusCode: 422,
      description: "[controlsRelationshipConfig > syncControlRelationship] Unprocessable Entity",
    },
  },

  fn: async function ({ deviceType, driverType }, exits) {
    try {
      const components = await ComponentService.find({
        deviceType,
        driverType,
      });
      const componentIds = components.map((component) => component.deviceId);
      const syncedControlRelationship = await DriverControlConfigMapping.syncControlRelationship(
        componentIds,
        deviceType,
        driverType,
      );
      const res = {
        sync_control_count: syncedControlRelationship.length,
        message: "sync completed",
      };
      return exits.success(res);
    } catch (error) {
      if (error.code == "NO_CONTROL_RELATIONSHIP_CONFIG_EXISTS")
        return exits.badRequest({ error: error.message });
      if (error.code == "E_DB") return exits.unprocessableEntity({ error: error.message });
      return exits.serverError(error);
    }
  },
};
