const controlConfig = require('../../services/controlRelationshipConfig/controlsRelationshipConfig.service');
const deviceTypeService = require('../../services/devicetype/devicetype.public');
const utils = require('../../utils/controlsConfig');
const { ERROR_CODE } = require('../../utils/controlsConfig/constants');
module.exports = {
  friendlyName: 'createControlRelationship',
  description: '',
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: {
        id: 'userId',
        _role: 'role',
        _site: 'siteId'
      },
      description: 'User meta information added by default to authenticated routes',
    },
    deviceType: {
      type: 'string',
      required: true,
    },
    driverType: {
      type: 'string',
      required: true,
    },
    controlRelationship: {
      type: 'json',
      required: true,
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[controlRelationshipByDriver > createControlRelationship] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[controlRelationshipByDriver > createControlRelationship] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[controlRelationshipByDriver > createControlRelationship] forbidden Request!',
    },
    success:{
      statusCode:201,
      description:'successfully created control relationship'
    }
  },

  fn: async function (inputs, exits) {
    const {
      _userMeta,
      deviceType,
      driverType,
      controlRelationship
    } = inputs;

    let driverControlConfig = await controlConfig.DriverControlConfigMapping.find({deviceType,driverType,status:1});
    if(!_.isEmpty(driverControlConfig)){
      return exits.badRequest({
        ...ERROR_CODE['CONTROl_RELATIONSHIP_ALREADY_EXIST'],
        data: {
          existingControlRelationship:driverControlConfig
        }
      });
    }

    let driverDetails = await deviceTypeService.findOne({
      deviceType,
      driverType,
      class:'components'
    });
    if (_.isEmpty(driverDetails)) {
      return exits.badRequest({
        ...ERROR_CODE['INVALID_DRIVER_ID'],
        data: {
          driverType,
          deviceType
        }
      });
    }

    const dataParams = new Set(driverDetails.parameters.filter(it => (it.type === 'data'))
      .map(it => it.abbr));
    const commandParams = new Set(driverDetails.parameters.filter(it => (it.type === 'command'))
      .map(it => it.abbr));
    if (commandParams.size === 0) {
      return exits.badRequest({
        ...ERROR_CODE['CONTROl_PARAM_NOT_FOUND'],
        data: {
          driverParam: driverDetails.parameters
        }
      });
    }
    let error = utils.validateControlConfigList(controlRelationship, [...dataParams], [...commandParams]);
    if (error) {
      return exits.badRequest({
        ...ERROR_CODE['INVALID_CONTROL_CONFIG_SCHEMA'],
        data: error.details.map(it => it.message)
      });
    }
    const controlsRecord = controlRelationship.map(it=>{
      it.deviceType = deviceType;
      it.driverType = driverType
      it.createdBy = _userMeta.id
      return it;
    })
    await controlConfig.DriverControlConfigMapping.createEach(controlsRecord);
    let response = await controlConfig.DriverControlConfigMapping.find({driverType,deviceType,status:1 })
    return exits.success(response);
  }
};
