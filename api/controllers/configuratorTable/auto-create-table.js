const { autoCreateTable } = require('../../services/ConfiguratorTable/configuratorTable.service');
const { validateAutoCreateTableRequest } = require('../../utils/ConfiguratorTable/inputValidation.utils');

module.exports = {


  friendlyName: 'Auto create table',


  description: '',


  inputs: {
    siteId: {
      type: 'string'
    },
    pageId: {
      type: 'string'
    }
  },


  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[configuratorTable > auto-create-table] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[configuratorTable > auto-create-table] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[configuratorTable > auto-create-table] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[configuratorTable > auto-create-table] Not Found'
    },
    success: {
      statusCode: 200,
      description: '[configuratorTable > auto-create-table] Converted to table successfully'
    }
  },


  fn: async function (inputs, exits) {
    try {
      validateAutoCreateTableRequest(inputs);
      const res = await autoCreateTable(inputs);
      return exits.success(res);
    } catch (error) {
      switch (error.code) {
        case 'E_INPUT_VALIDATION':
          return exits.badRequest({ error: error.message });
        case 'E_SITE_NOT_FOUND':
          return exits.notFound({ error: error.message });
        default:
          return exits.serverError(error);
      }
    }
  }


};
