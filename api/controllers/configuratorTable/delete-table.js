const { deleteTable } = require("../../services/ConfiguratorTable/configuratorTable.service");
const {
  validateTableDeleteRequest,
} = require("../../utils/ConfiguratorTable/inputValidation.utils");

module.exports = {
  friendlyName: "Delete table",
  description: "",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: {
        id: "userId",
        _role: "role",
        _site: "siteId",
      },
    },
    pageId: {
      type: "string",
    },
    tgId: {
      type: "string",
    },
    tableId: {
      type: "string",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[configuratorTable > delete-table] Server Error!",
    },
    badRequest: {
      statusCode: 400,
      responseType: "badRequest",
      description: "[configuratorTable > delete-table] Bad Request!",
    },
    forbidden: {
      statusCode: 403,
      responseType: "forbidden",
      description: "[configuratorTable > delete-table] forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[configuratorTable > delete-table] Not Found",
    },
    success: {
      statusCode: 200,
      description: "[configuratorTable > delete-table] Table deleted successfully",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { _userMeta, ...filteredInputs } = inputs;
      validateTableDeleteRequest(filteredInputs);
      await deleteTable(inputs);
      return exits.success({ message: "Table deleted successfully" });
    } catch (error) {
      switch (error.code) {
        case "E_INPUT_VALIDATION":
        case "E_INVALID_PAGE_TYPE":
          return exits.badRequest({ error: error.message });
        case "E_PAGE_NOT_FOUND":
        case "E_TABLE_GROUP_NOT_FOUND":
        case "E_TABLE_NOT_FOUND":
          return exits.notFound({ error: error.message });
        default:
          return exits.serverError(error);
      }
    }
  },
};
