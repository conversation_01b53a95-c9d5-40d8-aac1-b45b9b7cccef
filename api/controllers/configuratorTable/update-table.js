const ConfiguratorTable = require("../../services/ConfiguratorTable/ConfiguratorTable");

module.exports = {
  friendlyName: "Update configurator table",
  description: "Update configurator table",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: {
        id: "userId",
        _role: "role",
        _site: "siteId",
      },
    },
    tableId: {
      type: "string",
      // Number validation is through Jo<PERSON> for specific err message
    },
    pageId: {
      type: "string",
    },
    tableGroupId: {
      type: "string",
    },
    tableProperty: {
      type: "json",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[configuratorTable > update-configurator-table] Server Error!",
    },
    badRequest: {
      statusCode: 400,
      responseType: "badRequest",
      description: "[configuratorTable > update-configurator-table] Bad Request!",
    },
    forbidden: {
      statusCode: 403,
      responseType: "forbidden",
      description: "[configuratorTable > update-configurator-table] forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[configuratorTable > update-configurator-table] Not Found",
    },
    success: {
      statusCode: 200,
      description: "[configuratorTable > update-configurator-table] Table updated successfully",
    },
  },

  fn: async function ({ tableId, pageId, tableGroupId, tableProperty, _userMeta }, exits) {
    try {
      const configuratorTableInstance = new ConfiguratorTable({ pageId });
      await configuratorTableInstance.setSiteId(pageId);

      const tableData = await configuratorTableInstance.updateTable(
        tableId,
        {
          pageId,
          tableGroupId,
          tableProperty,
        },
        _userMeta.id,
      );
      return exits.success(tableData);
    } catch (err) {
      switch (err.code) {
        case "E_INVALID_NEW_RECORD":
        case "E_INPUT_VALIDATION":
        case "E_INVALID_PAGE":
        case "E_INVALID_PAGE_TYPE":
        case "E_INVALID_TABLE_ROWS":
        case "E_INVALID_TABLE_COLUMNS":
        case "E_INVALID_DEVICE_CLASS":
        case "E_INVALID_PARAMETER":
        case "E_DRIVER_CONFIG_NOT_FOUND":
        case "E_INVALID_TABLE_GROUP":
        case "E_INVALID_PAGE":
        case "E_INVALID_TABLE":
        case "E_INVALID_PAGE_SITE_ID":
          return exits.badRequest({ error: err.message });
        case "E_DRIVER_CONFIG_NOT_FOUND":
          return exits.notFound({ error: err.message });
        default:
          sails.log.error(err);
          return exits.serverError(err);
      }
    }
  },
};
