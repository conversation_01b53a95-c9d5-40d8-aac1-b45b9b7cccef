const ConfiguratorTable = require("../../services/ConfiguratorTable/ConfiguratorTable");

module.exports = {

  friendlyName: 'Fetch configurator table',

  description: 'Fetch configurator table',

  inputs: {
    pageId: {
      type: 'string',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[configuratorTable > fetch-table] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[configuratorTable > fetch-table] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[configuratorTable > fetch-table] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[configuratorTable > fetch-table] Not Found'
    },
    success: {
      statusCode: 200,
      description: '[configuratorTable > fetch-table] Table group fetched successfully'
    }
  },

  fn: async function ({ pageId }, exits) {
    try {
      const configuratorTableInstance = new ConfiguratorTable()
      const tableData = await configuratorTableInstance.fetchPageTableGroups(
        pageId,
      )
      return exits.success(tableData);
    } catch (err) {
      switch (err.code) {
        case 'E_INVALID_PAGE_TYPE':
        case 'E_INVALID_TABLE_GROUP':
        case 'E_INVALID_PAGE':
        case 'E_INPUT_VALIDATION':
          return exits.badRequest({ err: err.message });
        default:
          sails.log.error(err);
          return exits.serverError(err);
      }
    }
  }
};
