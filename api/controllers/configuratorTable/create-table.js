const ConfiguratorTableBuilder = require("../../services/ConfiguratorTable/ConfiguratorTable");

module.exports = {
  friendlyName: "Create configurator table",
  description: "Create configurator table",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: {
        id: "userId",
        _role: "role",
        _site: "siteId",
      },
    },
    pageId: {
      type: "number",
    },
    tableGroupId: {
      type: "number",
    },
    tableProperty: {
      type: "json",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[configuratorTable > create-configurator-main-table] Server Error!",
    },
    badRequest: {
      statusCode: 400,
      responseType: "badRequest",
      description: "[configuratorTable > create-configurator-main-table] Bad Request!",
    },
    forbidden: {
      statusCode: 403,
      responseType: "forbidden",
      description: "[configuratorTable > create-configurator-main-table] forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[configuratorTable > create-configurator-main-table] Not Found",
    },
    success: {
      statusCode: 201,
      description:
        "[configuratorTable > create-configurator-main-table] Table group created successfully",
    },
  },

  fn: async function ({ pageId, tableGroupId, tableProperty, _userMeta: { id: userId } }, exits) {
    try {
      const configuratorTableInstance = new ConfiguratorTableBuilder();
      await configuratorTableInstance.setSiteId(pageId);
      const tableData = await configuratorTableInstance.buildTable({
        pageId,
        tableGroupId,
        tableProperty,
        userId,
      });
      return exits.success(tableData);
    } catch (err) {
      switch (err.code) {
        case "E_INVALID_NEW_RECORD":
        case "E_INPUT_VALIDATION":
        case "E_INVALID_PAGE":
        case "E_INVALID_PAGE_TYPE":
        case "E_INVALID_TABLE_ROWS":
        case "E_INVALID_TABLE_COLUMNS":
        case "E_INVALID_DEVICE_CLASS":
        case "E_INVALID_PARAMETER":
        case "E_INVALID_TABLE_GROUP":
        case "E_INVALID_PAGE_SITE_ID":
          return exits.badRequest({ err: err.message });
        case "E_DRIVER_CONFIG_NOT_FOUND":
          return exits.notFound({ err: err.message });
        default:
          sails.log.error(err);
          return exits.serverError(err);
      }
    }
  },
};
