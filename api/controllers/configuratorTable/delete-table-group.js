const configuratorTableGroup = require("../../services/ConfiguratorTableGroup/configuratorTableGroup.service");
const {  validateRequestParamsDeleteTableGroup } = require("../../utils/ConfiguratorTableGroup/InputValidation");

module.exports = {

  friendlyName: 'Delete configurator table group',

  description: 'Delete configurator table group',

  inputs: {
    _userMeta: {
      type: 'json',
      description: '',
      required: true,
    },
    pageId: {
      type: 'string'
    },
    tableGroupId: {
      type: 'string',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[configuratorTable > delete-configurator-table-accordian-group] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[configuratorTable > delete-configurator-table-accordian-group] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[configuratorTable > delete-configurator-table-accordian-group] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[configuratorTable > delete-configurator-table-accordian-group] Not Found'
    },
    success: {
      statusCode: 200,
      description: '[configuratorTable > delete-configurator-table-accordian-group] Table group created successfully'
    }
  },

  fn: async function (inputs, exits) {
    try {
      const {_userMeta:{id:userId}, ...filteredInputs } = inputs;
      validateRequestParamsDeleteTableGroup(filteredInputs)
      const {pageId, tableGroupId} = inputs
      await configuratorTableGroup.deleteTableGroup(pageId, tableGroupId,userId);
      return exits.success({ tableGroupId , message: `Table group has been successfully deleted`});
    } catch (err) {
      if (err.code == 'E_INPUT_VALIDATION') {
        return exits.badRequest({ err: err.message });
      } else if (err.code == 'E_INVALID_PAGE') {
        return exits.badRequest({ err: err.message });
      } else if (err.code == 'E_INVALID_PAGE_TYPE') {
        return exits.badRequest({ err: err.message });
      } else if (err.code == 'E_INVALID_TABLE_GROUP'){
        return exits.badRequest({ err: err.message });
      }else {
        sails.log.error(err);
        return exits.serverError(err);
      }
    }
  }
};
