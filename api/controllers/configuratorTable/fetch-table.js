const ConfiguratorTable = require("../../services/ConfiguratorTable/ConfiguratorTable");

module.exports = {

  friendlyName: 'Create configurator table',

  description: 'Create configurator table',

  inputs: {
    pageId: {
      type: 'string',
    },
    tableGroupId: {
      type: 'string',
       //Number validation is being validated by JOI for throwing err message
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[configuratorTable > fetch-table] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[configuratorTable > fetch-table] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[configuratorTable > fetch-table] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[configuratorTable > fetch-table] Not Found'
    },
    success: {
      statusCode: 200,
      description: '[configuratorTable > fetch-table] Table group fetched successfully'
    }
  },

  fn: async function ({pageId, tableGroupId}, exits) {
    try {
      const configuratorTableInstance = new ConfiguratorTable()
      await configuratorTableInstance._validateTable(pageId, tableGroupId);
      const mainTableProperty = await configuratorTableInstance.fetchAllTableByTableGroupId(
        tableGroupId,
      )
      return exits.success(mainTableProperty);
    } catch (err) {
      switch (err.code) {
        case 'E_INVALID_NEW_RECORD':
        case 'E_INPUT_VALIDATION':
        case 'E_INVALID_PAGE':
        case 'E_INVALID_PAGE_TYPE':
        case 'E_INVALID_TABLE_ROWS':
        case 'E_INVALID_TABLE_COLUMNS':
        case 'E_INVALID_DEVICE_CLASS':
        case 'E_INVALID_PARAMETER':
        case 'E_DRIVER_CONFIG_NOT_FOUND':
        case 'E_INVALID_TABLE_GROUP':
        case 'E_INVALID_PAGE':
        case 'E_INVALID_PAGE_SITE_ID':
        case 'E_TABLE_NOT_EXIST':
          return exits.badRequest({ err: err.message });
        case 'E_DRIVER_CONFIG_NOT_FOUND':
            return exits.notFound({ err: err.message });
        default:
           sails.log.error(err);
          return exits.serverError(err);
      }
    }
  }
};
