const ConfiguratorTable = require('../../services/ConfiguratorTable/ConfiguratorTable');

module.exports = {

  friendlyName: 'Fetch table details by page ID',

  description: 'Fetch table details by page ID',

  inputs: {
    pageId: {
      type: 'string',
      required: true,
      description: 'The ID of the page to fetch table details for',
    },
    siteId: {
      type: "string",
      required: true,
      example: "aph-ahm",
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[configuratorTable > get-table-details-by-page-id] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[configuratorTable > get-table-details-by-page-id] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[configuratorTable > get-table-details-by-page-id] Forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[configuratorTable > get-table-details-by-page-id] Not Found',
    },
    success: {
      statusCode: 200,
      description: '[configuratorTable > get-table-details-by-page-id] Table group fetched successfully',
    },
  },

  fn: async function ({ pageId }, exits) {
    try {
      const configuratorTableInstance = new ConfiguratorTable();
      const tableDetailsByPageId = await configuratorTableInstance.getTableDetailsByPageId(pageId);
      return exits.success(tableDetailsByPageId);
    } catch (err) {
      switch (err.code) {
        case 'E_INVALID_PAGE_TYPE':
        case 'E_INVALID_TABLE_GROUP':
        case 'E_INVALID_PAGE':
        case 'E_INPUT_VALIDATION':
          return exits.badRequest({ err: err.message });
        default:
          sails.log.error('configuratorTable > get-table-details-by-page-id')
          sails.log.error(err);
          return exits.serverError(err);
      }
    }
  }
};
