const configuratorTableGroupService = require("../../services/ConfiguratorTableGroup/configuratorTableGroup.service");
const {
  validateRequestParamsUpdateTableGroup,
} = require("../../utils/ConfiguratorTableGroup/InputValidation");

module.exports = {
  friendlyName: "Update configurator table group",
  description: "",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: {
        id: "userId",
        _role: "role",
        _site: "siteId",
      },
    },
    pageId: {
      type: "string",
    },
    tableGroupId: {
      type: "string",
    },
    name: {
      type: "ref",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[configuratorTable > update-table-group] Server Error!",
    },
    badRequest: {
      statusCode: 400,
      responseType: "badRequest",
      description: "[configuratorTable > update-table-group] Bad Request!",
    },
    forbidden: {
      statusCode: 403,
      responseType: "forbidden",
      description: "[configuratorTable > update-table-group] forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[configuratorTable > update-table-group] Not Found",
    },
    success: {
      statusCode: 200,
      description:
        "[configuratorTable > update-table-group] Table group title updated successfully",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { _userMeta, ...filteredInputs } = inputs;
      validateRequestParamsUpdateTableGroup(filteredInputs);
      const res = await configuratorTableGroupService.updateTableGroup(inputs);
      return exits.success(res);
    } catch (err) {
      if (err.code == "E_INPUT_VALIDATION" || err.code == "E_TABLE_GROUP_ALREADY_EXISTS") {
        return exits.badRequest({ error: err.message });
      }
      if (err.code == "E_PAGE_NOT_FOUND" || err.code == "E_TABLE_GROUP_NOT_FOUND") {
        return exits.notFound({ error: err.message });
      }
      sails.log.error(err);
      return exits.serverError(err);
    }
  },
};
