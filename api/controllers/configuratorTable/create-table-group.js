const configuratorTableGroup = require("../../services/ConfiguratorTableGroup/configuratorTableGroup.service");
const {
  validateRequestParamsTableGroup,
} = require("../../utils/ConfiguratorTableGroup/InputValidation");

module.exports = {
  friendlyName: "Create configurator table group",
  description: "Create configurator table group",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: {
        id: "userId",
        _role: "role",
        _site: "siteId",
      },
    },
    pageId: {
      type: "string",
    },
    name: {
      type: "string",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[configuratorTable > create-configurator-table-accordian-group] Server Error!",
    },
    badRequest: {
      statusCode: 400,
      responseType: "badRequest",
      description: "[configuratorTable > create-configurator-table-accordian-group] Bad Request!",
    },
    forbidden: {
      statusCode: 403,
      responseType: "forbidden",
      description:
        "[configuratorTable > create-configurator-table-accordian-group] forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[configuratorTable > create-configurator-table-accordian-group] Not Found",
    },
    success: {
      statusCode: 201,
      description:
        "[configuratorTable > create-configurator-table-accordian-group] Table group created successfully",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const {
        _userMeta: { id: userId },
        ...filteredInputs
      } = inputs;
      validateRequestParamsTableGroup(filteredInputs);
      const { name, pageId } = inputs;
      const { id } = await configuratorTableGroup.createNewTableGroup(pageId, name, userId);
      return exits.success({
        tableGroupId: id,
        message: "Table group created successfully",
      });
    } catch (err) {
      if (err.code == "E_INPUT_VALIDATION") {
        return exits.badRequest({ err: err.message });
      } else if (err.code == "E_INVALID_PAGE") {
        return exits.badRequest({ err: err.message });
      } else if (err.code == "E_INVALID_PAGE_TYPE") {
        return exits.badRequest({ err: err.message });
      } else if (err.code == "E_TABLE_GRP_NAME_NOT_UNIQUE") {
        return exits.badRequest({ err: err.message });
      } else {
        sails.log.error(err);
        return exits.serverError(err);
      }
    }
  },
};
