const self = require('../../services/device/device.service');

const siteservice = require('../../services/site/site.public');
const dynamokeystoreservice = require('../../services/dynamokeystore/dynamokeystore.public');

module.exports = {

  friendlyName: 'UpdateMainEnergyMeterList',
  description: 'Update Main Energy meters list from a site.',
  example: [
    `curl -X POST -H "Content-Type:application/json" --data '{"emList":["1100"]}'  0:1337/config/v2/updateem?siteId=demo`,
  ],
  oldroute: 'POST /:siteId/updateEMList',
  inputs: {
    _userMeta: {
      type: 'json',
      example: 'ssh(site Id to get Em of)',
      required: true
    },
    emList: {
      type: ['ref'],
      example: ['2001'],
      required: true
    }
  },

  exits: {
    emptyEMList: {
      description: 'Energy meter list shouldnt be empty.',
      responseType: 'notFound'
    },
    invalidSiteId: {
      description: 'Invalid site id.',
      responseType: 'notFound'
    }
  },

  fn: async function (inputs, exits) {
    let { _userMeta, emList } = inputs;
    let siteId = _userMeta._site;
    try {
      if (emList.length === 0) {
        return exits.emptyEMList();
      }

      if (!siteservice.siteExists(siteId)) {
        return exits.invalidSiteId();
      }

      let response = await dynamokeystoreservice.updateEMList(siteId, emList);
      if (response) {
        return exits.success({ 'err': null, 'status': 'Done' });
      } else {
        return this.res.serverError();
      }

    } catch (e) {
      sails.log.error(e);
      return this.res.serverError();
    }

  }
};

