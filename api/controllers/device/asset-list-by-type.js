const deviceService = require("../../services/device/device.service");
const componentService = require("../../services/component/component.service");
const {validateAssetInputData} = require("../../utils/device/requestInputsValidators.utils");
module.exports = {
    friendlyName: 'getAssetList',
    description: 'get device and component list',
    inputs: {
        _userMeta: {
          type: 'json',
        },
        siteId: {
         type: 'string',
        },
        assetType: {
          type: 'string',
        },
        deviceType: {
          type: 'string',
        },
        driverType: {
          type: 'string',
        }
    },
    exits: {
        badRequest: {
            statusCode: 400,
            description: 'The device does not exist'
        },
        forbidden: {
            statusCode: 402,
            description: 'Forbidden'
        },
        notFound: {
            statusCode: 404,
            description: 'Not Found'
        },
        serverError: {
            statusCode: 500,
            responseType: "serverError",
            description: 'Server Error'
        },
        success: {
            statusCode: 200,
            description: 'data fetched successfully'
        }
    },
    fn:  async function(inputs, exits) {
        try {
          const {
            driverType,
            deviceType,
            assetType,
            siteId
          } = inputs;
          validateAssetInputData(inputs)
          let assetList
          if (driverType && deviceType) {
            if (assetType === 'component') {
              assetList = await  componentService.getComponentListByDriverAndDeviceType(siteId, driverType, deviceType)
            } else {
              assetList = await deviceService.getDeviceListByDriverAndDeviceType(siteId, driverType, deviceType)
            }
          } else {
            if (assetType === 'component') {
              assetList = await  componentService.getComponentListBySiteId(siteId)
            } else if (assetType === 'device') {
              assetList = await deviceService.getDeviceList(siteId)
            }

          }
          return exits.success(assetList);
        } catch(e) {
          if (e.code == 'INPUT_VALIDATION_ERROR') {
            return exits.badRequest({
              err: e.message
            })
          } else {
            sails.logs.info('assetList error: fetch data' + e.message)
            sails.log.error(e);
            return exits.serverError(e.message);
          }
        }
    }
}
