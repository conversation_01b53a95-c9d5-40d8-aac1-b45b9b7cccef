const componentService = require("../../services/component/component.service");
const parameterService = require("../../services/parameter/parameter.public");
const {validateAssetInputParameter} = require("../../utils/device/requestInputsValidators.utils");
module.exports = {
    friendlyName: 'getAssetParameter',
    description: 'get a asset parameter list',
    inputs: {
        _userMeta: {
          type: 'json',
        },
        assetId: {
         type: 'string',
        },
        assetType: {
          type: 'string',
        },
        siteId: {
          type: 'string',
        },
        paramType: {
          type: 'string',
        }
    },
    exits: {
        badRequest: {
            statusCode: 400,
            description: 'The device does not exist'
        },
        forbidden: {
            statusCode: 402,
            description: 'Forbidden'
        },
        notFound: {
            statusCode: 404,
            description: 'Not Found'
        },
        serverError: {
            statusCode: 500,
            responseType: "serverError",
            description: 'Server Error'
        },
        success: {
            statusCode: 200,
            description: 'parameters fetched successfully'
        }
    },
    fn:  async function(inputs, exits) {
        try {
          const {
            assetType,
            assetId,
            siteId,
            paramType
          } = inputs;
          validateAssetInputParameter(inputs)
          let assetData
          if (assetType === 'device') {
            assetData = await parameterService.getDeviceParameter(siteId, assetId)
          } else if (assetType === 'component') {
              assetData = await componentService.getComponentAssetData(assetId, siteId, paramType)
          }
          return exits.success(assetData);
        } catch(e) {
          if (e.code == 'INPUT_VALIDATION_ERROR') {
            return exits.badRequest({
              err: e.message
            })
          } else if (e.code == 'E_COMPONENT_NOT_FOUND') {
            return exits.notFound({
              err: e.message
            })
          } else {
            sails.log.info('Configurator < asset-parameter [server error]')
            sails.log.error(e);
            return exits.serverError(e.message);
          }
        }
    }
}
