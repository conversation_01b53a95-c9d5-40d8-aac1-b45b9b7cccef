const self = require('../../services/device/device.service');

const siteService = require('../../services/site/site.public');
const dynamoKeyStoreService = require('../../services/dynamokeystore/dynamokeystore.public');

const globalHelpers = require('../../utils/globalhelper'); // get global utils
const utils = require('../../utils/device/utils'); // get device comman utils
const selfUtils = require('../../utils/device/create-controller.utils'); // get my utils


module.exports = {

  friendlyName: 'AddNewController',
  description: 'Add new device to database',
  example: [
    `curl -X POST -H "Content-Type: application/json" --data '{"devicesInfo" :[ {"networkId" : "smartjoules-network-0","regionId" : "nzmp","softwareVer" : "test","hardwareVer" : "test","vendorId" : "smartjoules","operationMode" : "test","deviceType" : "joulebox","areaId" : "mqpb","siteId" : "gknmh", "name":"JT-1" }]}'  0:1337/config/v2/controllers`,
  ],
  oldroute: 'POST /v1/devices/controller',

  inputs: {

    devicesInfo: {
      type: ['ref'],
      example: [{ 'networkId': 'id', 'regionId': 'id', }],
      custom: selfUtils.validKeys,
      required: true,
    }
  },

  exits: {
    inValidVendor: {
      responseType: 'badRequest',
      description: 'Not a valid vendor',
    },
    siteDoesnotExist: {
      responseType: 'badRequest',
      description: 'Invalid site id'
    },
    cannotGenerateControllerMap: {
      responseType: 'badRequest',
      description: 'Cannot generate controller Map'
    },
    inValidSiteConfig: {
      responseType: 'badRequest',
      description: 'Device doesnt match site configration'
    }
  },

  fn: async function (inputs, exits) {

    // #IntegrationChanges this api use to take either array of controllers or single controller object.
    // Now it will always take array
    // Parameter is devicesInfo now before was deviceInfo.

    let { devicesInfo } = inputs;

    try {

      if (devicesInfo.filter(selfUtils.isValidVendor).length !== devicesInfo.length) { // all should be valid controllers
        return exits.inValidVendor();
      }

      let { siteId } = devicesInfo[0];

      let [site, devices] = await Promise.all([
        siteService.findOne({ siteId }),
        self.find({ siteId }),
      ]);

      // Check we found data
      if (globalHelpers.isNullishArray([site, devices])) {
        return exits.siteDoesnotExist();
      }

      // filter invalid devices on bases of invalid region/area or networkId
      let validSiteDevices = devicesInfo.filter(device => utils.isValidSiteInfo(site, device));

      if (validSiteDevices.length !== devicesInfo.length) {
        return exits.inValidSiteConfig();
      }

      // Get total count of SJPL controllers configured till now on this site
      let controllerCountMap = utils.getSJPLControllerCount(devices);

      if (!controllerCountMap) {
        return exits.cannotGenerateControllerMap();
      }

      let { regions, networks } = site;

      for (let i = 0; i < devicesInfo.length; i++) {
        let deviceInfo = devicesInfo[i];
        let { deviceType } = deviceInfo;
        let deviceCount = await dynamoKeyStoreService.getDeviceIDFromTotalDeviceCount();
        let newDeviceCountString = String(deviceCount + 1);
        deviceInfo['deviceId'] = newDeviceCountString;
        if ( utils.isValidControllerType(deviceType) ){
          controllerCountMap[deviceType] += 1;
          deviceInfo['name'] = utils.getControllerNameFromCustomNames(controllerCountMap, deviceType);
        }
        regions[deviceInfo.regionId].controller.push(newDeviceCountString);
        networks[deviceInfo.networkId].push(newDeviceCountString);

        // #IDK, WHat moment, here bcz deviceInfo is passed by refrence, ORM auto add values according to schema we have in models.
        await self.create(deviceInfo); // create device,
        await dynamoKeyStoreService.updateTotalDeviceCount(newDeviceCountString); // update device count
        await siteService.update(siteId, {regions, networks});
        // #TodoEventService Send to frontEnd via socket

      }

      await dynamoKeyStoreService.updateConfigTs(siteId); // update dynamoKS config TS
      return exits.success(devicesInfo);



    } catch (e) {
      sails.log.error(e);
      return this.res.serverError();
    }



  }

}
