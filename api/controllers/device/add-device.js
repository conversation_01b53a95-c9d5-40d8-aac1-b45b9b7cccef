const deviceService = require("../../services/device/device.service");
const deviceUtils = require('../../utils/device/add-device.utils');
const parameterService = require('../../services/parameter/parameter.service');
const socketService = require("../../services/socket/socket.public");
const deviceTypeService = require("../../services/devicetype/devicetype.service");
const auditEventLogService = require('../../services/auditEventLog/auditEventLog.public');

module.exports = {
    friendlyName: 'Add Device',
    description: 'add a device to the device list',
    inputs: {
        _userMeta: {
            required: true,
            type: 'json',
        },
        deviceInfo: {
            required: true,
            type: 'json',
            example: {
                    "networkId": "smartjoules-network-0",
                    "regionId": "196c",
                    "siteId": "sjo-del",
                    "portNumber": "12",
                    "controllerId": "latest",
                    "communicationType": "v1",
                    "communicationCategory": "smartjoules",
                    "driverType": "0",
                    "deviceType": "joulesense",
                    "areaId": "ltec",
                    "name": "smartjoules",
                    "isMainMeter": false,
            }
        }
    },
    exits: {
        badRequest: {
            statusCode: 400,
            description: 'The device does not exist'
        },
        forbidden: {
            statusCode: 402,
            description: 'Forbidden'
        },
        notFound: {
            statusCode: 404,
            description: 'Not Found'
        },
        serverError: {
            statusCode: 500,
            responseType: "serverError",
            description: 'Server Error'
        },
        success: {
            statusCode: 201,
            description: 'Device added successfully'
        }
    },
    fn:  async function(inputs, exits) {
        try {
            const { _userMeta:{ id:userId } } = inputs;
            const deviceInfo = deviceUtils.filterKeys(inputs.deviceInfo);
            if (!deviceInfo) {
                return exits.badRequest({ "err": "Insufficient Parameters" })
            }
            const {
                siteId,
                deviceType,
                driverType,
                portNumber,
                communicationType,
                controllerId,
            } = deviceInfo;
            let isEnergyMeter = false;
            let isMainMeter = false

            if (deviceInfo.deviceType === 'em') {
                isEnergyMeter = true
            }
            if(deviceInfo.hasOwnProperty("name")){
              deviceInfo.name = deviceInfo.name.trim();
            }
            if (deviceInfo.hasOwnProperty("isMainMeter") && deviceInfo.isMainMeter) {
                isMainMeter = true;
            }

            const deviceDriver = await deviceTypeService.findOne({ deviceType, driverType });
            const functionType = deviceUtils.getFunctionType(deviceDriver);
            deviceInfo.functionType = functionType;
            // Only add mbBatchReadEnable if it's not null or undefined
            if (deviceDriver.mbBatchReadEnable !== undefined && deviceDriver.mbBatchReadEnable !== null) {
                deviceInfo.mbBatchReadEnable = deviceDriver.mbBatchReadEnable;
            }

            let {
                totalDevicesCount,
                _siteInfo,
                _deviceList,
                _emKeystoreData,
                _mainMeterKeystoreData,
            } =  await deviceService.getDeviceConfiguration(deviceInfo.siteId, isMainMeter, isEnergyMeter)

            /**
             * Generate deviceId and convert its type to string because it will go into
             * transaction items list[string] it will take only string for that
             */
            const deviceIdCounter = deviceUtils.generateDeviceId(totalDevicesCount);
            deviceInfo.deviceId = deviceIdCounter.toString();

            const isAreasRegionsValid = deviceUtils.validateRegionsAndAreas(deviceInfo, _siteInfo)
            if (!isAreasRegionsValid) {
                return exits.badRequest({
                    err: "Invalid regions and areas for device",
                })
            }


            /**
             * In case of NMB slaveID will be zero
             */
            if (communicationType !== 'MB' && communicationType !== "MBIP") {
                deviceInfo.slaveId = '0'
                /**
                 * MB device should have duplicate recent port number
                 * While NMB will have unique port number
                 */
                const portNumberInUse = deviceUtils.isPortNumberInUse(_deviceList, portNumber, controllerId)
                if (portNumberInUse) {
                    return exits.badRequest({
                        err: 'Port number already in use'
                    })
                }
            } else {
                /**
                 * MB device allocate generated slaveID
                 */
                const availableSlaveIds = deviceUtils.getAvailableSlaveIds(_deviceList, deviceInfo)
                if (_.isEmpty(availableSlaveIds)) {
                    return exits.badRequest({err:'No slave id available'});
                }
                if (deviceInfo.slaveId && availableSlaveIds.indexOf(deviceInfo.slaveId) === -1) {
                    return exits.badRequest({
                        "err": "Slave id already exists",
                        "availableSlaves": availableSlaveIds,
                    })
                }
                if (!deviceInfo.slaveId) {
                    deviceInfo.slaveId = availableSlaveIds[0];
                }
            }
            if(communicationType != "MBIP"){ // Parameters are created via bulk configuration in case of MBIP Devices
                await parameterService.addDeviceParameters(
                    deviceType,
                    driverType,
                    deviceInfo.deviceId,
                    siteId
                );
            }

            /**
             * Get _em and _mainMeter keyStore data with appended deviceId in list
             */
              const addDeviceToDynamoKeyStore = deviceUtils.dynamoKeystoreKey(
                    _emKeystoreData,
                    _mainMeterKeystoreData,
                    {
                        deviceId: deviceInfo.deviceId,
                        isMainMeter,
                        siteId,
                        isEnergyMeter,
                    },
                );
            const addDeviceParameters = deviceUtils.generateAddDeviceParameters(deviceInfo)
            await deviceService.addDevice({
                deviceId: deviceInfo.deviceId,
                siteId: deviceInfo.siteId,
            }, addDeviceParameters, addDeviceToDynamoKeyStore);
            await deviceService.removeCachedDeviceCategories(siteId)
            await socketService.notifyJouleTrackPublicRoom(siteId, 'devices', {
                event: 'create',
                data: [deviceInfo]
            });

          const auditPayload = {
            event_name: "state_create_device",
            user_id: userId,
            site_id: siteId,
            asset_id: deviceInfo.deviceId,
            req: this.req,
            prev_state: null,
            curr_state: deviceInfo,
          };

          auditEventLogService.emit(auditPayload);

            return exits.success(deviceInfo);
        } catch(e) {
            switch(e.code) {
                case 'E_NOT_FOUND': {
                    return exits.notFound({err: e.message})
                }
                case 'E_BAD_REQUEST': {
                    return exits.badRequest({err: e.message})
                }
                default: {
                    sails.log.error(e);
                    return exits.serverError(e.message);
                }
            }
        }
    }
}
