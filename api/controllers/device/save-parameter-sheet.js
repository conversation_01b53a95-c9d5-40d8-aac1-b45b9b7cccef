
const deviceService = require('../../services/device/device.service');
const deviceTypeService = require('../../services/devicetype/devicetype.service.js');
const globalHelper = require('../../utils/globalhelper');
const selfUtils = require('../../utils/device/save-parameter-sheet.util.js');
const utils = require('../../utils/device/utils.js');
const SheetService = require("../../services/google/bulk-configuration.service.js");
const parameterService = require('../../services/parameter/parameter.service');

module.exports = {
  friendlyName: 'saveParameterSheet',
  description : '',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    sheetId: {
      type: 'string',
      required: false,
      example: '1nJT92Hv1ee6JGDgGSEDMHEGX2OW3D7sigxA37PdHi20',
      description: 'sheetId of the Google Sheet used to fetch parameter values.',
    },
    deviceId: {
      type: 'string',
      required: true,
      example: '100573',
      description: 'deviceId of the device whose parmeters need to be generated.',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[device > saveParameterSheet] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[device > saveParameterSheet] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[device > saveParameterSheet] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      const { sheetId: sheetIdInBodyParams, deviceId } = inputs;
      let sheetId;

      // Fetching device configuration and initialising sheetId.
      const deviceConfiguration = await deviceService.findOne({ deviceId });
      if (!deviceConfiguration){
        return exits.badRequest({
          code: "E_DEVICE_NOT_FOUND",
          problems: ["Device not found"]
        });
      }
      if (sheetIdInBodyParams) sheetId = sheetIdInBodyParams;
      else if(deviceConfiguration.sheetId) sheetId = deviceConfiguration.sheetId;
      else {
        return exits.badRequest({
          code: "E_SHEET_INVALID",
          problems: ["SheetId not found in device configuration."]
        });
      }

      const { siteId, deviceType, driverType, maxAssetId } = deviceConfiguration;
      const $existingParametersInDb = parameterService.findWithoutAbbr(siteId, deviceId);
      const $driverConfig = deviceTypeService.findOne({ deviceType, driverType });
      let sheet, rows;
      try {
        sheet = new SheetService(sheetId);
        await sheet.init();
        rows = await sheet.getAllRows();
      } catch (error) {
        sails.log.error("Error reading data from google sheet!");
        sails.log.error(error);
        return exits.badRequest({
          code: "E_SHEET_INVALID",
          problems: ["Unable to read from Google Sheet"]
        });
      }

      const existingParametersInDb = await $existingParametersInDb;
      const driverConfig = await $driverConfig;
      let inputCheck = selfUtils.checkInput(rows, driverConfig, existingParametersInDb, deviceConfiguration);
      if(!inputCheck.status) return exits.badRequest({
        code: "E_INPUT_CHECK_FAILURE",
        problems: inputCheck.errors
      });

      // Deleting and creating parameters based on input checks
      const { parametersToBeCreated, parametersToBeDeleted } = inputCheck;
      let $deletedParameterQueries;
      if(parametersToBeDeleted.length != 0)
        $deletedParameterQueries = parameterService.delete(inputCheck.parametersToBeDeleted);
      if(parametersToBeCreated.length != 0)
        await parameterService.addDeviceParametersWithCustomValues(deviceType, driverType, deviceId, siteId, driverConfig, inputCheck.parametersToBeCreated);
      await $deletedParameterQueries;

      // Fetching all current parameter configurations
      const $modifiedParametersInDb = parameterService.findWithoutAbbr(siteId, deviceId);

      // // Saving maxAssetId in device configuration
      const { newMaxAssetId } = inputCheck;
      if(maxAssetId != newMaxAssetId)
        await deviceService.update({ deviceId, siteId }, { maxAssetId: newMaxAssetId });

      const modifiedParametersInDb = await $modifiedParametersInDb;
      return exits.success(modifiedParametersInDb);
    } catch(error) {
      sails.log.error('[device > saveParameterSheet] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
