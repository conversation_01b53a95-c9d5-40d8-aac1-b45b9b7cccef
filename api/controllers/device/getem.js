const self = require('../../services/device/device.service');

module.exports = {

  friendlyName: 'GetMainEnergyMeterList',
  description: 'Get Main Energy meters list from a site.',
  example: [
    `curl -X GET 0:1337/config/v2/getEM?siteId=demo`,
  ],
  oldroute: 'GET /:siteId/getEMList',
  inputs: {
    _userMeta: {
      type: 'string',
      example: 'ssh(site Id to get Em of)',
      required: true
    }

  },

  exits: {

  },

  fn: async function (inputs, exits) {
    let siteId = inputs._userMeta._site;
    try {
      let device = await self.getEM(siteId);
      let devivceList = device && device.list ? device.list : [];
      return exits.success(devivceList);
    } catch (e) {
      sails.log.error(e);
      return this.res.serverError();
    }

  }
};

