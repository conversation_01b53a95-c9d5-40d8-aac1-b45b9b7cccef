// const es = require('../../services/event/event.service');

const siteService = require('../../services/site/site.public');
module.exports = {

  friendlyName: 'GetMainEnergyMeterList',
  description: 'Get Main Energy meters list from a site.',
  example: [
    `curl -X GET 0:1337/config/v2/getEM?siteId=demo`,
  ],
  inputs: {
    test : {
      type: 'string',
    }

  },

  exits: {

  },

  fn: async function (inputs, exits) {

    try{

      await siteService.findOne({'whrtte':1});

    }catch(e){
      console.log(e);
      return ;
    }

  }
};

