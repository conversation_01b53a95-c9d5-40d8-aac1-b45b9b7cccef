
const deviceService = require('../../services/device/device.service');
const globalHelper = require('../../utils/globalhelper');
const util = require('../../utils/device/update-device-version.util.js');

module.exports = {
  friendlyName: 'updateDeviceVersion',
  description: 'update each device on the given site to new software version',
  example: [
    `curl -X GET "http://localhost:1337/[UNKNOWN]?siteId=testsite&currentVersion=1.3.6`,
  ],

  inputs: {
    siteId: {
      type: 'string',
      example: 'Id of site',
      required: true,
    },
    currentVersion: {
      type: 'string',
      example: '1.2',
      required: true

    }
  },

  exits: {
    siteNotFound: {

    }
  },

  fn: async function (inputs, exits) {
    try {
      let { siteId, currentVersion } = inputs;

      let devices = deviceService.find({ siteId });

      if(!devices){
        return exits.siteNotFound();
      }

      let $devicesUpdateList = devices.map(device => deviceService.update({deviceId: device.deviceId, siteId}, {currentVersion}) );

      await Promise.all($devicesUpdateList);

      return exits.success({ 'err': null, 'status': 'Done' });



    } catch (error) {
      sails.log.error('[device > updateDeviceVersion] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
