const self = require('../../services/device/device.service');

const siteservice = require('../../services/site/site.public');
const dynamokeystoreservice = require('../../services/dynamokeystore/dynamokeystore.public');
const parameterservice = require('../../services/parameter/parameter.public');
const devicetypeservice = require('../../services/devicetype/devicetype.public');
const sockerService = require('../../services/socket/socket.public');

const globalHelpers = require('../../utils/globalhelper');
const utils = require('../../utils/device/utils');
const selfUtils = require('../../utils/device/create-device.utils');

module.exports = {

  friendlyName: 'AddNewDevice',
  description: 'Adds the device configuration to device table and trigger corresponding hooks like incrementing deviceId count in dyanmoKeyStore',
  example: [
    `curl -X POST -H "Content-Type: application/json" --data '{"devicesInfo" : {"networkId" : "smartjoules-network-0","regionId" : "nzmp","softwareVer" : "test","hardwareVer" : "test","vendorId" : "smartjoules","operationMode" : "test","deviceType" : "joulebox","areaId" : "mqpb","siteId" : "gknmh", "name":"MyMyMy","portNumber":"2","controllerId":"2770","communicationType":"NMB","communicationCategory":"DR","driverType":"-1","deviceType":"relay"} }'  0:1337/config/v2/device`, // NMB,
    `curl -X POST -H "Content-Type: application/json" --data '{"deviceInfo" : {"networkId" : "smartjoules-network-0","regionId" : "nzmp","softwareVer" : "test","hardwareVer" : "test","vendorId" : "smartjoules","operationMode" : "test","deviceType" : "joulebox","areaId" : "mqpb","siteId" : "gknmh", "name":"kyabaathai","portNumber":"3","controllerId":"2770","communicationType":"MB","communicationCategory":"VFD","driverType":"-1","deviceType":"vfd"} }' 0:1337/config/v2/device`, // MB

  ],
  oldroute: 'POST /v1/devices',

  inputs: {
    deviceInfo: {
      type: 'json',
      example: { networkId: 'networkId', regionId: 'regionId', portNumber: '' },
      custom: selfUtils.validKeys,
      required: true
    },
    _userMeta: {
      type: {},
      example: { 'id': 'userName' },
      required: true // this fields auto add authentication
    },

  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[auth > create-device] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[auth > create-device] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[auth > create-device] Forbidden request',
    }
  },

  fn: async function (inputs, exits) {
    try {
      let { deviceInfo } = inputs;
      let siteId = inputs._userMeta._site;

      deviceInfo = globalHelpers.removeNullValueFromObject(deviceInfo);

      let { communicationCategory, portNumber, communicationType,
        controllerId, deviceType, driverType } = deviceInfo;
      controllerId = String(controllerId);

      let [site, deviceIdCount] = await Promise.all([
        siteservice.findOne({ siteId }),
        dynamokeystoreservice.getDeviceIDFromTotalDeviceCount()
      ]);
      if (!site) exits.badRequest({ problems: ['Site id donot exist'] });
      if (!utils.isValidSiteInfo(site, deviceInfo)) {
        return exits.forbidden({ problems: ['Invalid values for regions and areas'] });
      }

      deviceInfo.deviceId = deviceIdCount ? String(deviceIdCount + 1) : '1';
      deviceInfo.functionType = utils.getDeviceExecutionType(communicationCategory);


      let _deviceType = await devicetypeservice.findOne({ deviceType, driverType });
      if (!_deviceType) {
        return exits.badRequest({ problems: ['Device driver doesnt exist'] });
      }
      let devices = await self.find({ siteId });
      // filtering only devices which is attached to this controller
      devices = selfUtils.getDevicesAttachedToController(devices, controllerId);

      let paramList = utils.addDeviceParameters(deviceInfo.deviceId, _deviceType, siteId); // add some field to parameters object
      if (!paramList) {
        return exits.badRequest({ problems: ['ERROR:driver config'] });
      }
      let portInUse = selfUtils.isValidPortNumber(devices, portNumber);
      if (portInUse) {
        return exits.forbidden({ problems: ['Port is already occupied'] });
      }

      if (communicationType === 'MB') {
        // get an valid slave Id in case of Modbus device from available slave id

        let slaveId = deviceInfo.slaveId;
        let availableSlaveIds = utils.getAvailableSlaveIds(devices);
        if (availableSlaveIds.length === 0) return exits.forbidden({ problems: ['This controller is full! Please onto other controller'] });
        if (slaveId && availableSlaveIds.indexOf(slaveId) === -1) {
          return exits.badRequest({ problems: ['No available slave id'] });
        } else {
          deviceInfo.slaveId = availableSlaveIds[0]; // get 1st available slave
        }
      } else {
        // in case of nonModbus , slave id doesnt exist
        deviceInfo.slaveId = '0';
      }

      // #TBD when we have cache service, use a FLAG to check if we can update. DynamoKeyStore or not to avoid RACE Condition.
      let frontEndResponse = { ...deviceInfo };
      await self.create(deviceInfo); // Create new device
      await parameterservice.create(paramList); // add parameter's to parameter table

      await dynamokeystoreservice.update({ key: 'totalDeviceCount' }, { value: deviceInfo.deviceId }); // update total device count
      await dynamokeystoreservice.updateConfigTs(siteId); // update timestamp of last edit of config on this site.

      await sockerService.notifyJouleTrackPublicRoom(
        siteId, 'devices', { event: 'create', data: deviceInfo }
      ); // notify jouletrack

      return exits.success(frontEndResponse);

    } catch (e) {
      sails.log.error(e);
      return this.res.serverError();
    }

  }
};

