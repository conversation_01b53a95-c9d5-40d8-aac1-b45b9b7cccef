const globalHelper = require('../../utils/globalhelper');
const deviceTypeservice = require('../../services/devicetype/devicetype.service');


module.exports = {
  friendlyName: 'findDriver',
  description : 'to get driverfile',
  example: [
    `curl localhost:1337/m2/device/v2/driver -X POST -H "Content-Type: application/json" --data '{"deviceType":"chiller", "driverType": "0"}' `,
  ],

  inputs: {
    deviceType: {
      type: 'string',
      description: 'type of device',
      example: 'chiller',
      required: true
    },
    driverType: {
      type: 'string',
      example: '0',
      description: 'Unique drivertype for each devicetype',
      required: true
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[device > findDriver] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[device > findDriver] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[device > findDriver] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      let { deviceType, driverType } = inputs;
      let driver = await deviceTypeservice.findOne({ deviceType, driverType });

      if(driver === undefined){
        return exits.badRequest({ problems: ['Driver doesnot exists'] });
      }
      driver= globalHelper.toJson(driver);
      return exits.success(driver);

    } catch(error) {
      sails.log.error('[device > findDriver] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
