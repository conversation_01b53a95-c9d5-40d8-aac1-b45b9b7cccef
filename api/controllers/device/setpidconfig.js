const self = require('../../services/device/device.service');

const gloablhelpers = require('../../utils/globalhelper');


module.exports = {

  friendlyName: 'SetPidConfig',
  description: 'This route just send the new config to controller. Adding new config to our db is done on OK response from controller',
  example: [
    ``,
  ],
  oldroute: 'POST /pid/config',
  inputs: {
    siteId: {
      type: 'string',
      example: 'ssh(site Id to get Em of)',
      required: true
    },
    controllerId: {
      type: 'string',
      example: '3223',
      required : true
    },
    componentId: {
      type: 'string',
      example: 'ssh_23',
      required : true
    },
    config: {
      type: {},
      example: {'test' : 'test'},
      required: true
    },
    _userMeta: {
      type: {},
      example: { 'id': 'userName' },
      required: true // this fields auto add authentication
    },

  },

  exits: {
    notFound: {
      description: 'No user with the specified ID was found in the database.',
      responseType: 'notFound'
    }
  },

  fn: async function (inputs, exits) {
    let { config, componentId, controllerId, siteId } = inputs;
    let userId = inputs.id;
    try {

      let publishObject = {
        config,
        operation: 'pidConfig',
        ts: gloablhelpers.getCurrentUnixTs(),
        extra: {
          siteId,
          componentId,
          controlId: controllerId,
          userId
        }
      };
      let publishTopic = `${siteId}/config/${controllerId}/pid`;
      eventService.publish(publishTopic, JSON.stringify(publishObject));

      return exits.success('ok');
    } catch (e) {
      sails.log.error(e);
      return this.res.serverError();
    }

  }
};

