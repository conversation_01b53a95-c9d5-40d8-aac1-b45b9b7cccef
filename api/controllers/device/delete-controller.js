const deviceService = require('../../services/device/device.service');
const _ = require('lodash');
const inputValidation = require('../../utils/device/inputValidation');

module.exports = {
  friendlyName: 'delete-controller',
  description: 'Delete a controller',
  inputs: {
    controllerId: {
      type: 'string'
    },
    siteId: {
      type: 'string'
    },
    industryType: {
      type: 'string'
    }
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[config > deleteController] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[config > deleteController] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[config > deleteController] forbidden'
    },
    notFound: {
      statusCode: 404,
      description: '[config > deleteController] Not Found!',
  },
  unableToDeleteController: {
    statusCode: 409,
    description: '[config > deleteController] Unable to Delete Controller'
  }
  },
  fn: async function(inputs, exits) {
    let resObj = {
      deleted: [],
      notDeleted: [],
      err: [],
    };
    let { controllerId, siteId, industryType } = inputs
    try {
      inputValidation.deleteController(inputs)
      await deviceService.deleteControllerIfNoDeviceAttached(siteId, controllerId, industryType);
      resObj.deleted.push(controllerId)
      return exits.success(resObj);
    } catch(e) {
      switch(e.code) {

        case 'E_UNABLE_TO_DELETE_CONTROLLER': {
          return exits.unableToDeleteController({
            err: e.message
          })
        }
        case 'E_DEVICE_ATTACHED_TO_CONTROLLER': {
          const {devicesFound, message} = e;
          resObj.notDeleted.push(...devicesFound)
          resObj.err.push(message)
          return exits.badRequest(resObj)
        }
        case 'E_CONTROLLER_COMPONENT_EXIST': {
          const {devicesFound, message} = e;
          resObj.notDeleted.push(...devicesFound)
          resObj.err.push(message)
          return exits.badRequest(resObj)
        }
        case 'E_SITE_NOT_FOUND': {
          return exits.notFound({
            err: e.message
          })
        }
        case 'E_INPUT_VALIDATION': {
          return exits.badRequest({
            err: e.message
          })
        }
        case 'E_DEVICE_NOT_EXIST': {
          return exits.notFound({
            err: e.message
          })
        }
        case 'E_CONTROLLER_CATEGORY_MISSING': {
          return exits.notFound({
            err: e.message
          })
        }
        case 'E_CONTROLLER_NOT_FOUND': {
          return exits.notFound({
            err: e.message
          })
        }
        case 'E_REMOVE_CONTROLLER_FROM_SITE': {
          return exits.unableToDeleteController({
            err: e.message
          })
        }
        default: {
          return exits.serverError(e)
        }
    }
  }
}
}
