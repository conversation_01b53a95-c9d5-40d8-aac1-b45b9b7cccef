const self = require('../../services/device/device.service');

const componentservice = require('../../services/component/component.public');
const dynamoservice = require('../../services/dynamokeystore/dynamokeystore.public');
const siteservice = require('../../services/site/site.service');
const devicetypeservice = require('../../services/devicetype/devicetype.public');

module.exports = {

  friendlyName: 'Initializecontroller',
  description: 'Initialize controller.',
  example: [
    `curl -X POST --data "cntrlId=3323&publish=false" 0:1337/v2/config/controller/init`,
  ],
  oldroute: 'GET /v1/devices/configureController',

  inputs: {
    cntrlId: {
      type: 'string',
      example: '2771(controller Id to initiliaze)',
      required: true
    },
    publish: {
      type: 'boolean',
      example: true,
      required: true
    }
  },
  exits: {
    notFoundDevice: {
      description: 'No device with the specified ID was found in the database.',
      responseType: 'notFound'
    },
    invalidDeviceType: {
      description: 'No device with the specified ID was found in the database.',
      responseType: 'notFound'
    }
  },

  fn: async function (inputs, exits) {
    let { cntrlId, publish } = inputs;
    try {

      let device = await self.findOne({ 'deviceId': cntrlId });
      let timestamp;

      if (!device) {
        return exits.notFoundDevice();
      }
      if (!self.isValidControllerType(device.deviceType)) {
        return exits.invalidDeviceType();
      }
      let siteId = device.siteId;
      let [devices, site, params, components, ts] = await Promise.all([
        self.find({ siteId }),
        siteservice.findOne({ siteId }),
        devicetypeservice.find({}), // how to get this
        componentservice.find({ siteId }),
        dynamoservice.findOne({ key: `${siteId}_configTS` })
      ]);

      try {
        timestamp = ts && ts.value ? ts.value : await dynamoservice.updateConfigTs(siteId);
      } catch (e) {
        sails.log.error(e);
        return this.res.serverError('Dynamo service is unaccessible');
      }

      if (publish === true) {
        let payload = {
          'timestamp': timestamp,
          'operation': 'verifyConfigVer',
        };
        // HAVE TO WRITE EVENT SERVICE
        eventService.publish(`${siteId}/config/all/timestamp`, payload);

      }

      return exits.success({
        components,
        devices,
        site,
        params,
        timestamp,
        self: device
      });

    } catch (e) {
      sails.log.error(e);
      return this.res.serverError();
    }

  }
};

