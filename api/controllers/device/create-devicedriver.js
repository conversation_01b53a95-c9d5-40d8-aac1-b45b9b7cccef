
const configService = require('../../services/device/device.service');
const globalHelper = require('../../utils/globalhelper');
const selfUtils = require('../../utils/device/create-devicedriver.util');
const utils = require('../../utils/device/utils');

const deviceTypeService = require('../../services/devicetype/devicetype.service');

module.exports = {
  friendlyName: 'createDevicedriver',
  description: 'create a new device driver',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    driver: {
      type: 'json',
      example: { class: 'devices', communicationCategory: 'ACI', communicationType: 'NMB' },
      // custom: selfUtils.checkInput,
      required: true

    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[config > createDevicedriver] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[config > createDevicedriver] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[config > createDevicedriver] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {

    try {
      let { driver } = inputs;
      let {
        communicationCategory,
        communicationType,
        deviceType,
        driverName,
        driverType,
        parameters
      } = driver;
      let driverClass = driver['class'];

      if (!utils.isValidDriverClass(driverClass)) {
        return exits.badRequest({ problems: ['Invalid driver class'] });
      }
      if (!utils.isValidCommunicationCategory(communicationCategory)) {
        return exits.badRequest({ problems: ['Invalid Communication category'] });
      }
      if (!utils.isValidCommunicationType(communicationType)) {
        return exits.badRequest({ problems: ['Invalid Communication type'] });
      }
      if (!Array.isArray(parameters)) {
        return exits.badRequest({ problems: ['Parameters should array'] });
      }
      parameters = parameters.map(globalHelper.toJson);
      for (let parameterIndex in parameters) {
        let parameter = parameters[parameterIndex];
        if (!selfUtils.isValidDeviceParameter(parameter)) {
          let showVal = JSON.stringify(parameter);
          return exits.badRequest({ problems: [`Not a valid parameter ${showVal} `] });
        }
        parameters[parameterIndex] = JSON.stringify(parameter); // #TODO we dont need to stringify in new adapter
      }
      driver.parameters = parameters; // #TODO remove after shifting from above thing

      if (typeof driverName !== 'string') {
        return exits.badRequest({ problems: ['Driver name should be string'] });
      }
      let deviceDriver = await deviceTypeService.findOne({ deviceType, driverType });
      if (deviceDriver) {
        return exits.forbidden({ problems: ['Device driver already exists'] });
      }
      try {
        // await deviceTypeService.create(driver);
      } catch (e) {
        sails.log.error(e);
        return exits.badRequest({ problems: ['Unable to add new driver to database'] });
      }
      return exits.success(driver);
    } catch (error) {
      sails.log.error('[config > createDevicedriver] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
