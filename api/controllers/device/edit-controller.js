const self = require('../../services/device/device.service');

const siteservice = require('../../services/site/site.public');
const dynamokeystoreservice = require('../../services/dynamokeystore/dynamokeystore.public');

const selfUtils = require('../../utils/device/edit-controller.utils');

module.exports = {

  friendlyName: 'updateControllerConfig',
  description: 'Update ',
  example: [
    `curl -X PUT -H "Content-Type: application/json" --data '{ "deviceConfig": {"deviceId" : "1134"}}' 0:1337/config/v2/controller `,
    `curl -X PUT -H "Content-Type: application/json" --data '{ "deviceConfig"{"deviceId" : "2175","communicationType":"NMB","regionId":"glok","areaId":"hwcw"}}' 0:1337/config/v2/controller `
  ], // 1st simple device update, 2nd update region in device too
  oldroute: 'PUT /v1/devices/controller',

  inputs: {

    deviceConfig: {
      type: {},
      example: { 'networkId': 'id', 'regionId': 'id' },
      custom: selfUtils.isValidFrontEndRequest,
      required: true
    },

    _userMeta: {
      type: {},
      example: { 'id': 'userName' },
      // required: true // this fields auto add authentication
    },

  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[auth > edit-controller] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[auth > edit-controller] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[auth > edit-controller] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    let { deviceConfig } = inputs;
    try {
      let { deviceId } = deviceConfig;
      let device = await self.findOne({ deviceId });
      let { siteId } = device;

      if (!device) {
        return exits.forbidden({'problems': ['Device doesnt exits']});
      }


      let validNameChangeCondition = selfUtils.isValidDeviceChangeNameCondition(device.deviceType, deviceConfig.name);
      if (!validNameChangeCondition) {
        return exits.badRequest({'problems': ['Not a valid device name change condition']});
      }

      // Do frontEnd request says we need to change region in which controller is?
      // If so we need to also change it in site table, #ToReviewAmit `I dont think we can change area, then why the check`
      let { areaId, regionId } = deviceConfig;

      if (regionId && areaId) {
        let updateRegionResponse = await siteservice.updateControllersRegion(siteId, deviceId, device.regionId, regionId);
        if (!updateRegionResponse){
          return exits.forbidden({'problems': ['unable To Update Region']});
        }
      }
      await self.update({deviceId, siteId}, deviceConfig);

      // #TodoEventService, send event of done
      await dynamokeystoreservice.updateConfigTs(siteId);

      return exits.success('ok');

    } catch (e) {
      sails.log.error(e);
      return this.res.serverError();
    }

  }
};

