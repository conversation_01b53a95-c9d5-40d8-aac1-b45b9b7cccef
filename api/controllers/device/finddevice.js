const self = require('../../services/device/device.service');



module.exports = {

  friendlyName: 'findDevice',
  description : 'Find a Device with filter options as REST.',
  example: [
    `curl -X GET "0:1337/v2/config/devices?siteId=ssh&deviceId=1134"`,
  ],

  inputs: {
    deviceId: {
      type: 'string',
      example: 'Id of device',
      required: true
    },
    siteId: {
      type: 'string',
      example: 'Id of site',
      required: true
    }
  },

  fn: async function (inputs, exits) {
    let { siteId, deviceId } = inputs;
    try{
      let device = await self.find({deviceId, siteId});
      return exits.success(device);
    }catch(e){
      sails.log.error(e);
      return this.res.serverError();
    }

  }
};

