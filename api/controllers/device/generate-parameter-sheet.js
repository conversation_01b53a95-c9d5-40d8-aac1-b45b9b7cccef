
const deviceService = require('../../services/device/device.service');
const parameterService = require('../../services/parameter/parameter.service.js');
const deviceTypeService = require('../../services/devicetype/devicetype.service.js');
const selfUtils = require('../../utils/device/generate-parameter-sheet.util.js');
const SheetService = require("../../services/google/bulk-configuration.service.js");
const routeService = require("../../services/device/generate-parameter-sheet.service");

module.exports = {
  friendlyName: 'generateParameterSheet',
  description : '',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    deviceId: {
      type: 'string',
      required: true,
      example: '100573',
      description: 'deviceId of the device whose parmeters need to be generated.',
    },
    numberOfNewParams: {
      type: 'number',
      required: false,
      example: 5,
      description: 'Number of sets of parameters that need to be added to the google sheet.',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[device > generateParameterSheet] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[device > generateParameterSheet] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[device > generateParameterSheet] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      const { deviceId, numberOfNewParams } = inputs;
      const systemId = "5"; // Hardcoded for Sunshine iBMS. System Id corresponds to Fire Alarm System.

      const deviceConfiguration = await deviceService.findOne({ deviceId });
      if (!deviceConfiguration){
        return exits.badRequest({
          code: "E_DEVICE_NOT_FOUND",
          problems: ["Device not found"]
        });
      }
      const { siteId, deviceType, driverType, sheetId, maxAssetId } = deviceConfiguration;
      const $existingDeviceParameters = parameterService.findWithoutAbbr(siteId, deviceId);
      const $driverConfig = deviceTypeService.findOne({ deviceType, driverType });
      const $layerInfo = routeService.fetchLayerNamesToIdMap(systemId, siteId);

      const existingDeviceParameters = await $existingDeviceParameters;
      const driverConfig = await $driverConfig;
      if (!driverConfig){
        return exits.badRequest({
          code: "E_DRIVER_NOT_FOUND",
          problems: ["Driver not found"]
        });
      }
      const $componentIdToLayerNameMap = routeService.fetchLayerInformationForComponentIds(existingDeviceParameters);
      const componentIdToNameMap = await routeService.fetchComponentNamesForComponentIds(existingDeviceParameters);
      const componentIdToLayerNameMap = await $componentIdToLayerNameMap;
      const layerInfo = await $layerInfo;
      const filteredFields = selfUtils.filterSheetFields(existingDeviceParameters, driverConfig, numberOfNewParams, componentIdToNameMap, componentIdToLayerNameMap, maxAssetId);
      // globalHelper.saveMockData(filteredFields, "filteredFields");

      // Initialising Default sheet
      let sheet = new SheetService(sheetId);
      await sheet.init(deviceId);
      await sheet.clearSheet();

      // Populating user facing sheet from existing parameter configuration.
      const headerValues = sheet.returnParameterSheetHeaders();
      await sheet.setHeaderValues(headerValues);
      const sortedRows = selfUtils.sortRowsByAssetId(filteredFields);
      // Populating drop down elements for Component Hierarchy
      await sheet.populateDropDownListForLayerNames(layerInfo, sortedRows);
      await sheet.addRows(sortedRows);


      // Popluating hidden list with the layerName -> layerId information
      await sheet.populateLayerInfoSheet(layerInfo);

      const URL = sheet.generateURL();
      const isNewSheet = sheet.isNewSheet();
      if(isNewSheet){
        await deviceService.update({ deviceId, siteId }, { sheetId: sheet.sheetId });
      }
      return exits.success({
        "status": true,
        URL,
      });
    } catch(error) {
      sails.log.error('[device > generateParameterSheet] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
