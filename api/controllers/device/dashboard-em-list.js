const dynamokeystorePublic = require('../../services/dynamokeystore/dynamokeystore.public');

module.exports = {
  friendlyName: 'dashboard-em-list',
  description: 'Fetch the EM list for the dashboard based on site ID',
  inputs: {
    siteId: {
      type: 'string',
      description: 'The site ID to fetch the EM list for'
    },
  },
  exits: {
    badRequest: {
      statusCode: 400,
      description: 'Missing or invalid siteId',
    },
    success: {
      statusCode: 200,
      description: 'Successfully fetched EM list',
    },
    serverError: {
      statusCode: 500,
      description: 'Server error occurred',
    },
  },
  fn: async function (inputs, exits) {
    try {
      const { siteId } = inputs;

      if (!siteId) {
        return exits.badRequest({ err: 'Site ID is required' });
      }

      const emList = await dynamokeystorePublic.fetchEmList(siteId);

      return exits.success({ data: emList });
    } catch (e) {
      sails.log.error("[device >> dashboard-em-list]", e);
      return exits.serverError(e);
    }
  }
}