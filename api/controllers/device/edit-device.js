const self = require('../../services/device/device.service');
const dynamokeystoreservice = require('../../services/dynamokeystore/dynamokeystore.public');
const devicetypeservice = require('../../services/devicetype/devicetype.public');
const parameterservice = require('../../services/parameter/parameter.public');
const utils = require('../../utils/device/utils');
const selfUtils = require('../../utils/device/edit-device.utils');
const globalHelpers = require('../../utils/globalhelper');
const deviceService = require('../../services/device/device.service');
const { notifyJouleTrackPublicRoom } = require('../../services/socket/socket.service');
const deviceUtils = require('../../utils/device/add-device.utils');

module.exports = {

  friendlyName: 'editDeviceConfigration',
  description: 'Edit single device configration[Cannot update siteId/deviceId]',
  example: [
    `curl -X PUT -H "Content-Type: application/json" --data '{"devicesInfo" : {"deviceId":"100100","name":"tester"} }' 0:1337/config/v2/device`, // simple request
    // ``, //update isMainMeter true TEST-CASE-1
    // `` // update isMainMeter false TEST-CASE-2
    // `` // update driverType TEST-CASE-3
  ],
  oldroute: 'PUT /v1/devices',

  inputs: {
    deviceConfig: {
      type: {},
      example: { 'networkId': 'id', 'regionId': 'id' },
      custom: selfUtils.isValidFrontEndRequest,
      required: true
    },
    deviceId: {
      type: 'string',
      required: true,
      description: 'The unique identifier for the component to be deleted'
    },
    siteId: {
      type: 'string'
    },
    _userMeta: {
      type: {},
      example: { 'id': 'userName' },
      // required: true // this fields auto add authentication
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[auth > edit-device] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[auth > edit-device] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[auth > edit-device] forbidden Request!',
    }
  },

  fn: async function (inputs, exits) {
    let { deviceConfig, siteId, deviceId } = inputs;
    deviceConfig = globalHelpers.removeNullValueFromObject(deviceConfig);
    deviceConfig.isMainMeter = deviceConfig.isMainMeter === "true" ? true : false;

    inputSiteId = siteId;

    try {
      let { isMainMeter, slaveId } = deviceConfig;
      let device = await deviceService.findOne({ deviceId });
      let isEnergyMeter = false;

      if (deviceConfig.deviceType === 'em') {
          isEnergyMeter = true
      }

      if (!device) {
        return exits.badRequest({ problems: ["Device doesn't exist"] });
      }

      let { driverType, portNumber } = deviceConfig;
      let { siteId, controllerId, communicationType } = device;

      /**
       * In case of NMB slaveID will be zero
       */
      if (communicationType !== 'MB' && communicationType !== "MBIP" && portNumber !== device.portNumber) {
          deviceConfig.slaveId = '0'
          let {_deviceList} =  await deviceService.getDeviceConfiguration(siteId, isMainMeter, isEnergyMeter)

          /**
           * MB device should have duplicate recent port number
           * While NMB will have unique port number
           */
          const portNumberInUse = deviceUtils.isPortNumberInUse(_deviceList, portNumber, controllerId)
          if (portNumberInUse) {
              return exits.badRequest({
                  err: 'Port number already in use'
              })
          }
      }

      // Validate slave ID if provided
      if (slaveId && communicationType !== "MBIP") {
        let siteDevices = await self.find({ siteId });
        let { availableIndexes: availableSlaves, slaveIdToDeviceTypeMap } = await deviceService.getAvailableModbusSlaveIds(siteDevices, controllerId, siteId);
        let slaveSet = new Set(availableSlaves);

        if (!slaveSet.has(slaveId.toString())) {
          // Add validation for `vrfController` exception
          if (
            device.deviceType !== "vrfController" &&
            slaveIdToDeviceTypeMap[slaveId] !== "vrfController"
          ) {
            return exits.badRequest({ problems: ["Slave ID already in use"] });
          }
        }
      }

      // Update the main meter list if `isMainMeter` is provided
      if (isMainMeter) {
        let isMainMeterUpdateSuccess = await deviceService.updateMainMeterList(isMainMeter, deviceId, siteId);
        if (!isMainMeterUpdateSuccess) {
          return exits.badRequest({ problems: ["Cannot update main meter list."] });
        }
      }


      // Handle driver type updates
      if (driverType !== undefined && device.driverType !== driverType) {
        // validation for deviceType and driverType combination
        let _deviceType = await devicetypeservice.findOne({
          deviceType: device.deviceType,
          driverType,
        });

        if (!_deviceType) {
          return exits.badRequest({
            problems: ["Device type and driver type combination not found"],
          });
        }

        //delete all parameters of the device
        await parameterservice.destroyWithoutAbbr(siteId, deviceId);

        // Add or update parameters for the device
        let paramList = utils.addDeviceParameters(device.deviceId, _deviceType, siteId);
        await parameterservice.create(paramList);
      }

      // Remove restricted fields from the update payload
      delete deviceConfig.deviceId;
      delete deviceConfig.siteId;

      // Perform the device update
      let updatedDevice = await deviceService.getUpdatedDevices(deviceId, siteId, deviceConfig);
      if (!updatedDevice) {
        return exits.badRequest({ problems: ["Cannot update devices."] });
      }

      // Notify external systems about the update
      let eventData = {
        event: "update",
        data: updatedDevice,
      };

      await notifyJouleTrackPublicRoom(
          siteId,
          "devices",
          eventData
      );

      // Update configuration timestamp
      await dynamokeystoreservice.updateConfigTs(siteId);

      return exits.success({ message: `Device - ${deviceId} updated successfully.` });
    } catch (e) {
      sails.log.error("[Device >> edit-device]", e);

      switch (e.code) {
        case 'E_INVALID_MAIN_METER': {
          return exits.badRequest({err: e.message, code: e.code})
        }
        default:  {
          return exits.serverError(e);
        }
      }
    }
  }
};

