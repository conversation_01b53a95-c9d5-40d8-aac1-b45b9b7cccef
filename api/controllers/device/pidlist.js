const self = require('../../services/device/device.service');

const gloablhelpers = require('../../utils/globalhelper');

module.exports = {

  friendlyName: 'GetPidList',
  description: 'Get controllers that can run pid on a site asked for.',
  example: [
    `curl -X GET 0:1337/v2/config/controller/pid?siteId=demo`,
  ],
  oldroute : 'POST /pid/list',

  inputs: {
    siteId: {
      type: 'string',
      example: 'ssh(site Id to get Em of)',
      required: true
    }

  },

  exits: {
    notFoundSiteId: {
      description: 'Site Id doesnt exists.',
      responseType: 'notFound'
    }
  },

  fn: async function (inputs, exits) {
    let { siteId } = inputs;
    try {
      let devices = await self.find({ siteId });
      if (!devices) return exits.notFoundSiteId();
      let responseControllerList = [];

      devices.filter(device =>{
        if(!device.controllerId){
          if (
            !(
              device.pidOn &&
              device.pid &&
              gloablhelpers.toJson(device.pid)
            )
          ){
            responseControllerList.push(device);
          }
        }
      });

      return exits.success(responseControllerList);
    } catch (e) {
      sails.log.error(e);
      return this.res.serverError();
    }

  }
};

