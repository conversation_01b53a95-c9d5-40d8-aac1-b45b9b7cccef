const self = require('../../services/device/device.service');

const gloablhelpers = require('../../utils/globalhelper');

module.exports = {

  friendlyName: 'PublishControllerMode',
  description: 'Publish New modes(joulerecipe/jouletrack) to controller',
  example: [
    `curl -X POST -H 'Content-Type: application/json' --data "{'controllerId':'2766','siteId': 'mgch', 'did' : {'2217.stop': 'jouletrack', '2217.start':'joulerecipe'}}" 0:1337/config/v2/publishmode`,
  ],
  oldroute: 'POST /devices/setMode',

  inputs: {
    siteId: {
      type: 'string',
      example: 'ssh(site Id to get Em of)',
      required: true
    },
    controllerId: {
      type: 'string',
      example: '2766',
      required: true
    },
    did: {
      type: {},
      example: { '2217.stop': 'jouletrack', '2217.start': 'joulerecipe' },
      required: true
    }

  },

  exits: {
    invaliddid: {
      description: 'Site Id doesnt exists.',
      responseType: 'notFound'
    }
  },

  fn: async function (inputs, exits) {
    let { siteId, controllerId } = inputs;
    let deviceIdsDotparam = inputs.did;
    try {
      for (let deviceIdDotParam in deviceIdsDotparam) {
        if (!(deviceIdDotParam.split('.').length === 2)) {
          return exits.invaliddid();
        }
      }
      let publishObject = {
        operation: 'InsertInModes',
        extra: { controllerId, siteId },
        config: {
          ...deviceIdsDotparam,
        },
      };
      let publishTopic = `${siteId}/config/${controllerId}/mode`;
      eventService.publish(publishTopic, JSON.stringify(publishObject));
      return exits.success('ok');

    } catch (e) {
      sails.log.error(e);
      return this.res.serverError();
    }

  }
};

