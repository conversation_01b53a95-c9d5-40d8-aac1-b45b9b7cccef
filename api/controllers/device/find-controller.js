const deviceservice = require('../../services/device/device.service');
const utils = require('../../utils/device/utils');

const selfUtils = require('../../utils/device/find-controller.utils');
const globalHelpers = require('../../utils/globalhelper');


const componentservice = require('../../services/component/component.public');
const dynamoservice = require('../../services/dynamokeystore/dynamokeystore.public');
const parameterservice = require('../../services/parameter/parameter.public');
const siteservice = require('../../services/site/site.service');


module.exports = {

  friendlyName: 'Get controller config',
  description: 'Get configration of device attached to a controller',
  example: [
    `curl 0:1337/config/v2/controller?cntrlId=3323`,
  ],
  oldroute: '/v1.5/devices/configureController',
  inputs: {
    cntrlId: {
      type: 'string',
      example: '2771(controller id to get configuration of)',
      required: true
    }
  },

  exits: {
    invalidControllerId: {
      responseType: 'badRequest',
      description: 'Controller not configured.',
    },
    invalidControllerType: {
      responseType: 'badRequest',
      description: 'Unknown Controller type.',
    },
  },

  fn: async function (inputs, exits) {
    let { cntrlId } = inputs;
    try {
      let devConfig = {};
      let device = await deviceservice.findOne({ 'deviceId': cntrlId });
      if (!device) {
        return exits.invalidControllerId();
      }

      let { deviceType, siteId } = device;
      if (!utils.isValidControllerType(deviceType)) {
        return exits.invalidControllerType();
      }
      let [components, devices, timestamp, site] = await Promise.all([
        componentservice.find({ siteId }),
        deviceservice.find({ siteId }),
        dynamoservice.getConfigTs(device.siteId),
        siteservice.findOne({ siteId })
      ]);

      if (!timestamp) {
        timestamp = globalHelpers.getCurrentUnixTs();
        await dynamoservice.updateConfigTs(siteId, timestamp);
      }

      let devicePortConfigMap = selfUtils.getControllersFeedbackPortConfig(components);
      let deviceIdParamMap = await parameterservice.getFilteredParams(siteId, devicePortConfigMap);
      let linkedDevices = selfUtils.filterNonLinkedController(devices, cntrlId);

      linkedDevices.reduce((acc, device, index) => {
        let { deviceId } = device;
        if (deviceIdParamMap[deviceId]) device['param'] = deviceIdParamMap[deviceId];
        acc[deviceId] = device;
        return acc;
      }, devConfig);

      return exits.success({
        schema: {
          components,
          devices,
          self: device,
          timestamp,
          site
        },
        devConfig
      });
    } catch (e) {
      sails.log.error(e);
      return this.res.serverError();
    }

  }
};
