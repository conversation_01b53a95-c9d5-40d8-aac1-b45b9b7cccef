const self = require('../../services/device/device.service');
const componentservice = require('../../services/component/component.public');

module.exports = {

  friendlyName: 'GetPidConfig',
  description: 'Get config of pid on controller asked for.',
  example: [
    `curl -X GET 0:1337/v2/config/pid?componentId=frtn_15`,
  ],
  oldroute : 'GET /pid/getConfig',

  inputs: {
    componentId: {
      type: 'string',
      example: 'ssh(site Id to get Em of)',
      required: true
    }

  },

  exits: {
    notFoundControllerId: {
      description: 'Component doest exits.',
      responseType: 'notFound'
    },
    deviceHaveBeenDeleted: {
      description : 'Componenet have attached device which have been deleted from device table',
      responseType: 'notFound'
    },
    pidNotAttached : {
      description : 'Componenet dont have pid attached',
      responseType: 'notFound'
    }
  },

  fn: async function (inputs, exits) {
    let { componentId } = inputs;
    try {
      let component = componentservice.findOne({deviceId: componentId});

      if(!component){
        return exits.notFoundControllerId();
      }
      if(component && component.pinOn){
        let device = await self.findOne({deviceId: component.pinOn});
        if (device){
          return exits.success(device);
        }else{
          return exits.deviceHaveBeenDeleted();
        }
      }else{
        return exits.pidNotAttached();
      }
    } catch (e) {
      sails.log.error(e);
      return this.res.serverError();
    }

  }
};

