const  ShiftService = require('../../services/shift/shift.service')
module.exports = {
  friendlyName: "addShift",
  description: "to add new shift",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
      description:
        "User meta information added by default to authenticated routes",
    },
    shift_start_time:{
      type:"string",
      description:"",
      required:true
    },
    shift_end_time:{
      type:"string",
      description:"",
      required:true
    },
  },
  exits: {
    serverError: {
      responseType: "serverError",
      description:
        "[shift > addShift] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description:
        "[shift > addShift] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description:
        "[shift > addShift] forbidden Request!",
    },
    duplicateShift:{
      statusCode:400,
      description: ""
    }
  },
  fn: async function creatShift(inputs, exits) {
    try {
      const { _userMeta, shift_start_time, shift_end_time  } = inputs
      const _shift = await ShiftService.create({
        siteId: _userMeta._site,
        shift_start_time: shift_start_time,
        shift_end_time: shift_end_time,
        created_by:_userMeta.id
      })
      if(_shift) return exits.success(_shift);
      else return exits.duplicateShift({problems:[`DUPLUCATE_SHIFT`]})
    } catch (err) {
      return exits.serverError(err);
    }
  },
};
