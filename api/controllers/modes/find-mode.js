const modesService = require('../../services/mode/mode.service');

module.exports = {
  friendlyName: 'findModes',
  description: 'This API is used get modes of did (deviceId.param) between start time and end time.',
  example: ['curl "http://localhost:1337/m2/modes"'],

  inputs: {
    did: {
      type: 'string',
      description: 'device id and parameter of the mode.',
      required: true,
      example: '3028.setfrequency',
    },
    startTime: {
      type: 'string',
      description: 'start time for mode data in unix',
      required: true,
      example: '1621708085000',
    },
    endTime: {
      type: 'string',
      description: 'end time for mode data in unix',
      required: false,
      example: '1624686188000',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[mode > findMode] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[mode > findMode] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[mode > findMode] unauthorized!',
    },
  },
  async fn(inputs, exits) {
    try {
      const { did, startTime, endTime } = inputs;
      let data;
      if (!endTime) {
        data = await modesService.getLastModeOfDeviceParam(did, startTime);
      } else {
        data = await modesService.getModesBetweenTwoTimeStamp(did, startTime, endTime);
      }

      if (data && data.problems) {
        return exits.badRequest(data);
      }
      return exits.success(data);
    } catch (error) {
      sails.log.error('[mode > findMode] Error!');
      sails.log.error(error);
      return exits.serverError(error);
    }
  },

};
