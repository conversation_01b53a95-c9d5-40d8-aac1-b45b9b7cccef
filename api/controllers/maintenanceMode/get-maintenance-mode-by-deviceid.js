const maintenanceModeService = require("../../services/maintenanceMode/maintenanceMode.service");
const maintenanceModesAuditService = require("../../services/MaintenanceModesAudit/MaintenanceModesAudit.service");

module.exports = {
  friendlyName: "getMaintenanceModeByDeviceId",
  description: "Fetches the Maintenance mode flag of a component",
  example: [],
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
      description: "User meta information added by default to authenticated routes",
    },
    deviceId: {
      type: "string",
      required: true,
      example: "mgch_1",
    },
    deviceClass: {
      type: "string",
      required: true,
      example: "component or device",
    },
    siteId: {
      type: "string",
      required: true,
      example: "aph-ahm",
    },
  },
  exits: {
    serverError: {
      responseType: "serverError",
      description: "[maintenanceMode > get-maintenance-mode-by-deviceid] Server Error!",
    },
    badRequest: {
      statusCode: 400,
      responseType: "badRequest",
      description: "[maintenanceMode > get-maintenance-mode-by-deviceid] Bad Request!",
    },
    forbidden: {
      statusCode: 403,
      responseType: "forbidden",
      description: "[maintenanceMode > get-maintenance-mode-by-deviceid] forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[maintenanceMode > get-maintenance-mode-by-deviceid] Not Found",
    },
    success: {
      statusCode: 200,
      description:
        "[maintenanceMode > get-maintenance-mode-by-deviceid] Maintenance Mode fetched successfully",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { deviceId, deviceClass, siteId } = inputs;
      const validDeviceClass = ["component", "device"];
      if (!validDeviceClass.includes(deviceClass)) {
        return exits.badRequest({ err: `Invalid deviceClass for :${deviceId}` });
      }
      const asset = [
        {
          deviceId,
          deviceClass
        }
      ];
      const hasAccess = await maintenanceModeService.checkUserAccessForDeviceList(asset, siteId);
      if (!hasAccess) {
        return exits.notFound({
          err: "User doesn't have access to fetch Maintenance Mode of given device.",
        });
      }
      if (deviceClass === "component") {
        const response = await maintenanceModeService.getMaintenanceModeByComponent(deviceId);
        const correctedResponse = await _getMaintenanceModeAuditLogAndReturn(deviceId, response);
        exits.success(correctedResponse);
      } else if (deviceClass === "device") {
        const response = await maintenanceModeService.getMaintenanceModeByDevice(deviceId);
        const correctedResponse = await _getMaintenanceModeAuditLogAndReturn(deviceId, response);
        exits.success(correctedResponse);
      }
    } catch (error) {
      sails.log.error("[maintenanceMode > get-maintenance-mode-by-deviceId] Error!");
      sails.log.error(error);

      switch (error.code) {
        case "ASSET_NOT_EXIST":
          return exits.notFound({ err: error.message });
        case "AUDIT_LOG_NOT_FOUND":
          return exits.badRequest({ err: error.message });
        case "INVALID_VALUE":
          return exits.badRequest({ err: error.message });
        default:
          return exits.serverError(error);
      }
    }
  },
};

async function _getMaintenanceModeAuditLogAndReturn(deviceId, response) {
  const auditLogResponse = await maintenanceModesAuditService.getLatestMaintenanceModeAuditLog(
    deviceId
  );
  if (!_.isEmpty(auditLogResponse)) {
    return { id: auditLogResponse.id, ...response };
  } else {
    return response;
  }
}
