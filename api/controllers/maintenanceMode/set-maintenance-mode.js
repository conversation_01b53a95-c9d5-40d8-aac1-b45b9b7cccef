const maintenanceModeService = require("../../services/maintenanceMode/maintenanceMode.service");
const socketService = require('../../services/socket/socket.public');

module.exports = {
  friendlyName: "setMaintenanceMode",
  description: "Changes the Maintenance mode flag of a component",
  example: [],
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    deviceId: {
      type: 'string',
      required: true,
      example: 'mgch_1',
    },
    deviceClass: {
      type: 'string',
      required: true,
      example: 'component or device',
    },
    siteId: {
      type: 'string',
      required: true,
      example: 'mgch',
    },
    maintenanceModeValue: {
      type: 'number',
      required: true,
      isIn: [1, 0],
      description: '1 => Enabling maintenance mode of a component, 0 => Disabling maintenance mode of a component'
    }
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[maintenanceMode > set-maintenance-mode] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[maintenanceMode > set-maintenance-mode] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[maintenanceMode > set-maintenance-mode] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: "[maintenanceMode > set-maintenance-mode] Not Found",
    },
    success: {
      statusCode: 200,
      description:
        "[maintenanceMode > set-maintenance-mode] Maintenance Mode changed successfully",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { deviceId, deviceClass, siteId, maintenanceModeValue, _userMeta: { id: userId }, } = inputs
      const validDeviceClass = ["component", "device"];
      if (!validDeviceClass.includes(deviceClass)) {
        return exits.badRequest({ err: `Invalid deviceClass for :${deviceId}` });
      }
      let response;
      if (deviceClass === "component") {
        response = await maintenanceModeService.setComponentMaintenanceMode(deviceId, siteId, maintenanceModeValue, deviceClass, userId)
      }
      else if (deviceClass === "device") {
        response = await maintenanceModeService.setDeviceMaintenanceMode(deviceId, siteId, maintenanceModeValue, deviceClass, userId)
      }
      response.isInMaintenanceMode = maintenanceModeValue;
      try {
        await socketService.notifyJouleTrackPublicRoom(
          siteId,
          deviceClass === "component" ? "components" : "devices",
          {
            event: "update-maintenance-mode",
            data: response,
          }
        );
      } catch (notificationError) {
        sails.log.error('[maintenanceMode > set-maintenance-mode] Failed to notify JouleTrack Public Room');
        sails.log.error(notificationError);
      }
      await maintenanceModeService.ingestMaintenanceModeLogToInflux({siteId, assetId: deviceId, userId, maintenanceModeValue, transactionId:this.req.headers['x-transaction-id'] });
      return exits.success(response);
    } catch (error) {
      sails.log.error('[maintenanceMode > set-maintenance-mode] Error!');
      sails.log.error(error);

      switch (error.code) {
        case "ASSET_NOT_EXIST":
          return exits.notFound({ err: error.message });
        case "AUDIT_LOG_NOT_FOUND":
          return exits.badRequest({ err: error.message });
        case "INVALID_VALUE":
          return exits.badRequest({ err: error.message });
        case "ERROR_FOUND":
          return exits.badRequest({ err: error.message });
        default:
          return exits.serverError(error);
      }
    }
  },
};
