const maintenanceAuditService = require("../../services/MaintenanceModesAudit/MaintenanceModesAudit.service");
const {
  validateMaintenanceModeHistoryInputsByAsset,
} = require("../../utils/maintenanceMode/requestValidator.util");
const moment = require("moment");

module.exports = {
  friendlyName: "fetchMaintenanceModeHistory",
  description: "Fetches the maintenance mode history of a component or device",
  example: [],
  inputs: {
    assetId: {
      type: "string",
      required: true,
      example: "aph-ahm_9",
    },
    siteId: {
      type: "string",
      required: true,
      example: "aph-ahm",
    },
    startTime: {
      type: "string",
      required: true,
      example: "2024-07-12 02:46:57",
    },
    endTime: {
      type: "string",
      required: true,
      example: "2024-07-25 02:46:57",
    },
  },
  exits: {
    serverError: {
      responseType: "serverError",
      description: "[maintenanceMode > fetch-maintenance-mode-history] Server Error!",
    },
    badRequest: {
      statusCode: 400,
      responseType: "badRequest",
      description: "[maintenanceMode > fetch-maintenance-mode-history] Bad Request!",
    },
    forbidden: {
      statusCode: 403,
      responseType: "forbidden",
      description: "[maintenanceMode > fetch-maintenance-mode-history] Forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[maintenanceMode > fetch-maintenance-mode-history] Not Found",
    },
    success: {
      statusCode: 200,
      description:
        "[maintenanceMode > fetch-maintenance-mode-history] Maintenance Mode history fetched successfully",
    },
  },
  fn: async function (inputs, exits) {
    try {
      validateMaintenanceModeHistoryInputsByAsset(inputs);
      let { startTime, endTime, siteId, assetId } = inputs;

      /**If startTime is invalid, return an empty array*/
      if (!moment(startTime).isValid()) {
        return exits.success([]);
      }

      /**If endTime is invalid, set it to the current time*/
      if (!moment(endTime).isValid()) {
        endTime = moment().format("YYYY-MM-DD HH:mm:ss");
      }

      /**Check if startTime is not greater than endTime*/
      if (moment(startTime).isAfter(moment(endTime))) {
        return exits.badRequest({
          err: "Start Time cannot be greater than End Time.",
        });
      }

      const { formattedStartTime, formattedEndTime } = await _getFormattedTime(
        siteId,
        startTime,
        endTime,
      );

      const maintenanceModeAuditHistory =
        await maintenanceAuditService.getMaintenanceModeAuditHistory(
          assetId,
          siteId,
          formattedStartTime,
          formattedEndTime,
        );
      return exits.success(maintenanceModeAuditHistory);
    } catch (error) {
      sails.log.error("maintenanceMode > fetch-maintenance-mode-history");
      sails.log.error(error);
      switch (error.code) {
        case "E_INVALID_DEVICE_ID":
          return exits.badRequest({ err: error.message });
        case "E_INPUT_VALIDATION":
          return exits.badRequest({ err: error.message });
        case "E_SITE_NOT_FOUND": {
          return exits.notFound({
            err: error.message,
          });
        }
        default:
          return exits.serverError({
            err: "Server has encountered an issue. Please contact admin to resolve this issue.",
          });
      }
    }
  },
};

/**
 * @description Formats the start and end times based on the site's timezone.
 * @param {string} siteId - The ID of the site for which the timezone offset is to be fetched.
 * @param {string} startTime - The start time to be formatted, in ISO 8601 format (e.g., "2024-07-18T01:04:23.690+05:30").
 * @param {string} endTime - The end time to be formatted, in ISO 8601 format (e.g., "2024-07-25T01:04:23.690+05:30").
 * @returns {Promise<{formattedStartTime: string, formattedEndTime: string}>} A promise that resolves to an object containing:
 * - {string} formattedStartTime - The formatted start time in ISO 8601 format with timezone offset.
 * - {string} formattedEndTime - The formatted end time in ISO 8601 format with timezone offset.
 */
async function _getFormattedTime(siteId, startTime, endTime) {
  const timezoneOffset = await sails.helpers.getSiteTimezone.with({
    siteId,
    timezoneFormat: "Z",
  });
  const formattedStartTime = moment(startTime)
    .utcOffset(timezoneOffset)
    .format("YYYY-MM-DDTHH:mm:ss.SSSZ");
  const formattedEndTime = moment(endTime)
    .utcOffset(timezoneOffset)
    .format("YYYY-MM-DDTHH:mm:ss.SSSZ");

  return {
    formattedStartTime,
    formattedEndTime,
  };
}
