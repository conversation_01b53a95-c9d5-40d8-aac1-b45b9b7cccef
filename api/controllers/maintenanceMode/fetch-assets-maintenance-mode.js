const maintenanceModeService = require("../../services/maintenanceMode/maintenanceMode.service");
const {
  validateFetchMaintenanceModeDetailsByAssets,
} = require("../../utils/maintenanceMode/requestValidator.util");

module.exports = {
  friendlyName: "Get the maintenance mode by Asset List",

  description: "Get the maintenance mode for the list of components/devices",

  inputs: {
    assetList: {
      type: "json",
      required: true,
      example: [{ deviceId: "suh-hyd_9", deviceClass: "component" }],
    },
    siteId: {
      type: "string",
      required: true,
      example: "aph-ahm",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[maintenanceMode > fetch-assets-maintenance-mode] Server Error!",
    },
    badRequest: {
      statusCode: 400,
      responseType: "badRequest",
      description: "[maintenanceMode > fetch-assets-maintenance-mode] Bad Request!",
    },
    forbidden: {
      statusCode: 403,
      responseType: "forbidden",
      description: "[maintenanceMode > fetch-assets-maintenance-mode] Forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[maintenanceMode > fetch-assets-maintenance-mode] Not Found",
    },
    success: {
      statusCode: 200,
      description:
        "[maintenanceMode > fetch-assets-maintenance-mode] Table group fetched successfully",
    },
  },

  fn: async function (inputs, exits) {
    try {
      await validateFetchMaintenanceModeDetailsByAssets(inputs);
      const { assetList = [], siteId } = inputs;
      if (!assetList || _.isEmpty(assetList)) {
        return exits.badRequest({ message: "No componentId/deviceId is provided" });
      }
      const hasAccess = await maintenanceModeService.checkUserAccessForDeviceList(assetList, siteId);
      if (!hasAccess) {
        return exits.notFound({
          err: "User doesn't have access to fetch Maintenance Mode of given devices.",
        });
      }
      const maintenanceModeDetailsByAssetList =
        await maintenanceModeService.getMaintenanceModeByAssets(assetList);
      return exits.success(maintenanceModeDetailsByAssetList);
    } catch (err) {
      sails.log.error("maintenanceMode > fetch-assets-maintenance-mode");
      sails.log.error(err);
      switch (err.code) {
        case "ASSET_NOT_EXIST":
          return exits.notFound({ err: err.message });
        case "E_INPUT_VALIDATION":
          return exits.badRequest({ err: err.message });
        default:
          return exits.serverError(err);
      }
    }
  },
};
