const actionService = require('../../services/action/action.service');
const globalHelper = require('../../utils/globalhelper');
const selfUtils = require('../../utils/action/execute-command.util.js');
const utils = require('../../utils/action/utils.js');

const modeService = require('../../services/mode/mode.service');

module.exports = {
  friendlyName: 'executeCommand',
  description:
    'Send a command from joule track(Front end) to IOT controller on specified device',
  example: [`curl -X GET "http://localhost:1337/`],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    },
    command: {
      type: 'json',
      required: true,
      example: {
        value: 0,
        deviceId: '2222',
        param: 'setFrequency',
        componentId: 'ssh_16',
      },
      custom: selfUtils.isCommandValid,
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[action > executeCommand] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[action > executeCommand] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[action > executeCommand] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try {
      let {
        _userMeta: { _site: siteId },
        command: { deviceId, param, componentId, value },
      } = inputs;
      let actionPacket, currentDeviceMode;

      currentDeviceMode = modeService.getLastKnownModeOfDeviceParam(
        deviceId,
        param
      );
      if (currentDeviceMode !== 'jouletrack') {
        return exits.forbidden({
          problems: ['Device param not in valid mode'],
        });
      }
      actionPacket = selfUtils.createCommanActionPacket(command);

      return exits.success({ status: 'Command Execution In process' });
    } catch (error) {
      sails.log.error('[action > executeCommand] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  },
};
