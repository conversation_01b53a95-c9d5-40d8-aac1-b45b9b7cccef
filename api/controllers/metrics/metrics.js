const PrometheusService = require('../../services/PrometheusService');
const client = require('prom-client');
module.exports = {
    fn: async function () {
        try {
            const metrics = await client.register.metrics();
            this.res.set('Content-Type', client.register.contentType);
            this.res.send(metrics);
        } catch (error) {
            res.status(500).send(error.message);
        }
    }
};
