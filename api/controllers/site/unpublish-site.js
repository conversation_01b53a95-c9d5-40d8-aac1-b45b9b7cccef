const siteService = require('../../services/site/site.service');
const { throwExceptionInvalidSiteList } = require('../../utils/site/errorHandler.util');
module.exports = {
  friendlyName: 'delete-site',
  description: 'Soft delete specific site',
  example: [
    `curl -X DEL "http://localhost:1337/site/:siteId`,
  ],

  inputs: {
   siteList: {
    type: 'ref',
    example: []
   }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[site > deleteSite] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[site > deleteSite] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[site > deleteSite] forbidden Request!',
    },
  },

  fn: async function ({siteList}, exits) {
    try {
      throwExceptionInvalidSiteList({siteList})
       const $deleteSite = siteList.map((siteId)=> siteService.deleteSite(siteId));
       await Promise.all($deleteSite);
      return exits.success({
        message: `Sites ${siteList.join(',')} has been successfully deleted`
      });
    } catch (error) {
      if (error.code == 'INPUT_VALIDATION_ERROR') {
        return exits.badRequest({err: error.message})
      } else {
        sails.log.error('[site > deleteSite] Error!');
        sails.log.error(error);
        return exits.serverError(error);
      }
    }
  }
};
