module.exports = {
  friendlyName: 'Update graph title',

  description: 'Update the title of a configurator graph based on provided graph ID',

  inputs: {
    graphId: {
      type: 'number',
      required: true
    },
    name: {
      type: 'string',
      required: true
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[configuratorGraph > update-graph-title] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[configuratorGraph > update-graph-title] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[configuratorGraph > update-graph-title] Forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[configuratorGraph > update-graph-title] Not Found',
    },
    success: {
      statusCode: 200,
      description: '[configuratorGraph > update-graph-title] Graph title updated successfully',
    },
  },

  fn: async function ({
    graphId,
    name
  }, exits) {
    try {
      if (!name) {
        return exits.badRequest({ err: 'Invalid input: name is required.' });
      }

      const graph = await ConfiguratorGraphs.findOne({ id: graphId });
      if (!graph) {
        return exits.notFound({ err: 'Graph not found.' });
      }

      const updatedGraph = await ConfiguratorGraphs.updateOne({ id: graphId })
        .set({ name });

      if (!updatedGraph) {
        return exits.badRequest({ err: 'Failed to update graph title.' });
      }

      return exits.success({
        status: 'Success',
        message: 'Graph title updated successfully',
        data: updatedGraph
      });

    } catch (err) {
      sails.log.error(err);
      return exits.serverError(err);
    }
  },
};
