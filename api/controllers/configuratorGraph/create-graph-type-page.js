const configuratorPageService = require('../../services/configuratorPage/configuratorPage.service');
const graphUtils = require('../../utils/configuratorGraph/graphUtils');
module.exports = {
  friendlyName: 'Create configurator graph by type',
  description: 'Create configurator graph based on selected type',
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    },
    pageId: {
      type: 'string',
      required: true
    },
    type: {
      type: 'number',
      required: true
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[configuratorGraph > create-graph-type-page] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[configuratorGraph > create-graph-type-page] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[configuratorGraph > create-graph-type-page] Forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[configuratorGraph > create-graph-type-page] Not Found',
    },
    success: {
      statusCode: 201,
      description:
        '[configuratorGraph > create-graph-type-page] Graph created successfully',
    },
  },

  fn: async function ({
    pageId,
    type,
    _userMeta: { id: userId }
  }, exits) {
    try {
      const graphType = graphUtils.graphTypeMap[type];
      if (!graphType) {
        return exits.badRequest({ err: 'Invalid Graph Type, Please provide a valid graph type' });
      }
      await configuratorPageService.validatePage(pageId);

      const response =
        await ConfiguratorGraphs
          .create({
            name: 'Untitled',
            subsystemPageId: pageId,
            type: type,
            createdBy:userId
          })
          .fetch();
      return exits.success({
        status: 'Success',
        message: `${graphType} Graph created successfully`,
        data: response
      });

    } catch (err) {
      switch (err.code) {
        case 'E_INVALID_PAGE':
          return exits.badRequest({
            err: err.message
          });
        default:
          sails.log.error(err);
          return exits.serverError(err);
      }
    }
  },
};
