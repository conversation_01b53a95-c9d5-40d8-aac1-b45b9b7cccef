const ConfiguratorGraphFactory = require("../../services/configuratorGraph/ConfiguratorGraphFactory");
const configuratorPage = require("../../services/configuratorPage/configuratorPage.public");
module.exports = {
  friendlyName: "Save configurator graph",
  description: "save configurator graph",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: {
        id: "userId",
        _role: "role",
        _site: "siteId",
      },
    },
    pageId: {
      type: "string",
    },
    graphProperty: {
      type: "ref",
    },
    graphId: {
      type: "string",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[configuratorGraph > save-configurator-graph] Server Error!",
    },
    badRequest: {
      statusCode: 400,
      responseType: "badRequest",
      description: "[configuratorGraph > save-configurator-graph] Bad Request!",
    },
    forbidden: {
      statusCode: 403,
      responseType: "forbidden",
      description: "[configuratorGraph > save-configurator-graph] forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[configuratorGraph > save-configurator-graph] Not Found",
    },
    success: {
      statusCode: 200,
      description: "[configuratorGraph > save-configurator-graph] Table group created successfully",
    },
  },

  fn: async function ({ pageId, graphProperty, graphId, _userMeta }, exits) {
    try {
      if (!Number(pageId)) return exits.badRequest({ err: "Please provide valid pageId" });
      if (!Number(graphId)) return exits.badRequest({ err: "Please provide valid graphId" });
      if (!graphProperty) return exits.badRequest({ err: "Please provide graph property." });
      const graphRecord = await ConfiguratorGraphs.findOne({
        subsystemPageId: pageId,
        id: graphId,
        status: 1,
      });
      if (!graphRecord) return exits.badRequest({ err: "Graph does not exist" });
      const isPagePublished = await configuratorPage.isPagePublished(pageId);
      if (isPagePublished) {
        return exits.badRequest({ err: "Please draft the page for creating new graph." });
      }

      const siteId = await configuratorPage.getSiteIdFromPage(pageId);
      const graph = new ConfiguratorGraphFactory({
        pageId,
        graphId,
        graphProperty,
        type: graphRecord.type,
        userId: _userMeta.id,
      });
      graph.setSiteId(siteId);
      await graph.validateGraphProperty();
      await graph.save();
      return exits.success({
        status: "Success",
        message: "Graph property saved successfully",
      });
    } catch (err) {
      switch (err.statusCode) {
        case 400:
          return exits.badRequest({ err: err.msg });
        default:
          sails.log.error(err);
          return exits.serverError(err);
      }
    }
  },
};
