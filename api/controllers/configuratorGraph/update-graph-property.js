module.exports = {
  friendlyName: 'Update graph title',
  description: 'Update the title of a configurator graph based on provided graph ID',
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
    },
    graphId: {
      type: 'number',
      required: true
    },
    name: {
      type: 'string',
      required: true
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[configuratorGraph > update-graph-property] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[configuratorGraph > update-graph-property] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[configuratorGraph > update-graph-property] Forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[configuratorGraph > update-graph-property] Not Found',
    },
    success: {
      statusCode: 200,
      description: '[configuratorGraph > update-graph-property] Graph title updated successfully',
    },
  },

  fn: async function (inputs, exits) {
    try {
      const {
        _userMeta: { id: userId },
        ..._updateGraphPropertyObject
      } = inputs;
      /**Validate if all input keys are allowed*/
      const allowedKeys = ['name', 'graphId'];
      const allKeysAreAllowed = Object.keys(_updateGraphPropertyObject)
        .every(key => allowedKeys.includes(key));

      if (!allKeysAreAllowed) {
        return exits.badRequest({ err: 'Invalid input: contains invalid keys.' });
      }
      const { name, graphId } = _updateGraphPropertyObject
      if (!name) {
        return exits.badRequest({ err: 'Invalid input: name is required.' });
      }

      const graph = await ConfiguratorGraphs.findOne({ id: graphId });
      if (!graph) {
        return exits.notFound({ err: 'Graph not found.' });
      }

      const updatedGraph = await ConfiguratorGraphs.updateOne({ id: graphId })
        .set({ name, lastUpdatedBy: userId});

      if (!updatedGraph) {
        return exits.badRequest({ err: 'Failed to update graph title.' });
      }

      return exits.success({
        status: 'Success',
        message: 'Graph title updated successfully',
        data: updatedGraph
      });

    } catch (err) {
      sails.log.error(err);
      return exits.serverError(err);
    }
  },
};
