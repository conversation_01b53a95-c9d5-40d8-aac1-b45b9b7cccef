name: CICD ON Beta ENV 
on:
  push:
    branches: [mqtt-microservice-appily]
  pull_request:
    branches: [mqtt-microservice-appily]
jobs:
  build-image-push-ECR:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      - name: Build and push jt_api_v2
        env: 
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }} 
          TAGID: ${{ github.run_number }}
          IMAGE_REPOSITORY: smartjules-beta  
        run: |
          docker build -t $ECR_REGISTRY/$IMAGE_REPOSITORY:jt_api_v2_latest .
          echo "pushing image to ECR"
          docker push $ECR_REGISTRY/$IMAGE_REPOSITORY:jt_api_v2_latest
      - name: deploy on dockerhost ec2 instance with docker-compose
        env:
          PRIVATE_KEY: ${{ secrets.BETA_SSH_KEY }}
          HOST_IP: ************
          USER_NAME: ubuntu 
        run: |
          echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
          ssh -o StrictHostKeyChecking=no -i private_key ${USER_NAME}@${HOST_IP} << 'EOL'
          docker-compose down
          rm -rf docker-compose.yml
          docker rmi 878252606197.dkr.ecr.us-west-2.amazonaws.com/smartjules-beta:jt_api_v2_latest
          EOL
          scp -o StrictHostKeyChecking=no -i private_key docker-compose.yml ${USER_NAME}@${HOST_IP}:/home/<USER>/
          ssh -o StrictHostKeyChecking=no -i private_key ${USER_NAME}@${HOST_IP} 'docker-compose up -d'         
